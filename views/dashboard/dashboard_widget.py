#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据看板主组件
包含数据卡片、图表和实时数据表格
"""

import sys
import os
import random
from datetime import datetime, timedelta
from decimal import Decimal

from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QLabel, QFrame, 
    QTableWidget, QTableWidgetItem, QHeaderView
)
from PyQt6.QtCore import Qt, QTimer
from PyQt6.QtGui import QFont, QColor, QPainter
from PyQt6.QtCharts import (
    QChart, QChartView, QPieSeries, QBarSeries, QBarSet, 
    QLineSeries, QValueAxis, QBarCategoryAxis, QPieSlice
)

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from config.database import get_session
from models.product import Product, Category
from models.inventory import Inventory
from models.warehouse import Location
from sqlalchemy import func

class DashboardWidget(QWidget):
    """数据看板主组件"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.init_ui()
        self.set_styles()
        self.start_data_timers()
        
    def init_ui(self):
        """初始化界面"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(5, 5, 5, 5)
        layout.setSpacing(20)

        
        # 创建数据卡片行
        self.create_data_cards(layout)
        
        # 创建图表行
        self.create_charts(layout)
        
        # 创建实时数据表格
        self.create_realtime_data_table(layout)
        
    def create_data_cards(self, parent_layout):
        """创建数据卡片行"""
        # 数据卡片容器
        cards_widget = QWidget()
        cards_layout = QHBoxLayout(cards_widget)
        cards_layout.setContentsMargins(5, 5, 5, 5)
        cards_layout.setSpacing(15)
        
        # 创建四个数据卡片 - 初始显示为0
        self.total_inventory_card = self.create_data_card("总库存量", "0", "件", "#4CAF50")
        self.total_value_card = self.create_data_card("库存总价值", "¥ 0.00", "", "#2196F3")
        self.low_stock_card = self.create_data_card("低库存商品", "0", "种", "#FF9800")
        self.out_of_stock_card = self.create_data_card("缺货商品", "0", "种", "#F44336")
        
        # 添加到布局
        cards_layout.addWidget(self.total_inventory_card)
        cards_layout.addWidget(self.total_value_card)
        cards_layout.addWidget(self.low_stock_card)
        cards_layout.addWidget(self.out_of_stock_card)
        
        # 添加到父布局
        parent_layout.addWidget(cards_widget)
        
    def create_data_card(self, title, value, unit, color):
        """创建单个数据卡片"""
        # 卡片容器
        card = QFrame()
        card.setObjectName("dataCard")
        card.setProperty("cardColor", color)
        card.setStyleSheet(f"#dataCard[cardColor='{color}'] {{ border-top: 4px solid {color}; }}")
        
        # 卡片布局
        card_layout = QVBoxLayout(card)
        card_layout.setContentsMargins(20, 15, 20, 15)
        card_layout.setSpacing(5)
        
        # 卡片标题
        title_label = QLabel(title)
        title_label.setObjectName("cardTitle")
        
        # 卡片数值
        value_layout = QHBoxLayout()
        value_layout.setContentsMargins(0, 0, 0, 0)
        value_layout.setSpacing(5)
        
        value_label = QLabel(value)
        value_label.setObjectName("cardValue")
        value_label.setProperty("valueColor", color)
        value_label.setStyleSheet(f"#cardValue[valueColor='{color}'] {{ color: {color}; }}")
        
        unit_label = QLabel(unit)
        unit_label.setObjectName("cardUnit")
        
        value_layout.addWidget(value_label)
        value_layout.addWidget(unit_label)
        value_layout.addStretch()
        
        # 添加到卡片布局
        card_layout.addWidget(title_label)
        
        # 创建一个容器来包含value_layout
        value_container = QWidget()
        value_container.setLayout(value_layout)
        card_layout.addWidget(value_container)
        
        return card
        
    def create_charts(self, parent_layout):
        """创建图表行"""
        # 图表容器
        charts_widget = QWidget()
        charts_layout = QHBoxLayout(charts_widget)
        charts_layout.setContentsMargins(5, 5, 5, 5)
        charts_layout.setSpacing(15)
        
        # 创建三个图表
        self.create_inventory_pie_chart(charts_layout)
        self.create_monthly_bar_chart(charts_layout)
        self.create_trend_line_chart(charts_layout)
        
        # 添加到父布局
        parent_layout.addWidget(charts_widget)
    
    def create_chart_placeholder(self, parent_layout, title):
        """创建图表占位符"""
        placeholder = QFrame()
        placeholder.setObjectName("chartPlaceholder")
        placeholder.setStyleSheet("""
            #chartPlaceholder {
                background: white;
                border: 1px solid #E0E0E0;
                border-radius: 8px;
                margin: 5px;
                min-height: 300px;
            }
        """)
        
        layout = QVBoxLayout(placeholder)
        layout.setContentsMargins(20, 20, 20, 20)
        
        # 标题
        title_label = QLabel(title)
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        title_label.setStyleSheet("font-size: 16px; font-weight: bold; color: #333; margin-bottom: 20px;")
        layout.addWidget(title_label)
        
        # 占位内容
        content_label = QLabel("📊\n图表功能需要PyQt6-Charts支持\n请安装相关依赖")
        content_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        content_label.setStyleSheet("font-size: 14px; color: #666; line-height: 1.5;")
        layout.addWidget(content_label)
        
        parent_layout.addWidget(placeholder)
        
    def create_inventory_pie_chart(self, parent_layout):
        """创建库存分类饼图"""
        # 创建饼图系列
        self.pie_series = QPieSeries()
        self.pie_series.append("原材料", 35)
        self.pie_series.append("半成品", 25)
        self.pie_series.append("成品", 30)
        self.pie_series.append("其他", 10)
        
        # 设置饼图样式
        self.pie_series.setLabelsVisible(True)
        self.pie_series.setLabelsPosition(QPieSlice.LabelPosition.LabelOutside)
        
        # 突出显示第一个扇区
        slice = self.pie_series.slices()[0]
        slice.setExploded(True)
        slice.setLabelVisible(True)
        slice.setPen(QColor("#FFFFFF"))
        slice.setBrush(QColor("#4CAF50"))
        
        # 设置其他扇区颜色
        self.pie_series.slices()[1].setBrush(QColor("#2196F3"))
        self.pie_series.slices()[2].setBrush(QColor("#FF9800"))
        self.pie_series.slices()[3].setBrush(QColor("#F44336"))
        
        # 创建图表
        pie_chart = QChart()
        pie_chart.addSeries(self.pie_series)
        pie_chart.setTitle("库存分类占比")
        pie_chart.setAnimationOptions(QChart.AnimationOption.SeriesAnimations)
        pie_chart.legend().setVisible(True)
        pie_chart.legend().setAlignment(Qt.AlignmentFlag.AlignBottom)
        
        # 创建图表视图
        pie_chart_view = QChartView(pie_chart)
        pie_chart_view.setRenderHint(QPainter.RenderHint.Antialiasing)
        
        # 添加到布局
        parent_layout.addWidget(pie_chart_view)
        
    def create_monthly_bar_chart(self, parent_layout):
        """创建月度出入库柱状图"""
        # 创建柱状图系列
        self.in_set = QBarSet("入库")
        self.out_set = QBarSet("出库")
        
        # 添加数据
        self.in_set.append([120, 150, 180, 140, 160, 170])
        self.out_set.append([90, 120, 140, 130, 150, 160])
        
        # 设置柱状图颜色
        self.in_set.setColor(QColor("#4CAF50"))
        self.out_set.setColor(QColor("#F44336"))
        
        # 创建柱状图系列
        self.bar_series = QBarSeries()
        self.bar_series.append(self.in_set)
        self.bar_series.append(self.out_set)
        
        # 创建图表
        bar_chart = QChart()
        bar_chart.addSeries(self.bar_series)
        bar_chart.setTitle("近6个月出入库统计")
        bar_chart.setAnimationOptions(QChart.AnimationOption.SeriesAnimations)
        
        # 创建轴
        categories = ["1月", "2月", "3月", "4月", "5月", "6月"]
        axis_x = QBarCategoryAxis()
        axis_x.append(categories)
        bar_chart.addAxis(axis_x, Qt.AlignmentFlag.AlignBottom)
        self.bar_series.attachAxis(axis_x)
        
        axis_y = QValueAxis()
        axis_y.setRange(0, 200)
        axis_y.setTickCount(5)
        axis_y.setLabelFormat("%d")
        bar_chart.addAxis(axis_y, Qt.AlignmentFlag.AlignLeft)
        self.bar_series.attachAxis(axis_y)
        
        # 设置图例
        bar_chart.legend().setVisible(True)
        bar_chart.legend().setAlignment(Qt.AlignmentFlag.AlignBottom)
        
        # 创建图表视图
        bar_chart_view = QChartView(bar_chart)
        bar_chart_view.setRenderHint(QPainter.RenderHint.Antialiasing)
        
        # 添加到布局
        parent_layout.addWidget(bar_chart_view)
        
    def create_trend_line_chart(self, parent_layout):
        """创建趋势线图"""
        # 创建线图系列
        self.line_series = QLineSeries()
        self.line_series.setName("库存趋势")
        
        # 添加数据点
        for i in range(30):
            self.line_series.append(i, random.randint(80, 120))
        
        # 创建图表
        line_chart = QChart()
        line_chart.addSeries(self.line_series)
        line_chart.setTitle("近30天库存趋势")
        line_chart.setAnimationOptions(QChart.AnimationOption.SeriesAnimations)
        
        # 创建轴
        axis_x = QValueAxis()
        axis_x.setRange(1, 30)
        axis_x.setTickCount(7)
        axis_x.setLabelFormat("%d")
        line_chart.addAxis(axis_x, Qt.AlignmentFlag.AlignBottom)
        self.line_series.attachAxis(axis_x)
        
        axis_y = QValueAxis()
        axis_y.setRange(70, 130)
        axis_y.setTickCount(7)
        line_chart.addAxis(axis_y, Qt.AlignmentFlag.AlignLeft)
        self.line_series.attachAxis(axis_y)
        
        # 设置图例
        line_chart.legend().setVisible(True)
        line_chart.legend().setAlignment(Qt.AlignmentFlag.AlignBottom)
        
        # 创建图表视图
        line_chart_view = QChartView(line_chart)
        line_chart_view.setRenderHint(QPainter.RenderHint.Antialiasing)
        
        # 添加到布局
        parent_layout.addWidget(line_chart_view)
        
    def create_realtime_data_table(self, parent_layout):
        """创建实时数据表格"""
        # 表格标题
        table_title = QLabel("实时库存变动")
        table_title.setObjectName("tableTitle")
        parent_layout.addWidget(table_title)
        
        # 创建表格
        self.realtime_table = QTableWidget()
        self.realtime_table.setObjectName("realtimeTable")
        self.realtime_table.setColumnCount(5)
        self.realtime_table.setRowCount(20)
        
        # 设置表头
        headers = ["时间", "操作类型", "商品编码", "商品名称", "数量变化"]
        self.realtime_table.setHorizontalHeaderLabels(headers)
        
        # 设置表格列宽
        header = self.realtime_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.ResizeMode.Stretch)
        header.setSectionResizeMode(1, QHeaderView.ResizeMode.Stretch)
        header.setSectionResizeMode(2, QHeaderView.ResizeMode.Stretch)
        header.setSectionResizeMode(3, QHeaderView.ResizeMode.Stretch)
        header.setSectionResizeMode(4, QHeaderView.ResizeMode.Stretch)
        
        # 初始化表格数据
        self.update_realtime_table()
        
        # 添加到布局
        parent_layout.addWidget(self.realtime_table)
        
    def update_realtime_table(self):
        """更新实时数据表格 - 使用真实数据"""
        try:
            session = get_session()
            self.update_realtime_table_with_real_data(session)
            session.close()
        except Exception as e:
            print(f"更新实时表格失败: {e}")
            # 显示错误提示
            self.realtime_table.clearContents()
            self.realtime_table.setRowCount(1)
            item = QTableWidgetItem("数据加载失败")
            item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
            self.realtime_table.setItem(0, 0, item)
            self.realtime_table.setSpan(0, 0, 1, 6)  # 合并所有列
            
    def start_data_timers(self):
        """启动数据更新定时器"""
        # 数据卡片更新定时器
        self.data_timer = QTimer(self)
        self.data_timer.timeout.connect(self.update_data_cards)
        self.data_timer.start(10000)  # 每10秒更新一次
        
        # 实时表格更新定时器
        self.table_timer = QTimer(self)
        self.table_timer.timeout.connect(self.update_realtime_table)
        self.table_timer.start(5000)  # 每5秒更新一次
        
        # 初始加载数据
        self.update_data_cards()
        self.load_real_data()
        
    def update_data_cards(self):
        """更新数据卡片 - 使用真实数据"""
        try:
            # 调用真实数据加载方法
            self.load_real_data()
            
        except Exception as e:
            print(f"更新数据卡片失败: {e}")
            
    def update_card_value(self, card, new_value):
        """更新单个卡片的数值"""
        try:
            # 查找卡片中的数值标签
            for child in card.findChildren(QLabel):
                if child.objectName() == "cardValue":
                    child.setText(new_value)
                    break
        except Exception as e:
            print(f"更新卡片数值失败: {e}")
            
    def load_real_data(self):
        """加载真实数据"""
        try:
            session = get_session()
            
            # 获取总库存量
            total_inventory = session.query(func.sum(Inventory.current_quantity)).scalar() or 0
            
            # 获取库存总价值
            total_value_query = session.query(
                func.sum(Inventory.current_quantity * Product.standard_price)
            ).join(Product).filter(Product.standard_price.isnot(None))
            total_value = total_value_query.scalar() or Decimal('0')
            
            # 获取低库存商品数量（库存量小于最小库存的商品）
            low_stock_count = session.query(Product.id).join(Inventory).filter(
                Inventory.current_quantity < Product.min_stock
            ).distinct().count()
            
            # 获取缺货商品数量（库存量为0的商品）
            out_of_stock_count = session.query(Product.id).join(Inventory).filter(
                Inventory.current_quantity == 0
            ).distinct().count()
            
            # 更新数据卡片
            self.update_card_value(self.total_inventory_card, f"{total_inventory:,}")
            self.update_card_value(self.total_value_card, f"¥ {total_value:,.2f}")
            self.update_card_value(self.low_stock_card, str(low_stock_count))
            self.update_card_value(self.out_of_stock_card, str(out_of_stock_count))
            
            # 更新饼图数据
            self.update_pie_chart_data(session)
            
            # 更新实时数据表格
            self.update_realtime_table_with_real_data(session)
            
            session.close()
            
        except Exception as e:
            print(f"加载数据时出错: {e}")
            import traceback
            traceback.print_exc()
    
    def update_pie_chart_data(self, session):
        """更新饼图数据"""
        try:
            # 获取分类统计数据 - 修复SQL查询歧义
            category_stats = session.query(
                Category.name,
                func.sum(Inventory.current_quantity).label('total_quantity')
            ).select_from(Category).join(
                Product, Category.id == Product.category_id
            ).join(
                Inventory, Product.id == Inventory.product_id
            ).group_by(Category.id, Category.name).all()
            
            # 将分类归类到三大类
            category_mapping = {
                '配件': ['液压动力', '液压控制', '液压执行', '液压辅助', '电气动力', '电气配电', 
                        '电气控制', '电气仪表', '传感器', '电气辅助', '机械管路', '耐磨件', 
                        '加工件', '结构件', '螺栓丝杆'],
                '物资': ['油脂消耗', '日常消耗', '焊接耗材', '安全用品', '专用物资'],
                '通用': ['常用工具', '电动工具', '气动工具', '默认分类']
            }
            
            # 统计三大类的数量
            main_category_stats = {'配件': 0, '物资': 0, '通用': 0}
            
            for category_name, quantity in category_stats:
                quantity = quantity or 0
                categorized = False
                
                for main_cat, sub_cats in category_mapping.items():
                    if category_name in sub_cats:
                        main_category_stats[main_cat] += quantity
                        categorized = True
                        break
                
                # 如果没有匹配到，归类到通用
                if not categorized:
                    main_category_stats['通用'] += quantity
            
            # 清空现有数据
            self.pie_series.clear()
            
            # 添加三大类数据
            colors = ["#4CAF50", "#2196F3", "#FF9800"]
            for i, (main_category, quantity) in enumerate(main_category_stats.items()):
                if quantity > 0:  # 只显示有数据的分类
                    slice = self.pie_series.append(main_category, quantity)
                    slice.setBrush(QColor(colors[i % len(colors)]))
                    
            # 如果没有数据，显示默认数据
            if sum(main_category_stats.values()) == 0:
                self.pie_series.append("暂无数据", 1)
                
        except Exception as e:
            print(f"更新饼图数据时出错: {e}")
    
    def update_realtime_table_with_real_data(self, session):
        """使用真实数据更新实时数据表格"""
        try:
            # 获取最新的库存数据（前10条）
            inventory_data = session.query(
                Product.code,
                Product.name,
                Product.model,
                Inventory.current_quantity,
                Product.unit,
                Location.location_code
            ).join(Product).join(Location).order_by(
                Product.created_at.desc()
            ).limit(10).all()
            
            # 清空表格
            self.realtime_table.setRowCount(0)
            
            # 修改表头
            headers = ["商品编码", "商品名称", "规格型号", "库存数量", "单位", "库位"]
            self.realtime_table.setColumnCount(6)
            self.realtime_table.setHorizontalHeaderLabels(headers)
            
            # 添加数据
            for row_idx, (code, name, model, quantity, unit, location) in enumerate(inventory_data):
                self.realtime_table.insertRow(row_idx)
                
                # 商品编码
                self.realtime_table.setItem(row_idx, 0, QTableWidgetItem(code or ""))
                
                # 商品名称
                self.realtime_table.setItem(row_idx, 1, QTableWidgetItem(name or ""))
                
                # 规格型号
                self.realtime_table.setItem(row_idx, 2, QTableWidgetItem(model or ""))
                
                # 库存数量
                quantity_item = QTableWidgetItem(str(quantity or 0))
                quantity_item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
                self.realtime_table.setItem(row_idx, 3, quantity_item)
                
                # 单位
                unit_item = QTableWidgetItem(unit or "")
                unit_item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
                self.realtime_table.setItem(row_idx, 4, unit_item)
                
                # 库位
                location_item = QTableWidgetItem(location or "")
                location_item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
                self.realtime_table.setItem(row_idx, 5, location_item)
                
        except Exception as e:
            print(f"更新实时数据表格时出错: {e}")
            
    def set_styles(self):
        """设置样式"""
        # 使用统一样式
        from styles.unified_styles import get_unified_stylesheet
        unified_style = get_unified_stylesheet()
        
        # 仅添加当前模块特有的样式
        dashboard_style = """
            #dashboardTitle {
                font-size: 28px;
                font-weight: bold;
                color: #1976D2;
                text-align: center;
                margin: 10px 0 20px 0;
                padding: 10px;
                background: rgba(255, 255, 255, 0.8);
                border-radius: 8px;
                border: 2px solid #2196F3;
            }
            
            #dataCard {
                background: white;
                border: 1px solid #E0E0E0;
                border-radius: 8px;
                padding: 15px;
                margin: 5px;
                min-height: 120px;
            }
            
            #dataCard:hover {
                background: #F5F5F5;
            }
            
            #cardTitle {
                font-size: 14px;
                color: #666666;
                font-weight: normal;
                margin-bottom: 10px;
            }
            
            #cardValue {
                font-size: 32px;
                font-weight: bold;
                margin: 0;
            }
            
            #cardUnit {
                font-size: 16px;
                color: #999999;
                margin-left: 5px;
            }
            
            #tableTitle {
                font-size: 18px;
                font-weight: bold;
                color: #1976D2;
                margin: 20px 0 10px 0;
                padding: 8px 12px;
                background: rgba(255, 255, 255, 0.8);
                border-radius: 6px;
                border-left: 4px solid #2196F3;
            }
            
            #realtimeTable {
                background: white;
                border: 1px solid #E0E0E0;
                border-radius: 8px;
                gridline-color: #F0F0F0;
                selection-background-color: #E3F2FD;
                font-size: 13px;
            }
            
            #realtimeTable::item {
                padding: 8px;
                border-bottom: 1px solid #F5F5F5;
            }
            
            #realtimeTable::item:selected {
                background: #E3F2FD;
                color: #1976D2;
            }
            
            #realtimeTable QHeaderView::section {
                background: #F8F9FA;
                color: #333333;
                padding: 10px;
                border: none;
                border-bottom: 2px solid #E0E0E0;
                font-weight: bold;
            }
            
            QChartView {
                background: white;
                border: 1px solid #E0E0E0;
                border-radius: 8px;
                margin: 5px;
            }
        """
        
        self.setStyleSheet(unified_style + dashboard_style) 