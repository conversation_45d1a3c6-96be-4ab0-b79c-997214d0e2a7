#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
报表生成组件
"""

import sys
import os
from decimal import Decimal
from datetime import datetime, timedelta

from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QLabel, QFrame, 
    QTableWidget, QTableWidgetItem, QComboBox, QPushButton,
    QDateEdit, QGroupBox, QGridLayout, QScrollArea, QTextEdit,
    QCheckBox, QSpinBox, QProgressBar, QMessageBox, QFileDialog
)
from PyQt6.QtCore import Qt, QTimer, QDate, QThread, pyqtSignal
from PyQt6.QtGui import QFont, QColor, QPainter

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from config.database import get_session
from models.product import Product, Category, Supplier
from models.warehouse import Factory, Warehouse, Shelf, Location
from models.inventory import Inventory
from sqlalchemy import func, and_, or_

class ReportGeneratorWidget(QWidget):
    """报表生成组件"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.init_ui()
        self.set_styles()
        
    def init_ui(self):
        """初始化界面"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(5, 5, 5, 5)
        layout.setSpacing(20)
        
        # 创建标题
        title = QLabel("📋 报表生成")
        title.setObjectName("reportTitle")
        layout.addWidget(title)
        
        # 创建报表配置面板
        self.create_config_panel(layout)
        
        # 创建报表预览
        self.create_preview_panel(layout)
        
    def create_config_panel(self, parent_layout):
        """创建报表配置面板"""
        config_group = QGroupBox("报表配置")
        config_layout = QGridLayout(config_group)
        
        # 报表类型
        config_layout.addWidget(QLabel("报表类型:"), 0, 0)
        self.report_type = QComboBox()
        self.report_type.addItems([
            "库存汇总报表", "商品明细报表", "分类统计报表", 
            "供应商报表", "库位利用率报表", "库存预警报表"
        ])
        config_layout.addWidget(self.report_type, 0, 1)
        
        # 操作按钮
        button_layout = QHBoxLayout()
        
        self.generate_btn = QPushButton("生成报表")
        self.generate_btn.clicked.connect(self.generate_report)
        button_layout.addWidget(self.generate_btn)
        
        self.export_btn = QPushButton("导出报表")
        self.export_btn.clicked.connect(self.export_report)
        self.export_btn.setEnabled(False)
        button_layout.addWidget(self.export_btn)
        
        button_layout.addStretch()
        config_layout.addLayout(button_layout, 1, 0, 1, 4)
        
        parent_layout.addWidget(config_group)
        
    def create_preview_panel(self, parent_layout):
        """创建报表预览面板"""
        preview_group = QGroupBox("报表预览")
        preview_layout = QVBoxLayout(preview_group)
        
        self.preview_text = QTextEdit()
        self.preview_text.setObjectName("reportPreview")
        self.preview_text.setReadOnly(True)
        self.preview_text.setFont(QFont("Consolas", 10))
        preview_layout.addWidget(self.preview_text)
        
        parent_layout.addWidget(preview_group)
        
    def generate_report(self):
        """生成报表"""
        try:
            report_type = self.report_type.currentText()
            session = get_session()
            
            if report_type == "库存汇总报表":
                report_content = self.generate_inventory_summary_report(session)
            elif report_type == "商品明细报表":
                report_content = self.generate_product_detail_report(session)
            elif report_type == "分类统计报表":
                report_content = self.generate_category_statistics_report(session)
            elif report_type == "供应商报表":
                report_content = self.generate_supplier_report(session)
            elif report_type == "库位利用率报表":
                report_content = self.generate_location_utilization_report(session)
            elif report_type == "库存预警报表":
                report_content = self.generate_inventory_warning_report(session)
            else:
                report_content = "未知报表类型"
                
            session.close()
            self.preview_text.setPlainText(report_content)
            self.export_btn.setEnabled(True)
            
        except Exception as e:
            QMessageBox.warning(self, "生成失败", f"报表生成时出错: {str(e)}")
            
    def generate_inventory_summary_report(self, session):
        """生成库存汇总报表"""
        report = []
        report.append("=" * 60)
        report.append("库存汇总报表")
        report.append("=" * 60)
        report.append(f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        report.append("")
        
        # 总体统计
        total_products = session.query(Product).count()
        total_inventory = session.query(func.sum(Inventory.quantity)).scalar() or 0
        total_value = session.query(
            func.sum(Inventory.quantity * Product.unit_price)
        ).join(Product).filter(Product.unit_price.isnot(None)).scalar() or 0
        
        report.append("总体统计:")
        report.append(f"  商品总数: {total_products:,}")
        report.append(f"  库存总量: {total_inventory:,}")
        report.append(f"  库存总价值: ¥{total_value:,.2f}")
        report.append("")
        
        # 分仓库统计
        warehouse_stats = session.query(
            Warehouse.name,
            func.sum(Inventory.quantity).label('quantity'),
            func.count(Product.id.distinct()).label('product_count')
        ).join(Location).join(Inventory).join(Product).group_by(Warehouse.id, Warehouse.name).all()
        
        report.append("分仓库统计:")
        for warehouse_name, quantity, product_count in warehouse_stats:
            report.append(f"  {warehouse_name}: {quantity or 0:,}件, {product_count or 0}种商品")
        report.append("")
        
        # 分类统计
        category_stats = session.query(
            Category.name,
            func.sum(Inventory.quantity).label('quantity'),
            func.count(Product.id.distinct()).label('product_count')
        ).join(Product).join(Inventory).group_by(Category.id, Category.name).all()
        
        report.append("分类统计:")
        for category_name, quantity, product_count in category_stats:
            report.append(f"  {category_name}: {quantity or 0:,}件, {product_count or 0}种商品")
        
        return "\n".join(report)
        
    def generate_product_detail_report(self, session):
        """生成商品明细报表"""
        report = []
        report.append("=" * 80)
        report.append("商品明细报表")
        report.append("=" * 80)
        report.append(f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        report.append("")
        
        # 表头
        report.append(f"{'商品编码':<15} {'商品名称':<20} {'规格型号':<15} {'单位':<8} {'库存':<10} {'单价':<12} {'总价值':<15}")
        report.append("-" * 95)
        
        # 商品明细
        products = session.query(
            Product.code,
            Product.name,
            Product.model,
            Product.unit,
            Inventory.quantity,
            Product.unit_price
        ).join(Inventory).all()
        
        for code, name, model, unit, quantity, price in products:
            value = (quantity or 0) * (price or 0)
            report.append(
                f"{code or '':<15} {(name or '')[:18]:<20} {(model or '')[:13]:<15} "
                f"{unit or '':<8} {quantity or 0:<10} {price or 0:<12.2f} {value:<15.2f}"
            )
        
        return "\n".join(report)
        
    def generate_category_statistics_report(self, session):
        """生成分类统计报表"""
        report = []
        report.append("=" * 70)
        report.append("分类统计报表")
        report.append("=" * 70)
        report.append(f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        report.append("")
        
        # 分类统计
        category_stats = session.query(
            Category.name,
            func.count(Product.id).label('product_count'),
            func.sum(Inventory.quantity).label('total_quantity'),
            func.sum(Inventory.quantity * Product.unit_price).label('total_value'),
            func.avg(Product.unit_price).label('avg_price')
        ).join(Product).outerjoin(Inventory).group_by(Category.id, Category.name).all()
        
        report.append(f"{'分类名称':<20} {'商品数量':<10} {'库存总量':<12} {'总价值':<15} {'平均单价':<12}")
        report.append("-" * 75)
        
        for name, count, quantity, value, avg_price in category_stats:
            report.append(
                f"{name or '':<20} {count or 0:<10} {quantity or 0:<12} "
                f"{float(value or 0):<15.2f} {float(avg_price or 0):<12.2f}"
            )
        
        return "\n".join(report)
        
    def generate_supplier_report(self, session):
        """生成供应商报表"""
        report = []
        report.append("=" * 70)
        report.append("供应商报表")
        report.append("=" * 70)
        report.append(f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        report.append("")
        
        # 供应商统计
        supplier_stats = session.query(
            Supplier.name,
            Supplier.contact_person,
            Supplier.phone,
            func.count(Product.id).label('product_count'),
            func.sum(Inventory.quantity).label('total_quantity')
        ).join(Product).outerjoin(Inventory).group_by(
            Supplier.id, Supplier.name, Supplier.contact_person, Supplier.phone
        ).all()
        
        report.append(f"{'供应商名称':<20} {'联系人':<12} {'电话':<15} {'商品数量':<10} {'库存总量':<12}")
        report.append("-" * 75)
        
        for name, contact, phone, count, quantity in supplier_stats:
            report.append(
                f"{name or '':<20} {contact or '':<12} {phone or '':<15} "
                f"{count or 0:<10} {quantity or 0:<12}"
            )
        
        return "\n".join(report)
        
    def generate_location_utilization_report(self, session):
        """生成库位利用率报表"""
        report = []
        report.append("=" * 70)
        report.append("库位利用率报表")
        report.append("=" * 70)
        report.append(f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        report.append("")
        
        # 库位利用率统计
        location_stats = session.query(
            Location.location_code,
            Warehouse.name.label('warehouse_name'),
            func.sum(Inventory.quantity).label('used_quantity'),
            Location.capacity
        ).join(Shelf).join(Warehouse).outerjoin(Inventory).group_by(
            Location.id, Location.location_code, Warehouse.name, Location.capacity
        ).all()
        
        report.append(f"{'库位编码':<15} {'所属仓库':<15} {'已用容量':<10} {'总容量':<10} {'利用率':<10}")
        report.append("-" * 65)
        
        for location_code, warehouse_name, used_quantity, capacity in location_stats:
            used = used_quantity or 0
            cap = capacity or 100
            utilization = (used / cap * 100) if cap > 0 else 0
            
            report.append(
                f"{location_code or '':<15} {warehouse_name or '':<15} {used:<10} "
                f"{cap:<10} {utilization:<10.1f}%"
            )
        
        return "\n".join(report)
        
    def generate_inventory_warning_report(self, session):
        """生成库存预警报表"""
        report = []
        report.append("=" * 80)
        report.append("库存预警报表")
        report.append("=" * 80)
        report.append(f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        report.append("")
        
        # 预警统计
        low_stock_products = session.query(
            Product.code,
            Product.name,
            Product.model,
            Inventory.quantity,
            Product.min_stock,
            Location.location_code
        ).join(Inventory).join(Location).filter(
            Inventory.quantity < Product.min_stock
        ).all()
        
        report.append("低库存预警商品:")
        report.append(f"{'商品编码':<15} {'商品名称':<20} {'规格型号':<15} {'当前库存':<10} {'最小库存':<10} {'库位':<12}")
        report.append("-" * 85)
        
        for code, name, model, quantity, min_stock, location_code in low_stock_products:
            report.append(
                f"{code or '':<15} {(name or '')[:18]:<20} {(model or '')[:13]:<15} "
                f"{quantity or 0:<10} {min_stock or 0:<10} {location_code or '':<12}"
            )
        
        # 缺货商品
        out_of_stock_products = session.query(
            Product.code,
            Product.name,
            Product.model,
            Location.location_code
        ).join(Inventory).join(Location).filter(
            Inventory.quantity == 0
        ).all()
        
        report.append("")
        report.append("缺货商品:")
        report.append(f"{'商品编码':<15} {'商品名称':<20} {'规格型号':<15} {'库位':<12}")
        report.append("-" * 65)
        
        for code, name, model, location_code in out_of_stock_products:
            report.append(
                f"{code or '':<15} {(name or '')[:18]:<20} {(model or '')[:13]:<15} {location_code or '':<12}"
            )
        
        return "\n".join(report)
        
    def export_report(self):
        """导出报表"""
        try:
            if not self.preview_text.toPlainText():
                QMessageBox.warning(self, "导出失败", "请先生成报表")
                return
                
            # 选择保存路径
            file_path, _ = QFileDialog.getSaveFileName(
                self, "导出报表", 
                f"{self.report_type.currentText()}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt", 
                "文本文件 (*.txt);;CSV文件 (*.csv);;所有文件 (*)"
            )
            
            if file_path:
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(self.preview_text.toPlainText())
                
                QMessageBox.information(self, "导出成功", f"报表已导出到: {file_path}")
                
        except Exception as e:
            QMessageBox.warning(self, "导出失败", f"导出报表时出错: {str(e)}")
            
    def set_styles(self):
        """设置样式"""
        self.setStyleSheet("""
            QWidget {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #E3F2FD, stop:1 #BBDEFB);
                font-family: "Microsoft YaHei", Arial, sans-serif;
            }
            
            #reportTitle {
                font-size: 28px;
                font-weight: bold;
                color: #1976D2;
                text-align: center;
                margin: 10px 0 20px 0;
                padding: 10px;
                background: rgba(255, 255, 255, 0.8);
                border-radius: 8px;
                border: 2px solid #2196F3;
            }
            
            QGroupBox {
                font-size: 14px;
                font-weight: bold;
                color: #1976D2;
                border: 2px solid #E0E0E0;
                border-radius: 8px;
                margin-top: 10px;
                padding-top: 10px;
                background: rgba(255, 255, 255, 0.9);
            }
            
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
                background: rgba(255, 255, 255, 0.9);
            }
            
            QComboBox {
                padding: 5px;
                border: 1px solid #CCCCCC;
                border-radius: 4px;
                background: white;
                min-width: 120px;
            }
            
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #2196F3, stop:1 #1976D2);
                color: white;
                border: none;
                border-radius: 6px;
                padding: 8px 16px;
                font-weight: bold;
                min-width: 80px;
            }
            
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #1976D2, stop:1 #1565C0);
            }
            
            QPushButton:pressed {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #1565C0, stop:1 #0D47A1);
            }
            
            QPushButton:disabled {
                background: #CCCCCC;
                color: #666666;
            }
            
            #reportPreview {
                background: white;
                border: 1px solid #E0E0E0;
                border-radius: 8px;
                font-family: "Consolas", "Courier New", monospace;
                font-size: 10pt;
                line-height: 1.2;
            }
        """) 