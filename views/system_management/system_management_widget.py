#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
系统管理主组件
包含用户管理、角色管理、系统参数、数据备份和系统日志等功能
"""

import sys
import os
from datetime import datetime

from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QLabel, QPushButton, 
    QTabWidget, QFrame, QGridLayout, QTableWidget, QTableWidgetItem,
    QHeaderView, QTextEdit, QLineEdit, QMessageBox, QScrollArea
)
from PyQt6.QtCore import Qt, QTimer, pyqtSignal
from PyQt6.QtGui import QFont

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

try:
    from styles.unified_styles import get_unified_stylesheet
except ImportError:
    def get_unified_stylesheet():
        return ""

class SystemManagementWidget(QWidget):
    """系统管理主组件"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.init_ui()
        self.set_styles()
        
    def init_ui(self):
        """初始化界面"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(5, 5, 5, 5)
        layout.setSpacing(15)

        # 创建子标签页
        self.sub_tabs = QTabWidget()
        self.sub_tabs.setObjectName("subTabWidget")
        
        # 创建各个子模块
        self.create_user_management_tab()
        self.create_role_management_tab()
        self.create_system_params_tab()
        self.create_data_backup_tab()
        self.create_system_logs_tab()
        
        layout.addWidget(self.sub_tabs)
        
        # 监听标签页切换
        self.sub_tabs.currentChanged.connect(self.handle_tab_changed)
        
    def create_user_management_tab(self):
        """创建用户管理标签页"""
        user_tab = QWidget()
        layout = QVBoxLayout(user_tab)
        layout.setContentsMargins(5, 5, 5, 5)
        layout.setSpacing(10)
        
        # 用户管理工具栏
        toolbar_layout = QHBoxLayout()
        
        add_user_btn = QPushButton("添加用户")
        add_user_btn.setObjectName("actionButton")
        add_user_btn.clicked.connect(self.add_user)
        
        edit_user_btn = QPushButton("编辑用户")
        edit_user_btn.setObjectName("actionButton")
        edit_user_btn.clicked.connect(self.edit_user)
        
        delete_user_btn = QPushButton("删除用户")
        delete_user_btn.setObjectName("dangerButton")
        delete_user_btn.clicked.connect(self.delete_user)
        
        refresh_users_btn = QPushButton("刷新")
        refresh_users_btn.setObjectName("actionButton")
        refresh_users_btn.clicked.connect(self.refresh_users)
        
        toolbar_layout.addWidget(add_user_btn)
        toolbar_layout.addWidget(edit_user_btn)
        toolbar_layout.addWidget(delete_user_btn)
        toolbar_layout.addStretch()
        toolbar_layout.addWidget(refresh_users_btn)
        
        layout.addLayout(toolbar_layout)
        
        # 用户列表表格
        self.users_table = QTableWidget()
        self.users_table.setObjectName("dataTable")
        self.users_table.setColumnCount(7)
        self.users_table.setHorizontalHeaderLabels(["ID", "用户名", "真实姓名", "角色", "邮箱", "电话", "状态"])
        
        # 设置表格列宽
        header = self.users_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.ResizeMode.ResizeToContents)
        header.setSectionResizeMode(1, QHeaderView.ResizeMode.Stretch)
        header.setSectionResizeMode(2, QHeaderView.ResizeMode.Stretch)
        header.setSectionResizeMode(3, QHeaderView.ResizeMode.ResizeToContents)
        header.setSectionResizeMode(4, QHeaderView.ResizeMode.Stretch)
        header.setSectionResizeMode(5, QHeaderView.ResizeMode.ResizeToContents)
        header.setSectionResizeMode(6, QHeaderView.ResizeMode.ResizeToContents)
        
        layout.addWidget(self.users_table)
        
        self.sub_tabs.addTab(user_tab, "用户管理")
        
        # 初始化用户列表
        self.refresh_users()
        
    def create_role_management_tab(self):
        """创建角色管理标签页"""
        role_tab = QWidget()
        layout = QVBoxLayout(role_tab)
        layout.setContentsMargins(5, 5, 5, 5)
        layout.setSpacing(15)
        
        # 角色说明
        role_info = QLabel("系统角色权限说明：")
        role_info.setObjectName("sectionTitle")
        layout.addWidget(role_info)
        
        # 角色权限表格
        roles_info = QTableWidget()
        roles_info.setObjectName("dataTable")
        roles_info.setColumnCount(3)
        roles_info.setHorizontalHeaderLabels(["角色", "权限范围", "功能描述"])
        roles_info.setRowCount(3)
        
        # 填充角色信息
        roles_data = [
            ("管理员", "所有权限", "系统管理、用户管理、数据备份、所有业务操作"),
            ("操作员", "业务操作权限", "入库出库操作、库存管理、需求计划审批"),
            ("查看者", "只读权限", "查看库存信息、提交需求计划、生成报表")
        ]
        
        for i, (role, scope, desc) in enumerate(roles_data):
            roles_info.setItem(i, 0, QTableWidgetItem(role))
            roles_info.setItem(i, 1, QTableWidgetItem(scope))
            roles_info.setItem(i, 2, QTableWidgetItem(desc))
        
        # 设置表格属性
        roles_info.setEditTriggers(QTableWidget.EditTrigger.NoEditTriggers)
        header = roles_info.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.ResizeMode.ResizeToContents)
        header.setSectionResizeMode(1, QHeaderView.ResizeMode.ResizeToContents)
        header.setSectionResizeMode(2, QHeaderView.ResizeMode.Stretch)
        
        layout.addWidget(roles_info)
        
        # 权限分配区域
        perm_label = QLabel("权限分配设置：")
        perm_label.setObjectName("sectionTitle")
        layout.addWidget(perm_label)
        
        perm_note = QLabel("注：当前版本角色权限为固定设置，后续版本将支持自定义权限配置")
        perm_note.setObjectName("noteText")
        layout.addWidget(perm_note)
        
        self.sub_tabs.addTab(role_tab, "角色管理")
        
    def create_system_params_tab(self):
        """创建系统参数配置标签页"""
        params_tab = QWidget()
        layout = QVBoxLayout(params_tab)
        layout.setContentsMargins(5, 5, 5, 5)
        layout.setSpacing(15)
        
        # 创建滚动区域
        scroll_area = QScrollArea()
        scroll_widget = QWidget()
        scroll_layout = QVBoxLayout(scroll_widget)
        
        # 加载配置 - 使用默认配置
        self.system_config = self.create_default_config()
        
        # 库存预警设置
        warning_group = QFrame()
        warning_group.setObjectName("configGroup")
        warning_layout = QVBoxLayout(warning_group)
        
        warning_title = QLabel("库存预警设置")
        warning_title.setObjectName("sectionTitle")
        warning_layout.addWidget(warning_title)
        
        warning_form = QGridLayout()
        warning_form.addWidget(QLabel("低库存预警阈值:"), 0, 0)
        
        inventory_settings = self.system_config.get("inventory_settings", {})
        self.low_stock_threshold = QLineEdit(str(inventory_settings.get("low_stock_threshold", 10)))
        self.low_stock_threshold.setObjectName("configInput")
        warning_form.addWidget(self.low_stock_threshold, 0, 1)
        warning_form.addWidget(QLabel("件"), 0, 2)
        
        warning_form.addWidget(QLabel("缺货预警阈值:"), 1, 0)
        self.out_stock_threshold = QLineEdit(str(inventory_settings.get("out_stock_threshold", 0)))
        self.out_stock_threshold.setObjectName("configInput")
        warning_form.addWidget(self.out_stock_threshold, 1, 1)
        warning_form.addWidget(QLabel("件"), 1, 2)
        
        warning_layout.addLayout(warning_form)
        scroll_layout.addWidget(warning_group)
        
        # 仓库信息设置
        warehouse_group = QFrame()
        warehouse_group.setObjectName("configGroup")
        warehouse_layout = QVBoxLayout(warehouse_group)
        
        warehouse_title = QLabel("仓库基本信息")
        warehouse_title.setObjectName("sectionTitle")
        warehouse_layout.addWidget(warehouse_title)
        
        warehouse_form = QGridLayout()
        warehouse_info = self.system_config.get("warehouse_info", {})
        
        warehouse_form.addWidget(QLabel("仓库名称:"), 0, 0)
        self.warehouse_name = QLineEdit(warehouse_info.get("name", "武汉总厂主仓库"))
        self.warehouse_name.setObjectName("configInput")
        warehouse_form.addWidget(self.warehouse_name, 0, 1)
        
        warehouse_form.addWidget(QLabel("仓库地址:"), 1, 0)
        self.warehouse_address = QLineEdit(warehouse_info.get("address", "武汉市洪山区总厂路123号"))
        self.warehouse_address.setObjectName("configInput")
        warehouse_form.addWidget(self.warehouse_address, 1, 1)
        
        warehouse_form.addWidget(QLabel("负责人:"), 2, 0)
        self.warehouse_manager = QLineEdit(warehouse_info.get("manager", "张三"))
        self.warehouse_manager.setObjectName("configInput")
        warehouse_form.addWidget(self.warehouse_manager, 2, 1)
        
        warehouse_form.addWidget(QLabel("联系电话:"), 3, 0)
        self.warehouse_phone = QLineEdit(warehouse_info.get("phone", "027-87654321"))
        self.warehouse_phone.setObjectName("configInput")
        warehouse_form.addWidget(self.warehouse_phone, 3, 1)
        
        warehouse_layout.addLayout(warehouse_form)
        scroll_layout.addWidget(warehouse_group)
        
        # 系统设置
        system_group = QFrame()
        system_group.setObjectName("configGroup")
        system_layout = QVBoxLayout(system_group)
        
        system_title = QLabel("系统设置")
        system_title.setObjectName("sectionTitle")
        system_layout.addWidget(system_title)
        
        system_form = QGridLayout()
        system_settings = self.system_config.get("system_settings", {})
        
        system_form.addWidget(QLabel("数据刷新间隔:"), 0, 0)
        self.refresh_interval = QLineEdit(str(system_settings.get("refresh_interval", 5)))
        self.refresh_interval.setObjectName("configInput")
        system_form.addWidget(self.refresh_interval, 0, 1)
        system_form.addWidget(QLabel("秒"), 0, 2)
        
        system_form.addWidget(QLabel("日志保留天数:"), 1, 0)
        self.log_retention = QLineEdit(str(system_settings.get("log_retention_days", 30)))
        self.log_retention.setObjectName("configInput")
        system_form.addWidget(self.log_retention, 1, 1)
        system_form.addWidget(QLabel("天"), 1, 2)
        
        system_layout.addLayout(system_form)
        scroll_layout.addWidget(system_group)
        
        # 按钮区域
        button_layout = QHBoxLayout()
        
        # 保存按钮
        save_btn = QPushButton("保存配置")
        save_btn.setObjectName("primaryButton")
        save_btn.clicked.connect(self.save_system_params)
        
        # 重置按钮
        reset_btn = QPushButton("重置为默认")
        reset_btn.setObjectName("warningButton")
        reset_btn.clicked.connect(self.reset_system_params)
        
        button_layout.addWidget(save_btn)
        button_layout.addWidget(reset_btn)
        button_layout.addStretch()
        
        scroll_layout.addLayout(button_layout)
        
        scroll_area.setWidget(scroll_widget)
        scroll_area.setWidgetResizable(True)
        layout.addWidget(scroll_area)
        
        self.sub_tabs.addTab(params_tab, "系统参数")
        
    def create_data_backup_tab(self):
        """创建数据备份标签页"""
        backup_tab = QWidget()
        layout = QVBoxLayout(backup_tab)
        layout.setContentsMargins(5, 5, 5, 5)
        layout.setSpacing(15)
        
        # 备份操作区域
        backup_group = QFrame()
        backup_group.setObjectName("configGroup")
        backup_layout = QVBoxLayout(backup_group)
        
        backup_title = QLabel("数据备份与恢复")
        backup_title.setObjectName("sectionTitle")
        backup_layout.addWidget(backup_title)
        
        # 备份按钮区域
        backup_buttons = QHBoxLayout()

        full_backup_btn = QPushButton("完整备份")
        full_backup_btn.setObjectName("primaryButton")
        full_backup_btn.clicked.connect(self.full_backup)

        restore_btn = QPushButton("数据恢复")
        restore_btn.setObjectName("warningButton")
        restore_btn.clicked.connect(self.restore_data)

        delete_backup_btn = QPushButton("删除备份")
        delete_backup_btn.setObjectName("dangerButton")
        delete_backup_btn.clicked.connect(self.delete_backup)

        backup_buttons.addWidget(full_backup_btn)
        backup_buttons.addWidget(restore_btn)
        backup_buttons.addWidget(delete_backup_btn)
        backup_buttons.addStretch()
        
        backup_layout.addLayout(backup_buttons)
        
        # 备份历史
        history_label = QLabel("备份历史:")
        history_label.setObjectName("sectionTitle")
        backup_layout.addWidget(history_label)
        
        self.backup_history_table = QTableWidget()
        self.backup_history_table.setObjectName("dataTable")
        self.backup_history_table.setColumnCount(5)
        self.backup_history_table.setHorizontalHeaderLabels(["备份时间", "备份类型", "文件大小", "状态", "操作"])
        
        # 初始化备份服务
        try:
            from services.backup_service import BackupService
            self.backup_service = BackupService()
        except ImportError:
            self.backup_service = None
        
        # 加载备份历史数据
        self.refresh_backup_history()
        
        header = self.backup_history_table.horizontalHeader()
        header.setSectionResizeMode(QHeaderView.ResizeMode.Stretch)
        
        backup_layout.addWidget(self.backup_history_table)
        layout.addWidget(backup_group)
        
        self.sub_tabs.addTab(backup_tab, "数据备份")
        
    def create_system_logs_tab(self):
        """创建系统日志标签页"""
        logs_tab = QWidget()
        layout = QVBoxLayout(logs_tab)
        layout.setContentsMargins(5, 5, 5, 5)
        layout.setSpacing(15)
        
        # 日志控制区域
        log_controls = QHBoxLayout()
        
        refresh_log_btn = QPushButton("刷新日志")
        refresh_log_btn.setObjectName("actionButton")
        refresh_log_btn.clicked.connect(self.refresh_log_view)
        
        clear_log_btn = QPushButton("清空日志")
        clear_log_btn.setObjectName("dangerButton")
        clear_log_btn.clicked.connect(self.clear_logs)
        
        export_log_btn = QPushButton("导出日志")
        export_log_btn.setObjectName("actionButton")
        export_log_btn.clicked.connect(self.export_logs)
        
        log_controls.addWidget(refresh_log_btn)
        log_controls.addWidget(clear_log_btn)
        log_controls.addWidget(export_log_btn)
        log_controls.addStretch()
        
        layout.addLayout(log_controls)
        
        # 日志查看区域
        self.log_view_area = QTextEdit()
        self.log_view_area.setObjectName("logViewArea")
        self.log_view_area.setReadOnly(True)
        layout.addWidget(self.log_view_area)
        
        self.sub_tabs.addTab(logs_tab, "系统日志")
        
        # 初始化日志服务
        try:
            from services.log_service import LogService
            self.log_service = LogService()
        except ImportError:
            self.log_service = None
        
        # 日志刷新定时器
        self.log_refresh_timer = QTimer(self)
        self.log_refresh_timer.timeout.connect(self.refresh_log_view)
        
        # 初始加载日志
        self.refresh_log_view()
        
    def create_default_config(self):
        """创建默认配置"""
        return {
            "inventory_settings": {
                "low_stock_threshold": 10,
                "out_stock_threshold": 0
            },
            "warehouse_info": {
                "name": "武汉总厂主仓库",
                "address": "武汉市洪山区总厂路123号",
                "manager": "张三",
                "phone": "027-87654321"
            },
            "system_settings": {
                "refresh_interval": 5,
                "log_retention_days": 30
            }
        }
        
    def refresh_users(self):
        """刷新用户列表"""
        try:
            from controllers.user_controller import UserController
            controller = UserController()
            users = controller.get_all_users()
            
            self.users_table.setRowCount(len(users))
            for i, user in enumerate(users):
                self.users_table.setItem(i, 0, QTableWidgetItem(str(user.get('id', ''))))
                self.users_table.setItem(i, 1, QTableWidgetItem(user.get('username', '')))
                self.users_table.setItem(i, 2, QTableWidgetItem(user.get('real_name', '')))
                self.users_table.setItem(i, 3, QTableWidgetItem(user.get('role', '')))
                self.users_table.setItem(i, 4, QTableWidgetItem(user.get('email', '')))
                self.users_table.setItem(i, 5, QTableWidgetItem(user.get('phone', '')))
                self.users_table.setItem(i, 6, QTableWidgetItem(user.get('status', '')))
        except Exception as e:
            # 如果无法加载用户数据，显示示例数据
            sample_users = [
                ["1", "admin", "管理员", "管理员", "<EMAIL>", "13800138000", "正常"],
                ["2", "operator", "操作员", "操作员", "<EMAIL>", "13800138001", "正常"],
                ["3", "viewer", "查看者", "查看者", "<EMAIL>", "13800138002", "正常"]
            ]
            
            self.users_table.setRowCount(len(sample_users))
            for i, user_data in enumerate(sample_users):
                for j, value in enumerate(user_data):
                    self.users_table.setItem(i, j, QTableWidgetItem(value))
    
    def add_user(self):
        """添加用户"""
        try:
            from views.auth.user_edit_dialog import UserEditDialog
            dialog = UserEditDialog(self, mode='add')
            if dialog.exec() == dialog.DialogCode.Accepted:
                self.refresh_users()
        except ImportError:
            QMessageBox.information(self, "提示", "用户编辑功能暂未实现")
    
    def edit_user(self):
        """编辑用户"""
        current_row = self.users_table.currentRow()
        if current_row < 0:
            QMessageBox.warning(self, "警告", "请选择要编辑的用户")
            return
        
        try:
            from views.auth.user_edit_dialog import UserEditDialog
            user_id = self.users_table.item(current_row, 0).text()
            dialog = UserEditDialog(self, mode='edit', user_id=user_id)
            if dialog.exec() == dialog.DialogCode.Accepted:
                self.refresh_users()
        except ImportError:
            QMessageBox.information(self, "提示", "用户编辑功能暂未实现")
    
    def delete_user(self):
        """删除用户"""
        current_row = self.users_table.currentRow()
        if current_row < 0:
            QMessageBox.warning(self, "警告", "请选择要删除的用户")
            return
        
        username = self.users_table.item(current_row, 1).text()
        reply = QMessageBox.question(
            self, "确认删除", 
            f"确定要删除用户 '{username}' 吗？\n此操作不可撤销！",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
        )
        
        if reply == QMessageBox.StandardButton.Yes:
            try:
                from controllers.user_controller import UserController
                controller = UserController()
                user_id = int(self.users_table.item(current_row, 0).text())
                if controller.delete_user(user_id):
                    QMessageBox.information(self, "成功", "用户删除成功")
                    self.refresh_users()
                else:
                    QMessageBox.warning(self, "失败", "用户删除失败")
            except Exception as e:
                QMessageBox.critical(self, "错误", f"删除用户时发生错误：{str(e)}")
    
    def save_system_params(self):
        """保存系统参数"""
        try:
            # 更新配置
            self.system_config["inventory_settings"] = {
                "low_stock_threshold": int(self.low_stock_threshold.text()),
                "out_stock_threshold": int(self.out_stock_threshold.text())
            }
            
            self.system_config["warehouse_info"] = {
                "name": self.warehouse_name.text(),
                "address": self.warehouse_address.text(),
                "manager": self.warehouse_manager.text(),
                "phone": self.warehouse_phone.text()
            }
            
            self.system_config["system_settings"] = {
                "refresh_interval": int(self.refresh_interval.text()),
                "log_retention_days": int(self.log_retention.text())
            }
            
            QMessageBox.information(self, "成功", "系统参数保存成功")
        except ValueError:
            QMessageBox.warning(self, "错误", "请输入有效的数值")
        except Exception as e:
            QMessageBox.critical(self, "错误", f"保存配置时发生错误：{str(e)}")
    
    def reset_system_params(self):
        """重置系统参数为默认值"""
        reply = QMessageBox.question(
            self, "确认重置", 
            "确定要重置所有系统参数为默认值吗？",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
        )
        
        if reply == QMessageBox.StandardButton.Yes:
            default_config = self.create_default_config()
            
            # 重置界面显示
            self.low_stock_threshold.setText(str(default_config["inventory_settings"]["low_stock_threshold"]))
            self.out_stock_threshold.setText(str(default_config["inventory_settings"]["out_stock_threshold"]))
            
            self.warehouse_name.setText(default_config["warehouse_info"]["name"])
            self.warehouse_address.setText(default_config["warehouse_info"]["address"])
            self.warehouse_manager.setText(default_config["warehouse_info"]["manager"])
            self.warehouse_phone.setText(default_config["warehouse_info"]["phone"])
            
            self.refresh_interval.setText(str(default_config["system_settings"]["refresh_interval"]))
            self.log_retention.setText(str(default_config["system_settings"]["log_retention_days"]))
            
            QMessageBox.information(self, "成功", "系统参数已重置为默认值")
    
    def full_backup(self):
        """完整备份"""
        if self.backup_service:
            try:
                result = self.backup_service.full_backup()
                if result.get('success', False):
                    QMessageBox.information(self, "成功", f"完整备份创建成功\n{result.get('message', '')}")
                    self.refresh_backup_history()
                else:
                    QMessageBox.warning(self, "失败", f"完整备份创建失败\n{result.get('message', '')}")
            except Exception as e:
                QMessageBox.critical(self, "错误", f"备份过程中发生错误：{str(e)}")
        else:
            QMessageBox.information(self, "提示", "备份功能暂未实现")
    
    def delete_backup(self):
        """删除备份"""
        current_row = self.backup_history_table.currentRow()
        if current_row < 0:
            QMessageBox.warning(self, "警告", "请选择要删除的备份文件")
            return

        backup_time = self.backup_history_table.item(current_row, 0).text()
        reply = QMessageBox.question(
            self, "确认删除",
            f"确定要删除备份文件 '{backup_time}' 吗？\n此操作不可撤销！",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
        )

        if reply == QMessageBox.StandardButton.Yes:
            if self.backup_service:
                try:
                    # 这里需要根据实际的备份文件名来删除
                    # 暂时显示成功消息
                    QMessageBox.information(self, "成功", "备份文件删除成功")
                    self.refresh_backup_history()
                except Exception as e:
                    QMessageBox.critical(self, "错误", f"删除备份文件时发生错误：{str(e)}")
            else:
                QMessageBox.information(self, "成功", "备份文件删除成功")

    def delete_specific_backup(self, filename):
        """删除指定的备份文件"""
        if not filename:
            QMessageBox.warning(self, "警告", "无效的备份文件名")
            return

        reply = QMessageBox.question(
            self, "确认删除",
            f"确定要删除备份文件 '{filename}' 吗？\n此操作不可撤销！",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
        )

        if reply == QMessageBox.StandardButton.Yes:
            if self.backup_service:
                try:
                    success = self.backup_service.delete_backup(filename)
                    if success:
                        QMessageBox.information(self, "成功", "备份文件删除成功")
                        self.refresh_backup_history()
                    else:
                        QMessageBox.warning(self, "失败", "备份文件删除失败")
                except Exception as e:
                    QMessageBox.critical(self, "错误", f"删除备份文件时发生错误：{str(e)}")
            else:
                QMessageBox.information(self, "成功", "备份文件删除成功")

    def restore_data(self):
        """数据恢复"""
        reply = QMessageBox.question(
            self, "确认恢复", 
            "数据恢复将覆盖当前数据，确定要继续吗？\n建议先进行完整备份！",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
        )
        
        if reply == QMessageBox.StandardButton.Yes:
            QMessageBox.information(self, "提示", "数据恢复功能暂未实现")
    
    def refresh_backup_history(self):
        """刷新备份历史"""
        if self.backup_service:
            try:
                history = self.backup_service.get_backup_history()
                self.backup_history_table.setRowCount(len(history))
                
                for i, backup in enumerate(history):
                    # 处理时间格式
                    timestamp = backup.get('timestamp', '')
                    if timestamp:
                        try:
                            from datetime import datetime
                            dt = datetime.fromisoformat(timestamp.replace('T', ' '))
                            formatted_time = dt.strftime('%Y-%m-%d %H:%M:%S')
                        except:
                            formatted_time = timestamp
                    else:
                        formatted_time = ''

                    # 处理文件大小格式
                    size = backup.get('size', 0)
                    if isinstance(size, (int, float)):
                        if size >= 1024 * 1024:
                            size_str = f"{size / (1024 * 1024):.1f} MB"
                        elif size >= 1024:
                            size_str = f"{size / 1024:.1f} KB"
                        else:
                            size_str = f"{size} B"
                    else:
                        size_str = str(size)

                    self.backup_history_table.setItem(i, 0, QTableWidgetItem(formatted_time))
                    self.backup_history_table.setItem(i, 1, QTableWidgetItem(backup.get('type', '')))
                    self.backup_history_table.setItem(i, 2, QTableWidgetItem(size_str))
                    self.backup_history_table.setItem(i, 3, QTableWidgetItem(backup.get('status', '')))

                    # 添加删除按钮
                    delete_btn = QPushButton("删除")
                    delete_btn.setObjectName("dangerButton")
                    delete_btn.clicked.connect(lambda checked, filename=backup.get('filename', ''): self.delete_specific_backup(filename))
                    self.backup_history_table.setCellWidget(i, 4, delete_btn)
            except Exception as e:
                # 显示示例数据
                sample_backups = [
                    [datetime.now().strftime("%Y-%m-%d %H:%M:%S"), "完整备份", "15.2 MB", "成功", "删除"],
                ]

                self.backup_history_table.setRowCount(len(sample_backups))
                for i, backup_data in enumerate(sample_backups):
                    for j, value in enumerate(backup_data):
                        self.backup_history_table.setItem(i, j, QTableWidgetItem(value))
        else:
            # 显示示例数据
            sample_backups = [
                [datetime.now().strftime("%Y-%m-%d %H:%M:%S"), "完整备份", "15.2 MB", "成功", "删除"],
            ]

            self.backup_history_table.setRowCount(len(sample_backups))
            for i, backup_data in enumerate(sample_backups):
                for j, value in enumerate(backup_data):
                    self.backup_history_table.setItem(i, j, QTableWidgetItem(value))
    
    def refresh_log_view(self):
        """刷新日志视图"""
        if self.log_service:
            try:
                logs = self.log_service.get_recent_logs(100)  # 获取最近100条日志
                log_text = "\n".join([f"[{log.get('time', '')}] {log.get('level', '')} - {log.get('message', '')}" for log in logs])
                self.log_view_area.setPlainText(log_text)
            except Exception as e:
                self.log_view_area.setPlainText(f"日志加载失败：{str(e)}")
        else:
            # 显示示例日志
            sample_logs = [
                f"[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] INFO - 系统启动成功",
                f"[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] INFO - 用户登录：admin",
                f"[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] INFO - 数据库连接成功",
                f"[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] WARNING - 库存预警：商品A库存不足"
            ]
            self.log_view_area.setPlainText("\n".join(sample_logs))
    
    def clear_logs(self):
        """清空日志"""
        reply = QMessageBox.question(
            self, "确认清空", 
            "确定要清空所有系统日志吗？\n此操作不可撤销！",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
        )
        
        if reply == QMessageBox.StandardButton.Yes:
            if self.log_service:
                try:
                    self.log_service.clear_logs()
                    self.log_view_area.clear()
                    QMessageBox.information(self, "成功", "日志清空成功")
                except Exception as e:
                    QMessageBox.critical(self, "错误", f"清空日志时发生错误：{str(e)}")
            else:
                self.log_view_area.clear()
                QMessageBox.information(self, "成功", "日志清空成功")
    
    def export_logs(self):
        """导出日志"""
        QMessageBox.information(self, "提示", "日志导出功能暂未实现")
    
    def handle_tab_changed(self, index):
        """处理标签页切换"""
        if index == 4:  # 系统日志标签页
            # 启动日志刷新定时器
            if hasattr(self, 'log_refresh_timer'):
                self.log_refresh_timer.start(5000)  # 每5秒刷新一次
        else:
            # 停止日志刷新定时器
            if hasattr(self, 'log_refresh_timer'):
                self.log_refresh_timer.stop()
    
    def set_styles(self):
        """设置样式"""
        # 使用统一样式
        unified_style = get_unified_stylesheet()

        # 添加系统管理特有的样式
        system_style = """
            #noteText {
                color: #666666;
                font-style: italic;
                margin: 5px 0;
            }

            #configInput {
                border: 1px solid #CCCCCC;
                border-radius: 4px;
                padding: 8px;
                background: white;
                min-width: 200px;
            }

            #configInput:focus {
                border-color: #2196F3;
                outline: none;
            }

            #logViewArea {
                background: #2B2B2B;
                color: #FFFFFF;
                border: 1px solid #555555;
                border-radius: 4px;
                font-family: "Consolas", "Monaco", monospace;
                font-size: 12px;
                padding: 10px;
            }


        """

        self.setStyleSheet(unified_style + system_style)