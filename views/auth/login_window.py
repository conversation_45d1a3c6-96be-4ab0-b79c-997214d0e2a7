#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
最终版登录窗口模块 - 背景图片完美填充
"""

import sys
import os
from PyQt6.QtWidgets import (QApplication, QMainWindow, QWidget, QVBoxLayout, 
                             QHBoxLayout, QLabel, QLineEdit, QPushButton, 
                             QCheckBox, QFrame, QGraphicsDropShadowEffect)
from PyQt6.QtCore import Qt, QTimer, pyqtSignal
from PyQt6.QtGui import QPixmap, QPalette, QBrush, QFont, QIcon

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from config.database import test_connection
from utils.path_manager import get_image_path

class LoginWindow(QMainWindow):
    """登录窗口类 - 背景图片完美填充"""
    
    # 定义信号
    login_success = pyqtSignal(str)  # 登录成功信号，传递用户名
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("武汉总厂仓库管理系统 - 登录")
        self.setFixedSize(1000, 700)
        
        # 禁用深色边框和设置窗口属性
        self.setAttribute(Qt.WidgetAttribute.WA_StyledBackground, True)
        
        # 设置窗口居中
        self.center_window()
        
        # 初始化UI
        self.init_ui()
        
        # 设置样式
        self.set_styles()
        
        # 启动数据库连接检查
        self.check_database_connection()
        
    def center_window(self):
        """窗口居中显示"""
        screen = QApplication.primaryScreen().geometry()
        window = self.geometry()
        x = (screen.width() - window.width()) // 2
        y = (screen.height() - window.height()) // 2
        self.move(x, y)
        
    def init_ui(self):
        """初始化用户界面"""
        # 创建中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 创建背景标签（覆盖整个窗口，使用绝对定位）
        self.bg_label = QLabel(central_widget)
        self.bg_label.setGeometry(0, 0, 1000, 700)  # 精确覆盖整个窗口
        self.bg_label.lower()  # 放到最底层
        
        # 设置背景图片
        self.set_background_image()
        
        # 创建主布局
        main_layout = QHBoxLayout(central_widget)
        main_layout.setContentsMargins(0, 0, 0, 0)
        
        # 左侧空白区域（让背景图片显示）
        left_spacer = QWidget()
        left_spacer.setFixedWidth(600)
        left_spacer.setStyleSheet("background: transparent;")
        main_layout.addWidget(left_spacer)
        
        # 右侧登录区域
        self.create_login_panel(main_layout)
        
    def set_background_image(self):
        """设置背景图片（使用QLabel确保完全填充）"""
        try:
            # 使用路径管理器获取背景图片路径
            bg_path = get_image_path("login_bg.png")
            
            if bg_path.exists():
                pixmap = QPixmap(str(bg_path))
                if not pixmap.isNull():
                    # 使用保持宽高比扩展的方式，然后裁剪
                    scaled_pixmap = pixmap.scaled(
                        1000, 700,
                        Qt.AspectRatioMode.KeepAspectRatioByExpanding,
                        Qt.TransformationMode.SmoothTransformation
                    )
                    
                    # 如果缩放后的图片比窗口大，需要居中裁剪
                    if scaled_pixmap.width() > 1000 or scaled_pixmap.height() > 700:
                        # 计算裁剪位置（居中）
                        x = max(0, (scaled_pixmap.width() - 1000) // 2)
                        y = max(0, (scaled_pixmap.height() - 700) // 2)
                        
                        # 裁剪图片到精确尺寸
                        final_pixmap = scaled_pixmap.copy(x, y, 1000, 700)
                    else:
                        final_pixmap = scaled_pixmap
                    
                    # 设置到背景标签
                    self.bg_label.setPixmap(final_pixmap)
                  
                else:
                    print("❌ 图片加载失败")
                    self.set_fallback_background()
            else:
                print(f"❌ 背景图片未找到: {bg_path}")
                self.set_fallback_background()
                
        except Exception as e:
            print(f"❌ 设置背景失败: {e}")
            self.set_fallback_background()
            
    def set_fallback_background(self):
        """设置备用背景（渐变色）"""
        # 创建渐变背景图片
        fallback_pixmap = QPixmap(1000, 700)
        fallback_pixmap.fill(Qt.GlobalColor.blue)  # 简单的蓝色背景
        self.bg_label.setPixmap(fallback_pixmap)
        print("✅ 使用备用蓝色背景")
        
    def create_login_panel(self, main_layout):
        """创建登录面板"""
        # 创建登录面板容器
        login_container = QWidget()
        login_container.setFixedWidth(400)
        login_container.setObjectName("loginContainer")
        
        # 创建登录面板布局
        container_layout = QVBoxLayout(login_container)
        container_layout.setContentsMargins(40, 0, 40, 40)
        container_layout.setSpacing(20)
        
        # 添加顶部间距
        container_layout.addStretch(1)
        
        # 创建登录面板
        login_panel = QFrame()
        login_panel.setObjectName("loginPanel")
        login_panel.setFixedSize(320, 520)
        
        # 添加阴影效果
        shadow = QGraphicsDropShadowEffect()
        shadow.setBlurRadius(20)
        shadow.setColor(Qt.GlobalColor.black)
        shadow.setOffset(0, 5)
        login_panel.setGraphicsEffect(shadow)
        
        # 登录面板布局
        panel_layout = QVBoxLayout(login_panel)
        panel_layout.setContentsMargins(30, 40, 30, 30)
        panel_layout.setSpacing(20)
        
        # 标题
        title_label = QLabel("系统登录")
        title_label.setObjectName("titleLabel")
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        panel_layout.addWidget(title_label)
        
        # 用户名输入
        self.username_input = QLineEdit()
        self.username_input.setPlaceholderText("请输入用户名")
        self.username_input.setObjectName("inputField")
        self.username_input.setText("admin")  # 默认用户名
        panel_layout.addWidget(self.username_input)
        
        # 密码输入
        self.password_input = QLineEdit()
        self.password_input.setPlaceholderText("请输入密码")
        self.password_input.setEchoMode(QLineEdit.EchoMode.Password)
        self.password_input.setObjectName("inputField")
        self.password_input.setText("123456")  # 默认密码
        panel_layout.addWidget(self.password_input)
        
        # 记住密码复选框
        self.remember_checkbox = QCheckBox("记住密码")
        self.remember_checkbox.setObjectName("checkBox")
        self.remember_checkbox.setChecked(True)
        panel_layout.addWidget(self.remember_checkbox)
        
        # 登录按钮
        self.login_button = QPushButton("登录")
        self.login_button.setObjectName("loginButton")
        self.login_button.clicked.connect(self.handle_login)
        panel_layout.addWidget(self.login_button)
        
        # 注册按钮
        self.register_button = QPushButton("注册新用户")
        self.register_button.setObjectName("registerButton")
        self.register_button.clicked.connect(self.handle_register)
        panel_layout.addWidget(self.register_button)
        
        # 忘记密码链接
        forgot_label = QLabel('<a href="#" style="color: #2196F3; text-decoration: none;">忘记密码？</a>')
        forgot_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        forgot_label.setObjectName("linkLabel")
        forgot_label.linkActivated.connect(self.handle_forgot_password)
        panel_layout.addWidget(forgot_label)
        
        # 添加面板到容器
        container_layout.addWidget(login_panel, alignment=Qt.AlignmentFlag.AlignCenter)
        
        # 数据库连接状态
        self.db_status_label = QLabel("正在检查数据库连接...")
        self.db_status_label.setObjectName("statusLabel")
        self.db_status_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        container_layout.addWidget(self.db_status_label)
        
        # 添加底部间距
        container_layout.addStretch(1)
        
        # 欢迎标题
        welcome_label = QLabel("欢迎使用武汉总厂仓库管理系统")
        welcome_label.setObjectName("welcomeLabel")
        welcome_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        
        # 创建顶部布局
        top_layout = QHBoxLayout()
        top_layout.addWidget(welcome_label)
        top_layout.setContentsMargins(10, 0, 10, 0)
        
        # 创建顶部部件
        top_widget = QWidget()
        top_widget.setLayout(top_layout)
        top_widget.setFixedHeight(60)
        
        # 最终布局
        final_layout = QVBoxLayout()
        final_layout.setContentsMargins(0, 0, 0, 0)
        final_layout.addWidget(top_widget)
        final_layout.addWidget(login_container)
        
        right_widget = QWidget()
        right_widget.setLayout(final_layout)
        
        main_layout.addWidget(right_widget)
        
        # 绑定回车键
        self.username_input.returnPressed.connect(self.handle_login)
        self.password_input.returnPressed.connect(self.handle_login)
        
    def set_styles(self):
        """设置样式表"""
        style = """
        QMainWindow {
            background-color: transparent;
        }
        
        #loginContainer {
            background: transparent;
            border-radius: 15px;
        }
        
        #loginPanel {
            background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 rgba(255, 255, 255, 0.95),
                stop:1 rgba(240, 248, 255, 0.95));
            border-radius: 15px;
            border: 1px solid rgba(33, 150, 243, 0.3);
        }
        
        #titleLabel {
            font-size: 24px;
            font-weight: bold;
            color: #1976D2;
            margin-bottom: 10px;
        }
        
        #inputField {
            padding: 12px 15px;
            border: 2px solid #E3F2FD;
            border-radius: 8px;
            font-size: 14px;
            background-color: rgba(255, 255, 255, 0.9);
            color: #333;
        }
        
        #inputField:focus {
            border-color: #2196F3;
            background-color: white;
        }
        
        #loginButton {
            background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 #2196F3,
                stop:1 #1976D2);
            color: white;
            border: none;
            border-radius: 8px;
            padding: 12px;
            font-size: 16px;
            font-weight: bold;
        }
        
        #loginButton:hover {
            background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 #1976D2,
                stop:1 #1565C0);
        }
        
        #loginButton:pressed {
            background: #1565C0;
        }
        
        #registerButton {
            background: transparent;
            color: #2196F3;
            border: 2px solid #2196F3;
            border-radius: 8px;
            padding: 10px;
            font-size: 14px;
        }
        
        #registerButton:hover {
            background: rgba(33, 150, 243, 0.1);
        }
        
        #checkBox {
            color: #666;
            font-size: 13px;
        }
        
        #checkBox::indicator {
            width: 16px;
            height: 16px;
        }
        
        #checkBox::indicator:unchecked {
            border: 2px solid #ccc;
            border-radius: 3px;
            background-color: white;
        }
        
        #checkBox::indicator:checked {
            border: 2px solid #2196F3;
            border-radius: 3px;
            background-color: #2196F3;
            image: url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTIiIGhlaWdodD0iOSIgdmlld0JveD0iMCAwIDEyIDkiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxwYXRoIGQ9Ik0xIDQuNUw0LjUgOEwxMSAxIiBzdHJva2U9IndoaXRlIiBzdHJva2Utd2lkdGg9IjIiIHN0cm9rZS1saW5lY2FwPSJyb3VuZCIgc3Ryb2tlLWxpbmVqb2luPSJyb3VuZCIvPgo8L3N2Zz4K);
        }
        
        #linkLabel {
            color: #2196F3;
            font-size: 13px;
        }
        
        #statusLabel {
            color: #666;
            font-size: 12px;
            padding: 5px;
        }
        
        #welcomeLabel {
            font-size: 28px;
            font-weight: bold;
            color: #1976D2;
 
 
 
 
        }
        """
        self.setStyleSheet(style)
        
    def check_database_connection(self):
        """检查数据库连接状态"""
        try:
            if test_connection():
                self.db_status_label.setText("✅ 数据库连接正常")
                self.db_status_label.setStyleSheet("color: #4CAF50;")
            else:
                self.db_status_label.setText("❌ 数据库连接失败")
                self.db_status_label.setStyleSheet("color: #F44336;")
        except Exception as e:
            self.db_status_label.setText(f"❌ 数据库连接错误: {str(e)}")
            self.db_status_label.setStyleSheet("color: #F44336;")
            
    def handle_login(self):
        """处理登录"""
        username = self.username_input.text().strip()
        password = self.password_input.text().strip()
        
        if not username or not password:
            self.show_message("请输入用户名和密码", "error")
            return
            
        # 连接数据库验证用户
        try:
            from services.user_service import UserService
            
            user_service = UserService()
            user, message = user_service.authenticate_user(username, password)
            
            if user:
                self.show_message("登录成功！", "success")
                # 发送登录成功信号
                self.login_success.emit(username)
                # 延迟关闭窗口
                QTimer.singleShot(1000, self.close)
            else:
                self.show_message(message, "error")
                
        except Exception as e:
            self.show_message(f"登录验证失败: {str(e)}", "error")
            
    def handle_register(self):
        """处理注册"""
        from views.auth.register_window import RegisterWindow
        self.register_window = RegisterWindow()
        self.register_window.register_success.connect(self.on_register_success)
        self.register_window.show()
        
    def handle_forgot_password(self):
        """处理忘记密码"""
        from views.auth.forgot_password_window import ForgotPasswordWindow
        self.forgot_password_window = ForgotPasswordWindow()
        # 可以连接 forgot_password_window 的 password_reset_success 信号到登录窗口的某个槽函数
        # 例如，自动填充用户名并提示用户输入新密码登录
        self.forgot_password_window.show()
        
    def show_message(self, message, msg_type="info"):
        """显示消息"""
        colors = {
            "success": "#4CAF50",
            "error": "#F44336", 
            "info": "#2196F3",
            "warning": "#FF9800"
        }
        
        color = colors.get(msg_type, "#2196F3")
        self.db_status_label.setText(message)
        self.db_status_label.setStyleSheet(f"color: {color}; font-weight: bold;")
        
        # 3秒后恢复数据库状态显示
        QTimer.singleShot(3000, self.check_database_connection)
        
    def on_register_success(self, username):
        """注册成功回调"""
        self.show_message(f"用户 {username} 注册成功！", "success")
        
    def clear_password(self):
        """清除密码输入框"""
        self.password_input.clear()

def main():
    """主函数"""
    app = QApplication(sys.argv)
    
    # 设置应用程序属性
    app.setApplicationName("武汉总厂仓库管理系统")
    app.setApplicationVersion("1.0.0")
    
    # 创建登录窗口
    login_window = LoginWindow()
    
    # 连接登录成功信号到主窗口显示
    def on_login_success(username):
        print(f"✅ 用户 {username} 登录成功，准备显示主窗口")
        # 这里可以添加主窗口显示逻辑
        # 目前只是关闭登录窗口
        login_window.close()
    
    login_window.login_success.connect(on_login_success)
    login_window.show()
    
    return app.exec()

if __name__ == "__main__":
    sys.exit(main())