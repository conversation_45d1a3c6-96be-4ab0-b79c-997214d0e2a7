#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
注册窗口模块 - 重新设计版本
"""

import sys
import os
from PyQt6.QtWidgets import (QApplication, QMainWindow, QWidget, QVBoxLayout, 
                             QHBoxLayout, QGridLayout, QLabel, QLineEdit, QPushButton, 
                             QComboBox, QFrame, QGraphicsDropShadowEffect,
                             QMessageBox)
from PyQt6.QtCore import Qt, QTimer, pyqtSignal
from PyQt6.QtGui import QFont

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from config.database import test_connection

class RegisterWindow(QMainWindow):
    """注册窗口类 - 重新设计版本"""
    
    # 定义信号
    register_success = pyqtSignal(str)  # 注册成功信号
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("用户注册")
        self.setFixedSize(450, 700)  # 小窗口尺寸
        
        # 设置窗口居中
        self.center_window()
        
        # 初始化UI
        self.init_ui()
        
        # 设置样式
        self.set_styles()
        
    def center_window(self):
        """窗口居中显示"""
        screen = QApplication.primaryScreen().geometry()
        window = self.geometry()
        x = (screen.width() - window.width()) // 2
        y = (screen.height() - window.height()) // 2
        self.move(x, y)
        
    def init_ui(self):
        """初始化用户界面"""
        # 创建中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 创建主布局
        main_layout = QVBoxLayout(central_widget)
        main_layout.setContentsMargins(5, 5, 5, 5)
        main_layout.setSpacing(20)
        
        # 创建注册面板
        self.create_register_panel(main_layout)
        
    def create_register_panel(self, main_layout):
        """创建注册面板"""
        # 创建注册面板
        register_panel = QFrame()
        register_panel.setObjectName("registerPanel")
        
        # 添加阴影效果
        shadow = QGraphicsDropShadowEffect()
        shadow.setBlurRadius(15)
        shadow.setColor(Qt.GlobalColor.gray)
        shadow.setOffset(0, 3)
        register_panel.setGraphicsEffect(shadow)
        
        # 注册面板格栅布局
        panel_layout = QGridLayout(register_panel)
        panel_layout.setContentsMargins(30, 30, 30, 30)
        panel_layout.setSpacing(15)
        
        # 标题 - 跨越两列
        title_label = QLabel("用户注册")
        title_label.setObjectName("titleLabel")
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        panel_layout.addWidget(title_label, 0, 0, 1, 2)  # row, col, rowspan, colspan
        
        # 用户名 - 第1行
        username_label = QLabel("用户名:")
        username_label.setObjectName("fieldLabel")
        panel_layout.addWidget(username_label, 1, 0)
        
        self.username_input = QLineEdit()
        self.username_input.setPlaceholderText("请输入用户名")
        self.username_input.setObjectName("inputField")
        panel_layout.addWidget(self.username_input, 1, 1)
        
        # 真实姓名 - 第2行
        realname_label = QLabel("真实姓名:")
        realname_label.setObjectName("fieldLabel")
        panel_layout.addWidget(realname_label, 2, 0)
        
        self.realname_input = QLineEdit()
        self.realname_input.setPlaceholderText("请输入真实姓名")
        self.realname_input.setObjectName("inputField")
        panel_layout.addWidget(self.realname_input, 2, 1)
        
        # 密码 - 第3行
        password_label = QLabel("密码:")
        password_label.setObjectName("fieldLabel")
        panel_layout.addWidget(password_label, 3, 0)
        
        self.password_input = QLineEdit()
        self.password_input.setPlaceholderText("请输入密码")
        self.password_input.setEchoMode(QLineEdit.EchoMode.Password)
        self.password_input.setObjectName("inputField")
        panel_layout.addWidget(self.password_input, 3, 1)
        
        # 确认密码 - 第4行
        confirm_label = QLabel("确认密码:")
        confirm_label.setObjectName("fieldLabel")
        panel_layout.addWidget(confirm_label, 4, 0)
        
        self.confirm_password_input = QLineEdit()
        self.confirm_password_input.setPlaceholderText("请确认密码")
        self.confirm_password_input.setEchoMode(QLineEdit.EchoMode.Password)
        self.confirm_password_input.setObjectName("inputField")
        panel_layout.addWidget(self.confirm_password_input, 4, 1)
        
        # 邮箱地址 - 第5行
        email_label = QLabel("邮箱地址:")
        email_label.setObjectName("fieldLabel")
        panel_layout.addWidget(email_label, 5, 0)
        
        self.email_input = QLineEdit()
        self.email_input.setPlaceholderText("请输入邮箱地址")
        self.email_input.setObjectName("inputField")
        panel_layout.addWidget(self.email_input, 5, 1)
        
        # 电话号码 - 第6行
        phone_label = QLabel("电话号码:")
        phone_label.setObjectName("fieldLabel")
        panel_layout.addWidget(phone_label, 6, 0)
        
        self.phone_input = QLineEdit()
        self.phone_input.setPlaceholderText("请输入电话号码")
        self.phone_input.setObjectName("inputField")
        panel_layout.addWidget(self.phone_input, 6, 1)
        
        # 用户权限 - 第7行
        role_label = QLabel("用户权限:")
        role_label.setObjectName("fieldLabel")
        panel_layout.addWidget(role_label, 7, 0)
        
        self.role_combo = QComboBox()
        self.role_combo.setObjectName("comboBox")
        # 显示中文权限名称
        self.role_combo.addItem("查看者", "viewer")
        self.role_combo.addItem("操作员", "operator") 
        self.role_combo.addItem("管理员", "admin")
        self.role_combo.setCurrentIndex(0)  # 默认为查看者
        panel_layout.addWidget(self.role_combo, 7, 1)
        
        # 按钮布局 - 第8行，跨越两列
        button_layout = QHBoxLayout()
        button_layout.setSpacing(15)

         # 注册按钮
        self.register_button = QPushButton("注册")
        self.register_button.setObjectName("registerButton")
        self.register_button.clicked.connect(self.handle_register)
        button_layout.addWidget(self.register_button)

        # 返回登录按钮
        self.back_button = QPushButton("返回登录")
        self.back_button.setObjectName("backButton")
        self.back_button.clicked.connect(self.close)
        button_layout.addWidget(self.back_button)


        # 创建按钮容器部件
        button_widget = QWidget()
        button_widget.setLayout(button_layout)
        panel_layout.addWidget(button_widget, 8, 0, 1, 2)
        
        # 状态标签 - 第9行，跨越两列
        self.status_label = QLabel("")
        self.status_label.setObjectName("statusLabel")
        self.status_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        panel_layout.addWidget(self.status_label, 9, 0, 1, 2)
        
        # 设置列的拉伸比例
        panel_layout.setColumnStretch(0, 0)  # 标签列不拉伸
        panel_layout.setColumnStretch(1, 1)  # 输入框列可拉伸
        
        # 添加面板到主布局
        main_layout.addWidget(register_panel)
        
        # 绑定回车键
        self.username_input.returnPressed.connect(self.handle_register)
        self.realname_input.returnPressed.connect(self.handle_register)
        self.password_input.returnPressed.connect(self.handle_register)
        self.confirm_password_input.returnPressed.connect(self.handle_register)
        self.email_input.returnPressed.connect(self.handle_register)
        self.phone_input.returnPressed.connect(self.handle_register)
        
    def set_styles(self):
        """设置样式表"""
        style = """
        QMainWindow {
            background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 #E3F2FD,
                stop:1 #BBDEFB);
        }
        
        #registerPanel {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            border: 1px solid rgba(33, 150, 243, 0.3);
        }
        
        #titleLabel {
            font-size: 24px;
            font-weight: bold;
            color: #1976D2;
            margin-bottom: 10px;
            padding: 10px;
            border: none;
        }
        
        #fieldLabel {
            font-size: 14px;
            font-weight: bold;
            color: #424242;
            margin-bottom: 5px;
            border: none;
        }
        
        #inputField {
            padding: 12px 15px;
            border: none;
            border-radius: 8px;
            font-size: 14px;
            background-color: #F5F5F5;
            color: #333;
            margin-bottom: 5px;
        }
        
        #inputField:focus {
            background-color: white;
            border: 2px solid #2196F3;
        }
        
        #comboBox {
            padding: 12px 15px;
            border: none;
            border-radius: 8px;
            font-size: 14px;
            background-color: #F5F5F5;
            color: #333;
            margin-bottom: 5px;
        }
        
        #comboBox:focus {
            background-color: white;
            border: 2px solid #2196F3;
        }
        
        #comboBox::drop-down {
            border: none;
            border-radius: 4px;
        }
        
        #comboBox::down-arrow {
            image: none;
            border-left: 5px solid transparent;
            border-right: 5px solid transparent;
            border-top: 5px solid #666;
            margin-right: 10px;
        }
        
        #registerButton {
            background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 #2196F3,
                stop:1 #1976D2);
            color: white;
            border: none;
            border-radius: 8px;
            padding: 12px 20px;
            font-size: 16px;
            font-weight: bold;
            min-width: 100px;
        }
        
        #registerButton:hover {
            background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 #1976D2,
                stop:1 #1565C0);
        }
        
        #registerButton:pressed {
            background: #1565C0;
        }
        
        #backButton {
            background: transparent;
            color: #666;
            border: 2px solid #CCC;
            border-radius: 8px;
            padding: 12px 20px;
            font-size: 14px;
            min-width: 100px;
        }
        
        #backButton:hover {
            background: rgba(0, 0, 0, 0.05);
            border-color: #999;
        }
        
        #statusLabel {
            color: #666;
            font-size: 12px;
            padding: 5px;
            margin-top: 10px;
            border: none;
        }
        """
        self.setStyleSheet(style)
        
    def handle_register(self):
        """处理注册"""
        username = self.username_input.text().strip()
        realname = self.realname_input.text().strip()
        password = self.password_input.text().strip()
        confirm_password = self.confirm_password_input.text().strip()
        email = self.email_input.text().strip()
        phone = self.phone_input.text().strip()
        role_data = self.role_combo.currentData()  # 获取英文值
        role_text = self.role_combo.currentText()  # 获取中文显示
        
        # 验证输入
        if not all([username, realname, password, confirm_password]):
            self.show_message("请填写所有必填字段", "error")
            return
            
        if password != confirm_password:
            self.show_message("两次输入的密码不一致", "error")
            return
            
        if len(password) < 6:
            self.show_message("密码长度至少6位", "error")
            return
            
        # 验证邮箱格式（如果填写了）
        if email and '@' not in email:
            self.show_message("请输入正确的邮箱地址", "error")
            return
            
        # 验证手机号格式（如果填写了）
        if phone and (len(phone) != 11 or not phone.isdigit()):
            self.show_message("请输入正确的11位手机号码", "error")
            return
        
        # 连接数据库进行注册
        try:
            from services.user_service import UserService
            
            user_service = UserService()
            new_user = user_service.register_user(
                username=username,
                password=password,
                role=role_data,  # 使用英文值
                real_name=realname,
                email=email if email else None,
                phone=phone if phone else None
            )
            
            self.show_message(f"注册成功！用户: {username}, 权限: {role_text}", "success")
            # 发送注册成功信号
            self.register_success.emit(username)
            # 延迟关闭窗口
            QTimer.singleShot(2000, self.close)
            
        except ValueError as e:
            self.show_message(str(e), "error")
        except Exception as e:
            self.show_message(f"注册失败: {str(e)}", "error")
            
    def show_message(self, message, msg_type="info"):
        """显示消息"""
        colors = {
            "success": "#4CAF50",
            "error": "#F44336", 
            "info": "#2196F3",
            "warning": "#FF9800"
        }
        
        color = colors.get(msg_type, "#2196F3")
        self.status_label.setText(message)
        self.status_label.setStyleSheet(f"color: {color}; font-weight: bold; font-size: 12px; padding: 5px; margin-top: 10px; border: none;")

def main():
    """主函数"""
    app = QApplication(sys.argv)
    
    # 设置应用程序属性
    app.setApplicationName("武汉总厂仓库管理系统")
    app.setApplicationVersion("1.0.0")
    
    # 创建注册窗口
    register_window = RegisterWindow()
    
    # 连接注册成功信号
    def on_register_success(username):
        print(f"✅ 用户 {username} 注册成功")
    
    register_window.register_success.connect(on_register_success)
    register_window.show()
    
    return app.exec()

if __name__ == "__main__":
    sys.exit(main()) 