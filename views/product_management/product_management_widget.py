#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
商品管理主组件
包含商品管理、分类管理、供应商管理
支持商品信息维护、分类体系管理、供应商档案管理等功能
"""

import os
import sys
from typing import Dict, Any, List
from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QTabWidget, QTableWidget, 
    QTableWidgetItem, QPushButton, QLineEdit, QComboBox, QLabel,
    QDialog, QFormLayout, QTextEdit, QSpinBox, QDoubleSpinBox,
    QMessageBox, QHeaderView, QFrame, QGridLayout, QScrollArea,
    QFileDialog, QGroupBox, QCheckBox
)
from PyQt6.QtCore import Qt, pyqtSignal, QTimer
from PyQt6.QtGui import QFont, QPixmap, QIcon

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from controllers.product_controller import ProductController
from views.product_management.category_delete_dialog import CategoryDeleteDialog

class ProductManagementWidget(QWidget):
    """商品管理主组件

    功能模块：
    - 商品管理：商品信息的增删改查、批量操作
    - 分类管理：商品分类体系的维护和管理
    - 供应商管理：供应商档案的建立和维护
    - 数据导入导出：支持Excel格式的批量数据处理
    - 搜索筛选：多条件组合搜索和数据筛选
    """
    
    def __init__(self):
        super().__init__()
        self.controller = ProductController()
        self.current_page = 1
        self.page_size = 50
        self.total_pages = 1
        
        self.init_ui()
        self.connect_signals()
        self.load_initial_data()
    
    def init_ui(self):
        """初始化界面"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(5, 5, 5, 5)
        layout.setSpacing(15)
        
        # 创建标签页
        self.tab_widget = QTabWidget()
        self.tab_widget.setObjectName("subTabWidget")
        
        # 商品管理标签页
        self.create_product_tab()
        
        # 分类管理标签页
        self.create_category_tab()
        
        # 供应商管理标签页
        self.create_supplier_tab()
        
        layout.addWidget(self.tab_widget)
        
        # 设置样式
        self.set_styles()
    
    def create_product_tab(self):
        """创建商品管理标签页"""
        product_widget = QWidget()
        layout = QVBoxLayout(product_widget)
        layout.setContentsMargins(5, 5, 5, 5)
        layout.setSpacing(15)
        
        # 合并搜索和操作按钮为一行
        top_frame = self.create_product_top_frame()
        layout.addWidget(top_frame)
        
        # 商品列表表格
        self.product_table = self.create_product_table()
        layout.addWidget(self.product_table)
        
        # 分页控件
        pagination_frame = self.create_pagination_frame()
        layout.addWidget(pagination_frame)
        
        self.tab_widget.addTab(product_widget, "商品管理")
    
    def create_product_top_frame(self):
        """创建顶部框架，包含搜索和操作按钮"""
        frame = QFrame()
        frame.setObjectName("topFrame")
        layout = QHBoxLayout(frame)
        layout.setContentsMargins(5, 5, 5, 5)
        layout.setSpacing(10)
        
        # 搜索区域
        search_group = self.create_search_group()
        layout.addWidget(search_group)
        
        # 分隔线
        separator = QFrame()
        separator.setFrameShape(QFrame.Shape.VLine)
        separator.setFrameShadow(QFrame.Shadow.Sunken)
        layout.addWidget(separator)
        
        # 操作按钮区域
        button_group = self.create_button_group()
        layout.addWidget(button_group)
        
        return frame
    
    def create_search_group(self):
        """创建搜索组"""
        group = QFrame()
        layout = QHBoxLayout(group)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(8)
        
        # 搜索框
        layout.addWidget(QLabel("搜索:"))
        self.search_edit = QLineEdit()
        self.search_edit.setPlaceholderText("输入商品编码、名称或型号")
        self.search_edit.setObjectName("searchEdit")
        self.search_edit.setMaximumWidth(200)
        layout.addWidget(self.search_edit)
        
        # 分类筛选
        layout.addWidget(QLabel("分类:"))
        self.category_combo = QComboBox()
        self.category_combo.setObjectName("categoryCombo")
        self.category_combo.setMaximumWidth(120)
        layout.addWidget(self.category_combo)
        
        # 供应商筛选
        layout.addWidget(QLabel("供应商:"))
        self.supplier_combo = QComboBox()
        self.supplier_combo.setObjectName("supplierCombo")
        self.supplier_combo.setMaximumWidth(120)
        layout.addWidget(self.supplier_combo)
        
        # 搜索按钮
        search_btn = QPushButton("搜索")
        search_btn.setObjectName("searchButton")
        search_btn.clicked.connect(self.search_products)
        layout.addWidget(search_btn)
        
        # 重置按钮
        reset_btn = QPushButton("重置")
        reset_btn.setObjectName("resetButton")
        reset_btn.clicked.connect(self.reset_search)
        layout.addWidget(reset_btn)
        
        return group
    
    def create_button_group(self):
        """创建操作按钮组"""
        group = QFrame()
        layout = QHBoxLayout(group)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(8)
        
        # 添加商品按钮
        add_btn = QPushButton("添加商品")
        add_btn.setObjectName("addButton")
        add_btn.clicked.connect(self.add_product)
        layout.addWidget(add_btn)
        
        # 编辑商品按钮
        self.edit_btn = QPushButton("编辑商品")
        self.edit_btn.setObjectName("editButton")
        self.edit_btn.clicked.connect(self.edit_product)
        self.edit_btn.setEnabled(False)
        layout.addWidget(self.edit_btn)
        
        # 删除商品按钮
        self.delete_btn = QPushButton("删除商品")
        self.delete_btn.setObjectName("deleteButton")
        self.delete_btn.clicked.connect(self.delete_product)
        self.delete_btn.setEnabled(False)
        layout.addWidget(self.delete_btn)
        
        # 刷新按钮
        refresh_btn = QPushButton("刷新")
        refresh_btn.setObjectName("refreshButton")
        refresh_btn.clicked.connect(self.refresh_products)
        layout.addWidget(refresh_btn)
        
        layout.addStretch()
        
        return group
    
    def create_product_search_frame(self):
        """创建商品搜索框架"""
        frame = QFrame()
        frame.setObjectName("searchFrame")
        layout = QHBoxLayout(frame)
        layout.setContentsMargins(5, 5, 5, 5)
        
        # 搜索框
        layout.addWidget(QLabel("搜索:"))
        self.search_edit = QLineEdit()
        self.search_edit.setPlaceholderText("输入商品编码、名称或型号")
        self.search_edit.setObjectName("searchEdit")
        layout.addWidget(self.search_edit)
        
        # 分类筛选
        layout.addWidget(QLabel("分类:"))
        self.category_combo = QComboBox()
        self.category_combo.setObjectName("categoryCombo")
        layout.addWidget(self.category_combo)
        
        # 供应商筛选
        layout.addWidget(QLabel("供应商:"))
        self.supplier_combo = QComboBox()
        self.supplier_combo.setObjectName("supplierCombo")
        layout.addWidget(self.supplier_combo)
        
        # 搜索按钮
        search_btn = QPushButton("搜索")
        search_btn.setObjectName("searchButton")
        search_btn.clicked.connect(self.search_products)
        layout.addWidget(search_btn)
        
        # 重置按钮
        reset_btn = QPushButton("重置")
        reset_btn.setObjectName("resetButton")
        reset_btn.clicked.connect(self.reset_search)
        layout.addWidget(reset_btn)
        
        layout.addStretch()
        return frame
    
    def create_product_button_frame(self):
        """创建商品操作按钮框架"""
        frame = QFrame()
        frame.setObjectName("buttonFrame")
        layout = QHBoxLayout(frame)
        layout.setContentsMargins(5, 5, 5, 5)
        
        # 添加商品按钮
        add_btn = QPushButton("添加商品")
        add_btn.setObjectName("addButton")
        add_btn.clicked.connect(self.add_product)
        layout.addWidget(add_btn)
        
        # 编辑商品按钮
        self.edit_btn = QPushButton("编辑商品")
        self.edit_btn.setObjectName("editButton")
        self.edit_btn.clicked.connect(self.edit_product)
        self.edit_btn.setEnabled(False)
        layout.addWidget(self.edit_btn)
        
        # 删除商品按钮
        self.delete_btn = QPushButton("删除商品")
        self.delete_btn.setObjectName("deleteButton")
        self.delete_btn.clicked.connect(self.delete_product)
        self.delete_btn.setEnabled(False)
        layout.addWidget(self.delete_btn)
        
        layout.addStretch()
        
        # 刷新按钮
        refresh_btn = QPushButton("刷新")
        refresh_btn.setObjectName("refreshButton")
        refresh_btn.clicked.connect(self.refresh_products)
        layout.addWidget(refresh_btn)
        
        return frame
    
    def create_product_table(self):
        """创建商品列表表格"""
        table = QTableWidget()
        table.setObjectName("productTable")
        
        # 设置列
        headers = ["ID", "商品编码", "商品名称", "型号", "分类种类", "分类名称", "供应商", "单位", "单价", "最小库存", "状态"]
        table.setColumnCount(len(headers))
        table.setHorizontalHeaderLabels(headers)
        
        # 设置表格属性
        table.setSelectionBehavior(QTableWidget.SelectionBehavior.SelectRows)
        table.setSelectionMode(QTableWidget.SelectionMode.SingleSelection)
        table.setAlternatingRowColors(True)
        table.setSortingEnabled(True)
        
        # 设置列宽
        header = table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.ResizeMode.Fixed)  # ID
        header.setSectionResizeMode(1, QHeaderView.ResizeMode.Fixed)  # 编码
        header.setSectionResizeMode(2, QHeaderView.ResizeMode.Fixed)  # 名称
        header.setSectionResizeMode(3, QHeaderView.ResizeMode.Stretch)  # 型号
        header.setSectionResizeMode(4, QHeaderView.ResizeMode.Fixed)  # 分类种类
        header.setSectionResizeMode(5, QHeaderView.ResizeMode.Fixed)  # 分类名称
        header.setSectionResizeMode(6, QHeaderView.ResizeMode.Fixed)  # 供应商
        header.setSectionResizeMode(7, QHeaderView.ResizeMode.Fixed)  # 单位
        header.setSectionResizeMode(8, QHeaderView.ResizeMode.Fixed)  # 单价
        header.setSectionResizeMode(9, QHeaderView.ResizeMode.Fixed)  # 最小库存
        header.setSectionResizeMode(10, QHeaderView.ResizeMode.Fixed)  # 状态
        
        table.setColumnWidth(0, 60)   # ID
        table.setColumnWidth(1, 120)  # 编码
        table.setColumnWidth(2, 160)  # 名称
        table.setColumnWidth(4, 80)   # 分类种类
        table.setColumnWidth(5, 120)  # 分类名称
        table.setColumnWidth(6, 120)  # 供应商
        table.setColumnWidth(7, 60)   # 单位
        table.setColumnWidth(8, 80)   # 单价
        table.setColumnWidth(9, 80)   # 最小库存
        table.setColumnWidth(10, 60)  # 状态
        
        # 连接选择信号
        table.itemSelectionChanged.connect(self.on_product_selection_changed)
        table.itemDoubleClicked.connect(self.edit_product)
        
        return table
    
    def create_pagination_frame(self):
        """创建分页控件框架"""
        frame = QFrame()
        frame.setObjectName("paginationFrame")
        layout = QHBoxLayout(frame)
        layout.setContentsMargins(5, 5, 5, 5)
        
        # 页面信息
        self.page_info_label = QLabel("第 1 页，共 1 页")
        self.page_info_label.setObjectName("pageInfoLabel")
        layout.addWidget(self.page_info_label)
        
        layout.addStretch()
        
        # 分页按钮
        self.first_btn = QPushButton("首页")
        self.first_btn.setObjectName("pageButton")
        self.first_btn.clicked.connect(lambda: self.go_to_page(1))
        layout.addWidget(self.first_btn)
        
        self.prev_btn = QPushButton("上一页")
        self.prev_btn.setObjectName("pageButton")
        self.prev_btn.clicked.connect(self.prev_page)
        layout.addWidget(self.prev_btn)
        
        self.next_btn = QPushButton("下一页")
        self.next_btn.setObjectName("pageButton")
        self.next_btn.clicked.connect(self.next_page)
        layout.addWidget(self.next_btn)
        
        self.last_btn = QPushButton("末页")
        self.last_btn.setObjectName("pageButton")
        self.last_btn.clicked.connect(lambda: self.go_to_page(self.total_pages))
        layout.addWidget(self.last_btn)
        
        return frame
    
    def create_category_tab(self):
        """创建分类管理标签页"""
        category_widget = QWidget()
        layout = QVBoxLayout(category_widget)
        layout.setContentsMargins(5, 5, 5, 5)
        layout.setSpacing(15)
        
        # 操作按钮
        button_layout = QHBoxLayout()
        
        add_category_btn = QPushButton("添加分类")
        add_category_btn.setObjectName("addButton")
        add_category_btn.clicked.connect(self.add_category)
        button_layout.addWidget(add_category_btn)
        
        self.edit_category_btn = QPushButton("编辑分类")
        self.edit_category_btn.setObjectName("editButton")
        self.edit_category_btn.clicked.connect(self.edit_category)
        self.edit_category_btn.setEnabled(False)
        button_layout.addWidget(self.edit_category_btn)
        
        self.delete_category_btn = QPushButton("删除分类")
        self.delete_category_btn.setObjectName("deleteButton")
        self.delete_category_btn.clicked.connect(self.delete_category)
        self.delete_category_btn.setEnabled(False)
        button_layout.addWidget(self.delete_category_btn)
        
        button_layout.addStretch()
        layout.addLayout(button_layout)
        
        # 分类列表表格
        self.category_table = QTableWidget()
        self.category_table.setObjectName("categoryTable")
        
        headers = ["ID", "分类编码", "分类名称", "分类种类", "描述", "状态"]
        self.category_table.setColumnCount(len(headers))
        self.category_table.setHorizontalHeaderLabels(headers)
        
        self.category_table.setSelectionBehavior(QTableWidget.SelectionBehavior.SelectRows)
        self.category_table.setSelectionMode(QTableWidget.SelectionMode.SingleSelection)
        self.category_table.setAlternatingRowColors(True)
        
        # 设置列宽
        header = self.category_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.ResizeMode.Fixed)
        header.setSectionResizeMode(1, QHeaderView.ResizeMode.Fixed)
        header.setSectionResizeMode(2, QHeaderView.ResizeMode.Stretch)
        header.setSectionResizeMode(3, QHeaderView.ResizeMode.Fixed)
        header.setSectionResizeMode(4, QHeaderView.ResizeMode.Stretch)
        header.setSectionResizeMode(5, QHeaderView.ResizeMode.Fixed)
        
        self.category_table.setColumnWidth(0, 60)
        self.category_table.setColumnWidth(1, 120)
        self.category_table.setColumnWidth(3, 80)
        self.category_table.setColumnWidth(5, 80)
        
        self.category_table.itemSelectionChanged.connect(self.on_category_selection_changed)
        self.category_table.itemDoubleClicked.connect(self.edit_category)
        
        layout.addWidget(self.category_table)
        
        self.tab_widget.addTab(category_widget, "分类管理")
    
    def create_supplier_tab(self):
        """创建供应商管理标签页"""
        supplier_widget = QWidget()
        layout = QVBoxLayout(supplier_widget)
        layout.setContentsMargins(5, 5, 5, 5)
        layout.setSpacing(15)
        
        # 操作按钮
        button_layout = QHBoxLayout()
        
        add_supplier_btn = QPushButton("添加供应商")
        add_supplier_btn.setObjectName("addButton")
        add_supplier_btn.clicked.connect(self.add_supplier)
        button_layout.addWidget(add_supplier_btn)
        
        self.edit_supplier_btn = QPushButton("编辑供应商")
        self.edit_supplier_btn.setObjectName("editButton")
        self.edit_supplier_btn.clicked.connect(self.edit_supplier)
        self.edit_supplier_btn.setEnabled(False)
        button_layout.addWidget(self.edit_supplier_btn)
        
        self.delete_supplier_btn = QPushButton("删除供应商")
        self.delete_supplier_btn.setObjectName("deleteButton")
        self.delete_supplier_btn.clicked.connect(self.delete_supplier)
        self.delete_supplier_btn.setEnabled(False)
        button_layout.addWidget(self.delete_supplier_btn)
        
        button_layout.addStretch()
        layout.addLayout(button_layout)
        
        # 供应商列表表格
        self.supplier_table = QTableWidget()
        self.supplier_table.setObjectName("supplierTable")
        
        headers = ["ID", "供应商名称", "联系人", "电话", "邮箱", "地址", "状态"]
        self.supplier_table.setColumnCount(len(headers))
        self.supplier_table.setHorizontalHeaderLabels(headers)
        
        self.supplier_table.setSelectionBehavior(QTableWidget.SelectionBehavior.SelectRows)
        self.supplier_table.setSelectionMode(QTableWidget.SelectionMode.SingleSelection)
        self.supplier_table.setAlternatingRowColors(True)
        
        # 设置列宽
        header = self.supplier_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.ResizeMode.Fixed)
        header.setSectionResizeMode(1, QHeaderView.ResizeMode.Fixed)
        header.setSectionResizeMode(2, QHeaderView.ResizeMode.Fixed)
        header.setSectionResizeMode(3, QHeaderView.ResizeMode.Fixed)
        header.setSectionResizeMode(4, QHeaderView.ResizeMode.Fixed)
        header.setSectionResizeMode(5, QHeaderView.ResizeMode.Stretch)
        header.setSectionResizeMode(6, QHeaderView.ResizeMode.Fixed)
        
        self.supplier_table.setColumnWidth(0, 60)
        self.supplier_table.setColumnWidth(1, 150)
        self.supplier_table.setColumnWidth(2, 100)
        self.supplier_table.setColumnWidth(3, 120)
        self.supplier_table.setColumnWidth(4, 150)
        self.supplier_table.setColumnWidth(6, 80)
        
        self.supplier_table.itemSelectionChanged.connect(self.on_supplier_selection_changed)
        self.supplier_table.itemDoubleClicked.connect(self.edit_supplier)
        
        layout.addWidget(self.supplier_table)
        
        self.tab_widget.addTab(supplier_widget, "供应商管理")
    
    def connect_signals(self):
        """连接信号"""
        # 控制器信号
        self.controller.product_created.connect(self.on_product_created)
        self.controller.product_updated.connect(self.on_product_updated)
        self.controller.product_deleted.connect(self.on_product_deleted)
        self.controller.category_created.connect(self.on_category_created)
        self.controller.category_updated.connect(self.on_category_updated)
        self.controller.category_deleted.connect(self.on_category_deleted)
        self.controller.supplier_created.connect(self.on_supplier_created)
        self.controller.supplier_updated.connect(self.on_supplier_updated)
        self.controller.supplier_deleted.connect(self.on_supplier_deleted)
        self.controller.error_occurred.connect(self.show_error)
        
        # 搜索框回车事件
        self.search_edit.returnPressed.connect(self.search_products)
    
    def load_initial_data(self):
        """加载初始数据"""
        self.load_categories()
        self.load_suppliers()
        self.load_products()
        self.load_category_list()
        self.load_supplier_list()
    
    def load_categories(self):
        """加载分类下拉框"""
        categories = self.controller.get_categories()
        
        self.category_combo.clear()
        self.category_combo.addItem("全部分类", None)
        
        for category in categories:
            self.category_combo.addItem(category['name'], category['id'])
    
    def load_suppliers(self):
        """加载供应商下拉框"""
        suppliers = self.controller.get_suppliers()
        
        self.supplier_combo.clear()
        self.supplier_combo.addItem("全部供应商", None)
        
        for supplier in suppliers:
            self.supplier_combo.addItem(supplier['name'], supplier['id'])
    
    def load_products(self):
        """加载商品列表"""
        # 获取搜索条件
        search = self.search_edit.text().strip() if self.search_edit.text() else None
        category_id = self.category_combo.currentData()
        supplier_id = self.supplier_combo.currentData()
        
        # 获取商品数据
        result = self.controller.get_products(
            page=self.current_page,
            page_size=self.page_size,
            search=search,
            category_id=category_id,
            supplier_id=supplier_id
        )
        
        products = result['products']
        self.total_pages = result['total_pages']
        
        # 更新表格
        self.product_table.setRowCount(len(products))
        
        for row, product in enumerate(products):
            self.product_table.setItem(row, 0, QTableWidgetItem(str(product['id'])))
            self.product_table.setItem(row, 1, QTableWidgetItem(product['code'] or ''))
            self.product_table.setItem(row, 2, QTableWidgetItem(product['name'] or ''))
            self.product_table.setItem(row, 3, QTableWidgetItem(product['model'] or ''))
            self.product_table.setItem(row, 4, QTableWidgetItem(product.get('category_type', '') or ''))
            self.product_table.setItem(row, 5, QTableWidgetItem(product['category_name'] or ''))
            self.product_table.setItem(row, 6, QTableWidgetItem(product['supplier_name'] or ''))
            self.product_table.setItem(row, 7, QTableWidgetItem(product['unit'] or ''))
            self.product_table.setItem(row, 8, QTableWidgetItem(f"{product['standard_price']:.2f}" if product.get('standard_price') else ''))
            self.product_table.setItem(row, 9, QTableWidgetItem(str(product['min_stock'] or 0)))
            self.product_table.setItem(row, 10, QTableWidgetItem("正常" if product['is_active'] else "停用"))
        
        # 更新分页信息
        self.update_pagination_info()
    
    def load_category_list(self):
        """加载分类列表"""
        categories = self.controller.get_categories()
        
        self.category_table.setRowCount(len(categories))
        
        for row, category in enumerate(categories):
            self.category_table.setItem(row, 0, QTableWidgetItem(str(category['id'])))
            self.category_table.setItem(row, 1, QTableWidgetItem(category['code'] or ''))
            self.category_table.setItem(row, 2, QTableWidgetItem(category['name'] or ''))
            self.category_table.setItem(row, 3, QTableWidgetItem(category['type'] or ''))
            self.category_table.setItem(row, 4, QTableWidgetItem(category['description'] or ''))
            self.category_table.setItem(row, 5, QTableWidgetItem("正常" if category['is_active'] else "停用"))
    
    def load_supplier_list(self):
        """加载供应商列表"""
        suppliers = self.controller.get_suppliers()
        
        self.supplier_table.setRowCount(len(suppliers))
        
        for row, supplier in enumerate(suppliers):
            self.supplier_table.setItem(row, 0, QTableWidgetItem(str(supplier['id'])))
            self.supplier_table.setItem(row, 1, QTableWidgetItem(supplier['name'] or ''))
            self.supplier_table.setItem(row, 2, QTableWidgetItem(supplier['contact_person'] or ''))
            self.supplier_table.setItem(row, 3, QTableWidgetItem(supplier['phone'] or ''))
            self.supplier_table.setItem(row, 4, QTableWidgetItem(supplier['email'] or ''))
            self.supplier_table.setItem(row, 5, QTableWidgetItem(supplier['address'] or ''))
            self.supplier_table.setItem(row, 6, QTableWidgetItem("正常" if supplier['is_active'] else "停用"))
    
    def update_pagination_info(self):
        """更新分页信息"""
        self.page_info_label.setText(f"第 {self.current_page} 页，共 {self.total_pages} 页")
        
        # 更新按钮状态
        self.first_btn.setEnabled(self.current_page > 1)
        self.prev_btn.setEnabled(self.current_page > 1)
        self.next_btn.setEnabled(self.current_page < self.total_pages)
        self.last_btn.setEnabled(self.current_page < self.total_pages)
    
    # ==================== 商品管理事件 ====================
    
    def search_products(self):
        """搜索商品"""
        self.current_page = 1
        self.load_products()
    
    def reset_search(self):
        """重置搜索"""
        self.search_edit.clear()
        self.category_combo.setCurrentIndex(0)
        self.supplier_combo.setCurrentIndex(0)
        self.current_page = 1
        self.load_products()
    
    def refresh_products(self):
        """刷新商品列表"""
        self.load_products()
    
    def add_product(self):
        """添加商品"""
        dialog = ProductDialog(self, self.controller)
        dialog.product_created.connect(self.on_product_created)
        dialog.exec()
    
    def edit_product(self):
        """编辑商品"""
        current_row = self.product_table.currentRow()
        if current_row >= 0:
            product_id = int(self.product_table.item(current_row, 0).text())
            product = self.controller.get_product_by_id(product_id)
            if product:
                dialog = ProductDialog(self, self.controller, product)
                dialog.product_updated.connect(self.on_product_updated)
                dialog.exec()
    
    def delete_product(self):
        """删除商品"""
        current_row = self.product_table.currentRow()
        if current_row >= 0:
            product_name = self.product_table.item(current_row, 2).text()
            reply = QMessageBox.question(
                self, "确认删除", 
                f"确定要删除商品 '{product_name}' 吗？",
                QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
            )
            
            if reply == QMessageBox.StandardButton.Yes:
                product_id = int(self.product_table.item(current_row, 0).text())
                try:
                    result = self.controller.delete_product(product_id)
                    if result:
                        self.on_product_deleted(product_id)
                except Exception as e:
                    QMessageBox.critical(self, "错误", f"删除失败: {str(e)}")
    
    def on_product_selection_changed(self):
        """商品选择改变"""
        has_selection = self.product_table.currentRow() >= 0
        self.edit_btn.setEnabled(has_selection)
        self.delete_btn.setEnabled(has_selection)
    
    # ==================== 分页事件 ====================
    
    def prev_page(self):
        """上一页"""
        if self.current_page > 1:
            self.current_page -= 1
            self.load_products()
    
    def next_page(self):
        """下一页"""
        if self.current_page < self.total_pages:
            self.current_page += 1
            self.load_products()
    
    def go_to_page(self, page):
        """跳转到指定页"""
        if 1 <= page <= self.total_pages:
            self.current_page = page
            self.load_products()
    
    # ==================== 分类管理事件 ====================
    
    def add_category(self):
        """添加分类"""
        dialog = CategoryDialog(self, self.controller)
        if dialog.exec() == QDialog.DialogCode.Accepted:
            pass  # 信号会自动处理
    
    def edit_category(self):
        """编辑分类"""
        current_row = self.category_table.currentRow()
        if current_row >= 0:
            category_id = int(self.category_table.item(current_row, 0).text())
            category = self.controller.get_category_by_id(category_id)
            if category:
                dialog = CategoryDialog(self, self.controller, category)
                if dialog.exec() == QDialog.DialogCode.Accepted:
                    pass  # 信号会自动处理
    
    def delete_category(self):
        """删除分类"""
        current_row = self.category_table.currentRow()
        if current_row >= 0:
            category_name = self.category_table.item(current_row, 2).text()
            category_id = int(self.category_table.item(current_row, 0).text())
            
            # 创建自定义删除确认对话框
            dialog = CategoryDeleteDialog(self, category_name)
            result = dialog.exec()
            
            if result == QDialog.DialogCode.Accepted:
                delete_type = dialog.get_delete_type()
                
                if delete_type == "force":
                    # 强制删除（物理删除）
                    success = self.controller.delete_category(category_id, force_delete=True)
                elif delete_type == "soft":
                    # 软删除（禁用）
                    success = self.controller.soft_delete_category(category_id)
                else:
                    # 普通删除
                    success = self.controller.delete_category(category_id, force_delete=False)
    
    def on_category_selection_changed(self):
        """分类选择改变"""
        has_selection = self.category_table.currentRow() >= 0
        self.edit_category_btn.setEnabled(has_selection)
        self.delete_category_btn.setEnabled(has_selection)
    
    # ==================== 供应商管理事件 ====================
    
    def add_supplier(self):
        """添加供应商"""
        dialog = SupplierDialog(self, self.controller)
        if dialog.exec() == QDialog.DialogCode.Accepted:
            # 手动刷新供应商列表
            self.load_supplier_list()
            self.load_suppliers()
    
    def edit_supplier(self):
        """编辑供应商"""
        current_row = self.supplier_table.currentRow()
        if current_row >= 0:
            supplier_id = int(self.supplier_table.item(current_row, 0).text())
            supplier = self.controller.get_supplier_by_id(supplier_id)
            if supplier:
                dialog = SupplierDialog(self, self.controller, supplier)
                if dialog.exec() == QDialog.DialogCode.Accepted:
                    # 手动刷新供应商列表
                    self.load_supplier_list()
                    self.load_suppliers()
    
    def delete_supplier(self):
        """删除供应商"""
        current_row = self.supplier_table.currentRow()
        if current_row >= 0:
            supplier_name = self.supplier_table.item(current_row, 1).text()
            reply = QMessageBox.question(
                self, "确认删除", 
                f"确定要删除供应商 '{supplier_name}' 吗？",
                QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
            )
            
            if reply == QMessageBox.StandardButton.Yes:
                supplier_id = int(self.supplier_table.item(current_row, 0).text())
                try:
                    result = self.controller.delete_supplier(supplier_id)
                    if result:
                        # 删除成功，手动刷新列表
                        self.load_supplier_list()
                        self.load_suppliers()
                        QMessageBox.information(self, "成功", "供应商删除成功！")
                    else:
                        QMessageBox.warning(self, "删除失败", "供应商删除失败，请检查是否有关联数据")
                except Exception as e:
                    QMessageBox.critical(self, "错误", f"删除失败: {str(e)}")
    
    def on_supplier_selection_changed(self):
        """供应商选择改变"""
        has_selection = self.supplier_table.currentRow() >= 0
        self.edit_supplier_btn.setEnabled(has_selection)
        self.delete_supplier_btn.setEnabled(has_selection)
    
    # ==================== 信号处理 ====================
    
    def on_product_created(self, product):
        """商品创建成功"""
        QMessageBox.information(self, "成功", "商品创建成功！")
        self.load_products()
        self.load_categories()  # 刷新分类下拉框
    
    def on_product_updated(self, product):
        """商品更新成功"""
        QMessageBox.information(self, "成功", "商品更新成功！")
        self.load_products()
    
    def on_product_deleted(self, product_id):
        """商品删除成功"""
        QMessageBox.information(self, "成功", "商品删除成功！")
        self.load_products()
    
    def on_category_created(self, category):
        """分类创建成功"""
        QMessageBox.information(self, "成功", "分类创建成功！")
        self.load_category_list()
        self.load_categories()  # 刷新下拉框
    
    def on_category_updated(self, category):
        """分类更新成功"""
        QMessageBox.information(self, "成功", "分类更新成功！")
        self.load_category_list()
        self.load_categories()  # 刷新下拉框
    
    def on_category_deleted(self, category_id):
        """分类删除成功"""
        QMessageBox.information(self, "成功", "分类删除成功！")
        self.load_category_list()
        self.load_categories()  # 刷新下拉框
    
    def on_supplier_created(self, supplier):
        """供应商创建成功"""
        QMessageBox.information(self, "成功", "供应商创建成功！")
        self.load_supplier_list()
        self.load_suppliers()  # 刷新下拉框
    
    def on_supplier_updated(self, supplier):
        """供应商更新成功"""
        QMessageBox.information(self, "成功", "供应商更新成功！")
        self.load_supplier_list()
        self.load_suppliers()  # 刷新下拉框
    
    def on_supplier_deleted(self, supplier_id):
        """供应商删除成功"""
        QMessageBox.information(self, "成功", "供应商删除成功！")
        self.load_supplier_list()
        self.load_suppliers()  # 刷新下拉框
    
    def show_error(self, message):
        """显示错误信息"""
        QMessageBox.critical(self, "错误", message)
    
    def set_styles(self):
        """设置样式"""
        # 使用统一样式
        from styles.unified_styles import get_unified_stylesheet
        unified_style = get_unified_stylesheet()
        
        # 仅添加当前模块特有的样式
        product_style = """
            /* 框架样式 */
            #searchFrame, #buttonFrame, #paginationFrame, #topFrame {
                background: white;
                border: 1px solid #E0E0E0;
                border-radius: 8px;
                margin: 5px 0;
            }
            
            /* 顶部框架特殊样式 */
            #topFrame {
                padding: 10px;
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #FFFFFF, stop:1 #F8F9FA);
                border: 1px solid #DEE2E6;
                border-radius: 10px;
                margin: 5px 0;
            }
            
            /* 分隔线样式 */
            QFrame[frameShape="5"] {  /* VLine */
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #E0E0E0, stop:1 #CCCCCC);
                max-width: 2px;
                margin: 8px 12px;
                border-radius: 1px;
            }
            
            /* 下拉框样式 */
            QComboBox::drop-down {
                border: none;
                width: 20px;
                background: transparent;
            }
            
            QComboBox::down-arrow {
                image: none;
                border-left: 5px solid transparent;
                border-right: 5px solid transparent;
                border-top: 5px solid #666666;
                margin-right: 5px;
            }
            
            QComboBox QAbstractItemView {
                background: white;
                border: 1px solid #D0D0D0;
                selection-background-color: #E3F2FD;
                color: #333333;
            }
            
            #pageInfoLabel {
                font-weight: bold;
                color: #666666;
                background: transparent;
            }
        """
        
        self.setStyleSheet(unified_style + product_style)


# 导入对话框类
from .product_dialog import ProductDialog
from .category_dialog import CategoryDialog
from .supplier_dialog import SupplierDialog 