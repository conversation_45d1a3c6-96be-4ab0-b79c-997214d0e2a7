#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
分类添加/编辑对话框
"""

import os
import sys
from typing import Dict, Any, Optional
from PyQt6.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QFormLayout, QLabel, 
    QLineEdit, QTextEdit, QPushButton, QMessageBox, QCheckBox, QComboBox
)
from PyQt6.QtCore import Qt

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

class CategoryDialog(QDialog):
    """分类添加/编辑对话框"""
    
    def __init__(self, parent=None, controller=None, category_data=None):
        super().__init__(parent)
        self.controller = controller
        self.category_data = category_data
        self.is_edit_mode = category_data is not None
        
        self.init_ui()
        
        if self.is_edit_mode:
            self.populate_form()
    
    def init_ui(self):
        """初始化界面"""
        self.setWindowTitle("编辑分类" if self.is_edit_mode else "添加分类")
        self.setModal(True)
        self.resize(400, 300)
        
        layout = QVBoxLayout(self)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(20)
        
        # 创建表单
        self.create_form()
        
        # 创建按钮
        self.create_buttons()
        
        # 设置样式
        self.set_styles()
    
    def create_form(self):
        """创建表单"""
        form_layout = QFormLayout()
        form_layout.setSpacing(15)
        
        # 分类编码
        self.code_edit = QLineEdit()
        self.code_edit.setPlaceholderText("如：pj-001（配件）、wz-001（物资）")
        form_layout.addRow("分类编码*:", self.code_edit)
        
        # 分类名称
        self.name_edit = QLineEdit()
        self.name_edit.setPlaceholderText("请输入分类名称")
        form_layout.addRow("分类名称*:", self.name_edit)
        
        # 分类种类
        self.type_combo = QComboBox()
        self.type_combo.addItems(["配件", "物资", "通用", "其他"])
        self.type_combo.setCurrentText("其他")
        form_layout.addRow("分类种类*:", self.type_combo)
        
        # 分类描述
        self.description_edit = QTextEdit()
        self.description_edit.setPlaceholderText("请输入分类描述")
        self.description_edit.setMaximumHeight(100)
        form_layout.addRow("分类描述:", self.description_edit)
        
        # 是否启用
        self.active_checkbox = QCheckBox("启用该分类")
        self.active_checkbox.setChecked(True)
        form_layout.addRow("状态:", self.active_checkbox)
        
        self.layout().addLayout(form_layout)
    
    def create_buttons(self):
        """创建按钮"""
        button_layout = QHBoxLayout()
        button_layout.addStretch()
        
        # 取消按钮
        cancel_btn = QPushButton("取消")
        cancel_btn.setObjectName("cancelButton")
        cancel_btn.clicked.connect(self.reject)
        button_layout.addWidget(cancel_btn)
        
        # 保存按钮
        save_btn = QPushButton("保存")
        save_btn.setObjectName("saveButton")
        save_btn.clicked.connect(self.save_category)
        button_layout.addWidget(save_btn)
        
        self.layout().addLayout(button_layout)
    
    def populate_form(self):
        """填充表单（编辑模式）"""
        if not self.category_data:
            return
        
        self.code_edit.setText(self.category_data.get('code', ''))
        self.name_edit.setText(self.category_data.get('name', ''))
        self.type_combo.setCurrentText(self.category_data.get('type', '其他'))
        self.description_edit.setPlainText(self.category_data.get('description', ''))
        self.active_checkbox.setChecked(self.category_data.get('is_active', True))
    
    def validate_form(self) -> bool:
        """验证表单"""
        # 检查必填字段
        if not self.code_edit.text().strip():
            QMessageBox.warning(self, "验证失败", "请输入分类编码")
            self.code_edit.setFocus()
            return False
        
        if not self.name_edit.text().strip():
            QMessageBox.warning(self, "验证失败", "请输入分类名称")
            self.name_edit.setFocus()
            return False
        
        # 验证编码格式
        code = self.code_edit.text().strip()
        if len(code) < 3:
            QMessageBox.warning(self, "验证失败", "分类编码长度至少为3个字符")
            self.code_edit.setFocus()
            return False
        
        return True
    
    def get_form_data(self) -> Dict[str, Any]:
        """获取表单数据"""
        return {
            'code': self.code_edit.text().strip(),
            'name': self.name_edit.text().strip(),
            'type': self.type_combo.currentText(),
            'description': self.description_edit.toPlainText().strip() or None,
            'is_active': self.active_checkbox.isChecked()
        }
    
    def save_category(self):
        """保存分类"""
        if not self.validate_form():
            return
        
        data = self.get_form_data()
        
        try:
            if self.is_edit_mode:
                # 编辑模式
                success = self.controller.update_category(self.category_data['id'], data)
            else:
                # 添加模式
                success = self.controller.create_category(data)
            
            if success:
                self.accept()
        
        except Exception as e:
            QMessageBox.critical(self, "错误", f"保存失败: {str(e)}")
    
    def set_styles(self):
        """设置样式"""
        self.setStyleSheet("""
            QDialog {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #E3F2FD, stop:1 #BBDEFB);
                font-family: "Microsoft YaHei", Arial, sans-serif;
            }
            
            QLabel {
                color: #333333;
                font-size: 14px;
                font-weight: normal;
                background: transparent;
            }
            
            QLineEdit {
                border: 1px solid #D0D0D0;
                border-radius: 4px;
                padding: 8px 12px;
                font-size: 14px;
                background: white;
                min-height: 20px;
                color: #333333;
            }
            
            QLineEdit:focus {
                border-color: #2196F3;
                background: white;
            }
            
            QTextEdit {
                border: 1px solid #D0D0D0;
                border-radius: 4px;
                padding: 8px;
                font-size: 14px;
                background: white;
                color: #333333;
            }
            
            QTextEdit:focus {
                border-color: #2196F3;
                background: white;
            }
            
            QCheckBox {
                font-size: 14px;
                color: #333333;
                spacing: 8px;
            }
            
            QCheckBox::indicator {
                width: 18px;
                height: 18px;
                border: 1px solid #D0D0D0;
                border-radius: 3px;
                background: white;
            }
            
            QCheckBox::indicator:checked {
                background: #2196F3;
                border-color: #2196F3;
            }
            
            QPushButton {
                border: none;
                border-radius: 4px;
                padding: 8px 16px;
                font-size: 14px;
                font-weight: normal;
                min-width: 70px;
                color: white;
            }
            
            #saveButton {
                background: #4CAF50;
            }
            
            #saveButton:hover {
                background: #45A049;
            }
            
            #cancelButton {
                background: #9E9E9E;
            }
            
            #cancelButton:hover {
                background: #757575;
            }
        """) 