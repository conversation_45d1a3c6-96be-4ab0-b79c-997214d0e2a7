#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
分类删除确认对话框
"""

from PyQt6.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QLabel, QPushButton, 
    QRadioButton, QButtonGroup, QFrame, QMessageBox
)
from PyQt6.QtCore import Qt
from PyQt6.QtGui import QFont

class CategoryDeleteDialog(QDialog):
    """分类删除确认对话框"""
    
    def __init__(self, parent=None, category_name=""):
        super().__init__(parent)
        self.category_name = category_name
        self.delete_type = "normal"
        
        self.init_ui()
    
    def init_ui(self):
        """初始化界面"""
        self.setWindowTitle("删除分类确认")
        self.setModal(True)
        self.resize(450, 300)
        
        layout = QVBoxLayout(self)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(20)
        
        # 标题
        title = QLabel(f"确认删除分类：{self.category_name}")
        title.setObjectName("dialogTitle")
        title.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(title)
        
        # 删除选项
        self.create_delete_options(layout)
        
        # 警告信息
        self.create_warning_info(layout)
        
        # 按钮
        self.create_buttons(layout)
        
        # 设置样式
        self.set_styles()
    
    def create_delete_options(self, layout):
        """创建删除选项"""
        options_frame = QFrame()
        options_frame.setObjectName("optionsFrame")
        options_layout = QVBoxLayout(options_frame)
        options_layout.setSpacing(15)
        
        # 选项标题
        options_title = QLabel("请选择删除方式：")
        options_title.setObjectName("optionsTitle")
        options_layout.addWidget(options_title)
        
        # 创建单选按钮组
        self.button_group = QButtonGroup(self)
        
        # 普通删除
        self.normal_radio = QRadioButton("普通删除")
        self.normal_radio.setChecked(True)
        self.normal_radio.setObjectName("normalRadio")
        self.button_group.addButton(self.normal_radio, 0)
        options_layout.addWidget(self.normal_radio)
        
        normal_desc = QLabel("• 仅删除分类记录，如果有关联商品则无法删除")
        normal_desc.setObjectName("optionDesc")
        options_layout.addWidget(normal_desc)
        
        # 软删除
        self.soft_radio = QRadioButton("禁用分类")
        self.soft_radio.setObjectName("softRadio")
        self.button_group.addButton(self.soft_radio, 1)
        options_layout.addWidget(self.soft_radio)
        
        soft_desc = QLabel("• 将分类设置为不活跃状态，保留数据但不显示")
        soft_desc.setObjectName("optionDesc")
        options_layout.addWidget(soft_desc)
        
        # 强制删除
        self.force_radio = QRadioButton("强制删除")
        self.force_radio.setObjectName("forceRadio")
        self.button_group.addButton(self.force_radio, 2)
        options_layout.addWidget(self.force_radio)
        
        force_desc = QLabel("• 删除分类及其所有关联商品（不可恢复）")
        force_desc.setObjectName("forceDesc")
        options_layout.addWidget(force_desc)
        
        layout.addWidget(options_frame)
    
    def create_warning_info(self, layout):
        """创建警告信息"""
        warning_frame = QFrame()
        warning_frame.setObjectName("warningFrame")
        warning_layout = QVBoxLayout(warning_frame)
        
        warning_title = QLabel("⚠️ 重要提示")
        warning_title.setObjectName("warningTitle")
        warning_layout.addWidget(warning_title)
        
        warning_text = QLabel(
            "• 普通删除：如果分类下有商品，删除会失败\n"
            "• 禁用分类：分类将不再显示，但数据保留\n"
            "• 强制删除：将永久删除分类和所有关联商品，此操作不可恢复！"
        )
        warning_text.setObjectName("warningText")
        warning_text.setWordWrap(True)
        warning_layout.addWidget(warning_text)
        
        layout.addWidget(warning_frame)
    
    def create_buttons(self, layout):
        """创建按钮"""
        button_layout = QHBoxLayout()
        button_layout.addStretch()
        
        # 取消按钮
        cancel_btn = QPushButton("取消")
        cancel_btn.setObjectName("cancelButton")
        cancel_btn.clicked.connect(self.reject)
        button_layout.addWidget(cancel_btn)
        
        # 确认删除按钮
        delete_btn = QPushButton("确认删除")
        delete_btn.setObjectName("deleteButton")
        delete_btn.clicked.connect(self.confirm_delete)
        button_layout.addWidget(delete_btn)
        
        layout.addLayout(button_layout)
    
    def confirm_delete(self):
        """确认删除"""
        checked_id = self.button_group.checkedId()
        
        if checked_id == 0:
            self.delete_type = "normal"
        elif checked_id == 1:
            self.delete_type = "soft"
        elif checked_id == 2:
            self.delete_type = "force"
            # 强制删除需要二次确认
            reply = QMessageBox.warning(
                self, "强制删除确认",
                f"您选择了强制删除分类 '{self.category_name}'。\n\n"
                "这将永久删除该分类及其所有关联商品，此操作不可恢复！\n\n"
                "确定要继续吗？",
                QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
                QMessageBox.StandardButton.No
            )
            
            if reply != QMessageBox.StandardButton.Yes:
                return
        
        self.accept()
    
    def get_delete_type(self):
        """获取删除类型"""
        return self.delete_type
    
    def set_styles(self):
        """设置样式"""
        self.setStyleSheet("""
            QDialog {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #E3F2FD, stop:1 #BBDEFB);
                font-family: "Microsoft YaHei", Arial, sans-serif;
            }
            
            #dialogTitle {
                font-size: 18px;
                font-weight: bold;
                color: #1976D2;
                padding: 10px;
            }
            
            #optionsFrame {
                background: rgba(255, 255, 255, 0.8);
                border: 1px solid #E0E0E0;
                border-radius: 8px;
                padding: 15px;
            }
            
            #optionsTitle {
                font-size: 14px;
                font-weight: bold;
                color: #333333;
                margin-bottom: 10px;
            }
            
            QRadioButton {
                font-size: 14px;
                color: #333333;
                spacing: 8px;
                padding: 5px;
            }
            
            #forceRadio {
                color: #D32F2F;
                font-weight: bold;
            }
            
            QRadioButton::indicator {
                width: 16px;
                height: 16px;
                border: 2px solid #CCCCCC;
                border-radius: 8px;
                background: white;
            }
            
            QRadioButton::indicator:checked {
                background: #2196F3;
                border-color: #2196F3;
            }
            
            #forceRadio::indicator:checked {
                background: #D32F2F;
                border-color: #D32F2F;
            }
            
            #optionDesc {
                font-size: 12px;
                color: #666666;
                margin-left: 25px;
                margin-bottom: 5px;
            }
            
            #forceDesc {
                font-size: 12px;
                color: #D32F2F;
                margin-left: 25px;
                margin-bottom: 5px;
                font-weight: bold;
            }
            
            #warningFrame {
                background: rgba(255, 243, 224, 0.9);
                border: 1px solid #FF9800;
                border-radius: 8px;
                padding: 15px;
            }
            
            #warningTitle {
                font-size: 14px;
                font-weight: bold;
                color: #F57C00;
                margin-bottom: 8px;
            }
            
            #warningText {
                font-size: 12px;
                color: #E65100;
                line-height: 1.4;
            }
            
            QPushButton {
                border: none;
                border-radius: 4px;
                padding: 10px 20px;
                font-size: 14px;
                font-weight: bold;
                min-width: 80px;
                color: white;
            }
            
            #cancelButton {
                background: #9E9E9E;
            }
            
            #cancelButton:hover {
                background: #757575;
            }
            
            #deleteButton {
                background: #F44336;
            }
            
            #deleteButton:hover {
                background: #D32F2F;
            }
        """) 