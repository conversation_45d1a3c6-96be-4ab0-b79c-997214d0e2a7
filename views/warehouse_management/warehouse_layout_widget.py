#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
仓库布局显示组件
"""

import os
import sys
from typing import Dict, Any, List
from PyQt6.QtWidgets import QWidget, QVBoxLayout, QLabel
from PyQt6.QtCore import Qt
from PyQt6.QtGui import QPainter, QPen, QBrush, QColor, QFont

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

class WarehouseLayoutWidget(QWidget):
    """仓库布局显示组件"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.layout_data = None
        self.setMinimumSize(800, 600)
        
    def set_layout_data(self, layout_data: Dict[str, Any]):
        """设置布局数据"""
        self.layout_data = layout_data
        self.update()
    
    def paintEvent(self, event):
        """绘制事件"""
        painter = QPainter(self)
        painter.setRenderHint(QPainter.RenderHint.Antialiasing)
        
        if not self.layout_data:
            # 显示提示信息
            painter.setPen(QPen(QColor(100, 100, 100)))
            painter.setFont(QFont("Microsoft YaHei", 16))
            painter.drawText(self.rect(), Qt.AlignmentFlag.AlignCenter, "请选择仓库查看布局")
            return
        
        # 绘制仓库布局
        self.draw_warehouse_layout(painter)
    
    def draw_warehouse_layout(self, painter: QPainter):
        """绘制仓库布局"""
        warehouse = self.layout_data['warehouse']
        shelves = self.layout_data['shelves']
        
        # 设置绘制区域
        margin = 50
        draw_width = self.width() - 2 * margin
        draw_height = self.height() - 2 * margin
        
        # 绘制仓库边框
        painter.setPen(QPen(QColor(0, 0, 0), 2))
        painter.setBrush(QBrush(QColor(245, 245, 245)))
        painter.drawRect(margin, margin, draw_width, draw_height)
        
        # 绘制仓库标题
        painter.setPen(QPen(QColor(0, 0, 0)))
        painter.setFont(QFont("Microsoft YaHei", 14, QFont.Weight.Bold))
        title = f"{warehouse['name']} ({warehouse['code']})"
        painter.drawText(margin + 10, margin + 25, title)
        
        # 如果没有货架，显示提示
        if not shelves:
            painter.setPen(QPen(QColor(150, 150, 150)))
            painter.setFont(QFont("Microsoft YaHei", 12))
            painter.drawText(margin + 10, margin + 60, "该仓库暂无货架")
            return
        
        # 计算货架布局
        shelf_width = 80
        shelf_height = 40
        shelf_margin = 20
        
        # 简单的网格布局
        cols = max(1, (draw_width - 20) // (shelf_width + shelf_margin))
        start_x = margin + 10
        start_y = margin + 50
        
        for i, shelf in enumerate(shelves):
            row = i // cols
            col = i % cols
            
            x = start_x + col * (shelf_width + shelf_margin)
            y = start_y + row * (shelf_height + shelf_margin)
            
            # 如果超出绘制区域，停止绘制
            if y + shelf_height > margin + draw_height - 10:
                break
            
            # 绘制货架
            self.draw_shelf(painter, shelf, x, y, shelf_width, shelf_height)
    
    def draw_shelf(self, painter: QPainter, shelf: Dict[str, Any], x: int, y: int, width: int, height: int):
        """绘制单个货架"""
        # 货架颜色（根据状态）
        if shelf['is_active']:
            shelf_color = QColor(100, 150, 255)  # 蓝色
        else:
            shelf_color = QColor(200, 200, 200)  # 灰色
        
        # 绘制货架矩形
        painter.setPen(QPen(QColor(0, 0, 0), 1))
        painter.setBrush(QBrush(shelf_color))
        painter.drawRect(x, y, width, height)
        
        # 绘制货架编码
        painter.setPen(QPen(QColor(255, 255, 255)))
        painter.setFont(QFont("Microsoft YaHei", 10, QFont.Weight.Bold))
        painter.drawText(x + 5, y + height // 2 + 5, shelf['shelf_code'])
        
        # 绘制层数和位置数信息
        painter.setFont(QFont("Microsoft YaHei", 8))
        info_text = f"{shelf['max_layers']}层x{shelf['positions_per_layer']}位"
        painter.drawText(x + 5, y + height - 5, info_text) 