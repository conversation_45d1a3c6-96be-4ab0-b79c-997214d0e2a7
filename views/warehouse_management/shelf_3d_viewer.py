#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
3D货架展示系统
基于PyVista实现，支持GPU加速和交互式操作
"""

import os
import sys
import numpy as np
from typing import Dict, List, Any, Optional, Tuple
from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QLabel, QComboBox, 
    QPushButton, QFrame, QMessageBox, QDialog, QFormLayout,
    QLineEdit, QSpinBox, QTextEdit, QGroupBox, QCheckBox,
    QSlider, QProgressBar
)
from PyQt6.QtCore import Qt, QThread, pyqtSignal, QTimer
from PyQt6.QtGui import QFont

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

try:
    import os
    # 在导入PyVista之前设置环境变量来禁用OpenGL调试信息
    os.environ['VTK_SILENCE_GET_VOID_POINTER_WARNINGS'] = '1'
    os.environ['VTK_DEBUG_LEAKS'] = '0'
    
    import pyvista as pv
    import vtk
    from pyvistaqt import QtInteractor
    
    # 设置PyVista全局配置以减少调试输出
    pv.set_error_output_file('nul' if os.name == 'nt' else '/dev/null')
    pv.set_jupyter_backend('none')  # 禁用Jupyter后端
    
    # 禁用VTK的详细输出
    vtk.vtkObject.GlobalWarningDisplayOff()
    vtk.vtkOutputWindow.SetGlobalWarningDisplay(0)
    
    PYVISTA_AVAILABLE = True
except ImportError:
    PYVISTA_AVAILABLE = False
    print("PyVista未安装，3D功能将不可用")

from controllers.warehouse_controller import WarehouseController
from services.log_service import LogService

class Shelf3DViewer(QWidget):
    """3D货架展示器"""
    
    # 预设颜色方案 - 分层颜色区分（4种颜色）
    LAYER_COLORS = {
        1: [1.0, 0.2, 0.2],    # 红色 - 第1层
        2: [0.2, 1.0, 1.0],    # 青色 - 第2层  
        3: [1.0, 1.0, 0.2],    # 黄色 - 第3层
        4: [0.2, 0.6, 1.0],    # 深青色 - 第4层
        5: [0.8, 0.2, 1.0],    # 紫色 - 第5层（扩展）
    }
    
    # 库存状态颜色（简化为2种）
    STOCK_COLORS = {
        'high': [0.2, 0.8, 0.2],    # 绿色 - 高库存
        'low': [1.0, 0.2, 0.2],     # 红色 - 低库存
        'empty': [0.7, 0.7, 0.7],   # 灰色 - 空库位
    }
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.controller = WarehouseController()
        self.logger = LogService.get_logger(__name__)
        
        # 数据存储
        self.current_warehouse_id = None
        self.shelves_data = []
        self.locations_data = []
        self.inventory_data = {}
        
        # 3D场景对象
        self.plotter = None
        self.shelf_actors = {}
        self.location_actors = {}
        self.product_actors = {}
        
        # 交互状态
        self.selected_location = None
        self.show_products = True
        self.show_wireframe = True
        self.opacity_value = 0.7
        
        self.init_ui()
        self.connect_signals()
        
        if PYVISTA_AVAILABLE:
            self.init_3d_viewer()
        else:
            self.show_error_message()
    
    def init_ui(self):
        """初始化界面"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(10, 10, 10, 10)
        layout.setSpacing(10)
        
        # 控制面板
        control_panel = self.create_control_panel()
        layout.addWidget(control_panel)
        
        # 3D显示区域
        if PYVISTA_AVAILABLE:
            self.viewer_frame = QFrame()
            self.viewer_frame.setMinimumHeight(600)
            self.viewer_frame.setStyleSheet("""
                QFrame {
                    border: 2px solid #CCCCCC;
                    border-radius: 8px;
                    background: #F8F9FA;
                }
            """)
            layout.addWidget(self.viewer_frame)
        else:
            error_label = QLabel("PyVista未安装，无法显示3D视图")
            error_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
            error_label.setStyleSheet("color: red; font-size: 16px;")
            layout.addWidget(error_label)
        
        # 状态栏
        self.status_bar = self.create_status_bar()
        layout.addWidget(self.status_bar)
        
        self.set_styles()
    
    def create_control_panel(self):
        """创建控制面板"""
        panel = QGroupBox("")
        panel.setObjectName("controlPanel")
        layout = QVBoxLayout(panel)
        
        # 第一行：仓库选择和基本控制
        row1 = QHBoxLayout()
        
        # 仓库选择
        row1.addWidget(QLabel("选择仓库:"))
        self.warehouse_combo = QComboBox()
        self.warehouse_combo.setObjectName("warehouseCombo")
        self.warehouse_combo.currentTextChanged.connect(self.on_warehouse_changed)
        row1.addWidget(self.warehouse_combo)
        
        row1.addStretch()
        
        # 刷新按钮
        self.refresh_btn = QPushButton("刷新数据")
        self.refresh_btn.setObjectName("refreshButton")
        self.refresh_btn.clicked.connect(self.refresh_data)
        row1.addWidget(self.refresh_btn)
        
        # 重置视角按钮
        self.reset_view_btn = QPushButton("重置视角")
        self.reset_view_btn.setObjectName("resetViewButton")
        self.reset_view_btn.clicked.connect(self.reset_camera_view)
        row1.addWidget(self.reset_view_btn)
        
        layout.addLayout(row1)
        
        # 第二行：显示选项
        row2 = QHBoxLayout()
        
        # 显示选项（移除商品显示选项，因为不再渲染商品立方体）
        # self.show_products_cb = QCheckBox("显示商品")
        # self.show_products_cb.setChecked(True)
        # self.show_products_cb.toggled.connect(self.toggle_products_display)
        # row2.addWidget(self.show_products_cb)
        
        self.show_wireframe_cb = QCheckBox("显示线框")      # 创建线框显示切换按钮
        self.show_wireframe_cb.setChecked(True) # 默认显示线框
        self.show_wireframe_cb.toggled.connect(self.toggle_wireframe_display)  # 连接线框显示切换信号
        row2.addWidget(self.show_wireframe_cb)  # 将线框显示切换按钮添加到布局中
        
        row2.addStretch()
        
        # 透明度控制
        row2.addWidget(QLabel("透明度:"))
        self.opacity_slider = QSlider(Qt.Orientation.Horizontal)
        self.opacity_slider.setRange(10, 100)
        self.opacity_slider.setValue(70)
        self.opacity_slider.setMaximumWidth(150)
        self.opacity_slider.valueChanged.connect(self.on_opacity_changed)
        row2.addWidget(self.opacity_slider)
        
        self.opacity_label = QLabel("70%")
        self.opacity_label.setMinimumWidth(40)
        row2.addWidget(self.opacity_label)
        
        layout.addLayout(row2)
        
        return panel
    
    def create_status_bar(self):
        """创建状态栏"""
        status_frame = QFrame()
        status_frame.setObjectName("statusFrame")
        layout = QHBoxLayout(status_frame)
        layout.setContentsMargins(10, 5, 10, 5)
        
        # 统计信息
        self.stats_label = QLabel("就绪")
        self.stats_label.setObjectName("statsLabel")
        layout.addWidget(self.stats_label)
        
        layout.addStretch()
        
        # 选中信息
        self.selection_label = QLabel("未选择")
        self.selection_label.setObjectName("selectionLabel")
        layout.addWidget(self.selection_label)
        
        return status_frame
    
    def init_3d_viewer(self):
        """初始化3D查看器"""
        if not PYVISTA_AVAILABLE:
            return
        
        try:
            # 创建PyVista绘图器
            viewer_layout = QVBoxLayout(self.viewer_frame)
            viewer_layout.setContentsMargins(5, 5, 5, 5)
            
            # 创建Qt交互器
            self.plotter = QtInteractor(self.viewer_frame)
            viewer_layout.addWidget(self.plotter.interactor)
            
            # 配置3D场景
            self.setup_3d_scene()
            
            # 启用交互功能
            self.setup_interactions()
            
            self.logger.info("3D查看器初始化成功")
            
        except Exception as e:
            self.logger.error(f"3D查看器初始化失败: {e}")
            self.show_error_message(f"3D查看器初始化失败: {e}")
            
            # 设置plotter为None，确保其他方法知道3D不可用
            self.plotter = None
    
    def setup_3d_scene(self):
        """设置3D场景"""
        if not self.plotter:
            return
        
        # 设置背景
        self.plotter.set_background('lightblue')
        
        # 启用阴影和抗锯齿
        self.plotter.enable_anti_aliasing()
        
        # 添加坐标轴
        self.plotter.show_axes()
        
        # 设置相机位置（适应6m×10m仓库）
        self.plotter.camera_position = [(8, -5, 8), (3, 5, 1), (0, 0, 1)]
        
        # 启用类似CAD的导航体验
        self.plotter.enable_terrain_style()
    
    def setup_interactions(self):
        """设置交互功能"""
        if not self.plotter:
            return
        
        # 先禁用所有picking功能
        try:
            self.plotter.disable_picking()
        except:
            pass  # 如果没有启用过picking，忽略错误
        
        # 检查是否已经启用了picking
        try:
            # 启用点选功能
            self.plotter.enable_point_picking(callback=self.on_point_picked, show_message=False)
        except Exception as e:
            if "already enabled" in str(e):
                # 如果已经启用，先禁用再重新启用
                try:
                    self.plotter.disable_picking()
                    self.plotter.enable_point_picking(callback=self.on_point_picked, show_message=False)
                except:
                    pass  # 如果仍然失败，忽略错误
            else:
                self.logger.warning(f"启用点选功能失败: {e}")
        
        # 启用单元格选择（如果点选成功的话）
        try:
            self.plotter.enable_cell_picking(callback=self.on_cell_picked, show_message=False)
        except Exception as e:
            if "already enabled" in str(e):
                # 单元格选择和点选可能冲突，这是正常的
                pass
            else:
                self.logger.warning(f"启用单元格选择失败: {e}")
    
    def connect_signals(self):
        """连接信号"""
        self.controller.error_occurred.connect(self.show_error_message)
    
    def load_warehouses(self):
        """加载仓库列表"""
        try:
            warehouses = self.controller.get_warehouses()
            
            self.warehouse_combo.clear()
            self.warehouse_combo.addItem("请选择仓库", None)
            
            for warehouse in warehouses:
                self.warehouse_combo.addItem(
                    f"{warehouse['name']} ({warehouse['code']})",
                    warehouse['id']
                )
                
        except Exception as e:
            self.logger.error(f"加载仓库列表失败: {e}")
            self.show_error_message(f"加载仓库列表失败: {e}")
    
    def on_warehouse_changed(self):
        """仓库选择改变"""
        warehouse_id = self.warehouse_combo.currentData()
        if warehouse_id:
            self.current_warehouse_id = warehouse_id
            self.load_warehouse_data()
    
    def load_warehouse_data(self):
        """加载仓库数据"""
        if not self.current_warehouse_id:
            return
        
        try:
            # 显示加载状态
            self.stats_label.setText("正在加载数据...")
            
            # 加载货架数据
            self.shelves_data = self.controller.get_shelves(self.current_warehouse_id)
            
            # 加载库位数据
            self.locations_data = self.controller.get_locations(warehouse_id=self.current_warehouse_id)
            
            # 加载库存数据（模拟）
            self.load_inventory_data()
            
            # 渲染3D场景
            self.render_3d_scene()
            
            # 更新统计信息
            self.update_statistics()
            
        except Exception as e:
            self.logger.error(f"加载仓库数据失败: {e}")
            self.show_error_message(f"加载仓库数据失败: {e}")
    
    def load_inventory_data(self):
        """加载库存数据（模拟）"""
        # 这里模拟库存数据，实际应该从数据库获取
        self.inventory_data = {}
        
        for location in self.locations_data:
            location_id = location['id']
            # 模拟库存量（0-100之间的随机数）
            quantity = np.random.randint(0, 101)
            
            # 库存预警值（随机设置在20-40之间）
            warning_threshold = np.random.randint(20, 41)
            
            # 根据库存量和预警值确定状态
            if quantity == 0:
                status = 'empty'
            elif quantity < warning_threshold:
                status = 'low'
            else:
                status = 'high'
            
            # 获取库位位置信息
            warehouse_name = "默认仓库"
            shelf_code = f"货架{((location_id - 1) // 50) + 1}"  # 假设每个货架50个库位
            layer = location.get('layer', 1)
            position = location.get('position', 1)
            
            self.inventory_data[location_id] = {
                'quantity': quantity,
                'warning_threshold': warning_threshold,
                'status': status,
                'product_name': f"商品{location_id % 10 + 1}" if quantity > 0 else None,
                'warehouse_name': warehouse_name,
                'shelf_code': shelf_code,
                'layer': layer,
                'position': position,
                'location_code': location.get('location_code', f"L{location_id:03d}")
            }
    
    def render_3d_scene(self):
        """渲染3D场景"""
        if not self.plotter or not PYVISTA_AVAILABLE:
            return
        
        try:
            # 清空现有场景
            self.plotter.clear()
            self.shelf_actors = {}
            self.location_actors = {}
            self.product_actors = {}
            
            # 重新设置场景
            self.setup_3d_scene()
            
            # 重新设置交互功能（因为clear()会清除交互设置）
            self.setup_interactions()
            
            # 渲染仓库地面
            self.render_warehouse_floor()
            
            # 渲染货架
            self.render_shelves()
            
            # 渲染库位
            self.render_locations()
            
            # 注意：不再渲染商品立方体，库位颜色已反映库存状态
            
            # 更新显示
            self.plotter.render()
            
            self.logger.info("3D场景渲染完成")
            
        except Exception as e:
            self.logger.error(f"3D场景渲染失败: {e}")
            self.show_error_message(f"3D场景渲染失败: {e}")
    
    def render_warehouse_floor(self):
        """渲染仓库地面（6米×10米）带50cm×50cm网格和标尺"""
        if not self.plotter:
            return
        
        try:
            # 创建仓库地面（6米×10米）
            warehouse_floor = pv.Plane(
                center=[3, 5, 0],  # 中心点
                direction=[0, 0, 1],  # 法向量（向上）
                i_size=6,  # 宽度6米
                j_size=10,  # 长度10米
                i_resolution=12,  # 50cm网格：6米/0.5米=12格
                j_resolution=20   # 50cm网格：10米/0.5米=20格
            )
            
            # 添加地面
            self.plotter.add_mesh(
                warehouse_floor,
                color='lightgray',
                opacity=0.2,
                show_edges=True,
                edge_color='gray',
                line_width=1,
                name='warehouse_floor'
            )
            
            # 添加50cm×50cm网格线
            self.render_grid_lines()
            
            # 添加仓库边界线框
            warehouse_bounds = pv.Box(
                bounds=[0, 6, 0, 10, 0, 0.01]  # 6米×10米×0.01米高的薄盒子
            )
            
            self.plotter.add_mesh(
                warehouse_bounds,
                style='wireframe',
                color='black',
                line_width=4,
                name='warehouse_bounds'
            )
            
            # 添加标尺
            self.render_rulers()
            
            # 添加仓库标签
            self.plotter.add_point_labels(
                [[3, 5, 0.1]],
                ["仓库区域 (6m × 10m)"],
                point_size=0,
                font_size=16,
                text_color='black',
                font_family='courier',
                always_visible=True
            )
            
        except Exception as e:
            self.logger.error(f"渲染仓库地面失败: {e}")
    
    def render_grid_lines(self):
        """渲染50cm×50cm网格线"""
        try:
            # X方向网格线（每50cm一条）
            for i in range(13):  # 0到6米，每0.5米一条线
                x = i * 0.5
                line_points = np.array([[x, 0, 0.005], [x, 10, 0.005]])
                line = pv.Line(line_points[0], line_points[1])
                self.plotter.add_mesh(
                    line,
                    color='darkgray',
                    line_width=1,
                    name=f'grid_x_{i}'
                )
            
            # Y方向网格线（每50cm一条）
            for j in range(21):  # 0到10米，每0.5米一条线
                y = j * 0.5
                line_points = np.array([[0, y, 0.005], [6, y, 0.005]])
                line = pv.Line(line_points[0], line_points[1])
                self.plotter.add_mesh(
                    line,
                    color='darkgray',
                    line_width=1,
                    name=f'grid_y_{j}'
                )
                
        except Exception as e:
            self.logger.error(f"渲染网格线失败: {e}")
    
    def render_rulers(self):
        """渲染标尺"""
        try:
            # X轴标尺（底部）
            ruler_points = []
            ruler_labels = []
            for i in range(13):  # 0到6米，每0.5米一个刻度
                x = i * 0.5
                ruler_points.append([x, -0.3, 0.01])
                ruler_labels.append(f"{x:.1f}m")
            
            self.plotter.add_point_labels(
                ruler_points,
                ruler_labels,
                point_size=0,
                font_size=8,
                text_color='blue',
                font_family='courier',
                always_visible=True
            )
            
            # Y轴标尺（左侧）
            ruler_points = []
            ruler_labels = []
            for j in range(21):  # 0到10米，每0.5米一个刻度
                y = j * 0.5
                ruler_points.append([-0.3, y, 0.01])
                ruler_labels.append(f"{y:.1f}m")
            
            self.plotter.add_point_labels(
                ruler_points,
                ruler_labels,
                point_size=0,
                font_size=8,
                text_color='blue',
                font_family='courier',
                always_visible=True
            )
            
        except Exception as e:
            self.logger.error(f"渲染标尺失败: {e}")
    
    def render_shelves(self):
        """渲染货架框架（10个货架，宽2米×高2米×深0.6米）"""
        if not self.shelves_data:
            return
        
        # 货架规格：宽2米×高2米×深0.6米
        shelf_width = 2.0
        shelf_height = 2.0
        shelf_depth = 0.6
        
        # 从数据库读取货架位置信息，不再使用硬编码
        for i, shelf in enumerate(self.shelves_data):
            try:
                # 从数据库读取货架位置（cm转换为米）
                x_pos = (shelf.get('x_position') or 0) / 100.0  # cm转米
                y_pos = (shelf.get('y_position') or 0) / 100.0  # cm转米
                
                z_pos = 0.01  # 稍微抬高避免与地面重叠
                
                # 创建货架主体框架
                shelf_box = pv.Box(
                    bounds=[
                        x_pos, x_pos + shelf_width,
                        y_pos, y_pos + shelf_depth,
                        z_pos, z_pos + shelf_height
                    ]
                )
                
                # 添加货架框架
                if self.show_wireframe:
                    shelf_actor = self.plotter.add_mesh(
                        shelf_box,
                        style='wireframe',
                        color='darkblue',
                        line_width=3,
                        name=f"shelf_frame_{shelf['id']}"
                    )
                    self.shelf_actors[shelf['id']] = shelf_actor
                
                # 添加每层货架的分隔线（5层）
                if self.show_wireframe:
                    layer_height = shelf_height / 5  # 每层0.4米
                    for layer_num in range(1, 6):  # 5层
                        layer_z = z_pos + (layer_num - 1) * layer_height
                        
                        # 创建层分隔线（水平面）
                        layer_plane = pv.Plane(
                            center=[x_pos + shelf_width/2, y_pos + shelf_depth/2, layer_z],
                            direction=[0, 0, 1],
                            i_size=shelf_width,
                            j_size=shelf_depth
                        )
                        
                        # 添加层分隔线
                        self.plotter.add_mesh(
                            layer_plane,
                            style='wireframe',
                            color='blue',
                            line_width=1,
                            opacity=0.5,
                            name=f"shelf_layer_{shelf['id']}_{layer_num}"
                        )
                
                # 添加货架标签
                label_pos = [x_pos + shelf_width/2, y_pos - 0.2, z_pos + shelf_height + 0.1]
                self.plotter.add_point_labels(
                    [label_pos],
                    [shelf.get('shelf_code', f"货架{i+1}")],
                    point_size=0,
                    font_size=12,
                    text_color='darkblue',
                    font_family='courier',
                    always_visible=True
                )
                
                # 注意：货架位置信息直接从数据库读取，不再需要存储计算值
                
            except Exception as e:
                self.logger.error(f"渲染货架 {shelf['id']} 失败: {e}")
    

    
    def render_locations(self):
        """渲染库位"""
        if not self.locations_data:
            return
        
        # 按货架分组库位
        shelves_locations = {}
        for location in self.locations_data:
            shelf_id = location['shelf_id']
            if shelf_id not in shelves_locations:
                shelves_locations[shelf_id] = []
            shelves_locations[shelf_id].append(location)
        
        # 为每个货架渲染库位
        for shelf_id, locations in shelves_locations.items():
            self.render_shelf_locations(shelf_id, locations)
    
    def render_shelf_locations(self, shelf_id: int, locations: List[Dict]):
        """渲染单个货架的库位（20列×1行×5层精确布局）"""
        # 找到对应的货架信息
        shelf_info = None
        for shelf in self.shelves_data:
            if shelf['id'] == shelf_id:
                shelf_info = shelf
                break
        
        if not shelf_info:
            return
        
        # 使用从数据库读取的货架位置和固定尺寸
        shelf_x = (shelf_info.get('x_position') or 0) / 100.0  # cm转米
        shelf_y = (shelf_info.get('y_position') or 0) / 100.0  # cm转米
        shelf_width = 2.0   # 固定宽度2米
        shelf_depth = 0.6   # 固定深度0.6米
        shelf_height = 2.0  # 固定高度2米
        
        # 库位布局参数（每层最大10个库位，1行×10列×5层）
        max_cols_per_layer = 10
        rows_per_layer = 1
        total_layers = 5
        
        # 根据实际商品数量动态调整库位数量
        actual_locations = [loc for loc in locations if loc['layer'] <= total_layers]
        locations_in_shelf = len(actual_locations)
        cols_per_layer = min(max_cols_per_layer, max(1, locations_in_shelf // total_layers))
        
        # 计算库位尺寸（智能间距控制）
        position_width = shelf_width / max_cols_per_layer * 0.8  # 90%用于库位，10%用于间距
        position_depth = shelf_depth / rows_per_layer * 0.5  # 90%用于库位，10%用于间距
        layer_height = shelf_height / total_layers * 0.5  # 80%用于库位，20%用于间距
        
        # 间距计算
        width_spacing = shelf_width / max_cols_per_layer * 0.2
        depth_spacing = shelf_depth / rows_per_layer * 0.1
        height_spacing = shelf_height / total_layers * 0.5
        
        for location in locations:
            try:
                layer = location['layer']
                position = location['position']
                location_id = location['id']
                
                # 验证层数和位置范围
                if layer < 1 or layer > total_layers:
                    continue
                if position < 1 or position > max_cols_per_layer:
                    continue
                
                # 计算库位的3D位置（精确定位）
                col = (position - 1) % max_cols_per_layer  # 列（0-9）
                row = 0  # 固定为第0行（1行布局）
                
                # 三维坐标精确计算
                x = shelf_x + col * (position_width + width_spacing) + width_spacing/2
                y = shelf_y + row * (position_depth + depth_spacing) + depth_spacing/2
                z = 0.01 + (layer - 1) * (layer_height + height_spacing) + height_spacing/2
                
                # 创建库位立方体
                location_box = pv.Box(
                    bounds=[
                        x, x + position_width,
                        y, y + position_depth,
                        z, z + layer_height
                    ]
                )
                
                # 根据库存状态动态设置颜色
                inventory = self.inventory_data.get(location_id, {})
                location_color = self.get_location_color(inventory)
                
                # 添加库位
                location_actor = self.plotter.add_mesh(
                    location_box,
                    color=location_color,
                    opacity=self.opacity_value,
                    show_edges=True,
                    edge_color='black',
                    line_width=0.5,
                    name=f"location_{location_id}"
                )
                
                self.location_actors[location_id] = {
                    'actor': location_actor,
                    'mesh': location_box,
                    'position': [x + position_width/2, y + position_depth/2, z + layer_height/2],
                    'location_data': location,
                    'inventory': inventory
                }
                
            except Exception as e:
                self.logger.error(f"渲染库位 {location.get('id')} 失败: {e}")
    
    def get_location_color(self, inventory: Dict) -> List[float]:
        """根据库存状态获取库位颜色"""
        if not inventory or inventory.get('quantity', 0) == 0:
            return self.STOCK_COLORS['empty']  # 灰色 - 空库位
        
        quantity = inventory.get('quantity', 0)
        warning_threshold = inventory.get('warning_threshold', 30)
        
        if quantity < warning_threshold:
            return self.STOCK_COLORS['low']   # 红色 - 低库存（低于预警值）
        else:
            return self.STOCK_COLORS['high']  # 绿色 - 高库存（高于预警值）
    
    def render_products(self):
        """渲染商品（在库位内显示）"""
        if not self.show_products or not self.inventory_data:
            return
        
        for location_id, location_actor_info in self.location_actors.items():
            try:
                inventory = self.inventory_data.get(location_id)
                if not inventory or inventory['quantity'] == 0:
                    continue
                
                # 获取库位位置和尺寸信息
                pos = location_actor_info['position']
                location_data = location_actor_info['location_data']
                
                # 根据库存量计算商品高度（在库位内）
                quantity = inventory['quantity']
                fill_ratio = min(quantity / 100.0, 0.8)  # 最多填充80%的库位高度
                
                # 计算商品尺寸（稍小于库位）
                product_width = 0.02  # 固定宽度
                product_depth = 0.15  # 固定深度
                product_height = 0.25 * fill_ratio  # 根据库存量调整高度
                
                # 创建商品立方体（在库位中心）
                product_box = pv.Box(
                    bounds=[
                        pos[0] - product_width/2, pos[0] + product_width/2,
                        pos[1] - product_depth/2, pos[1] + product_depth/2,
                        pos[2] - 0.1, pos[2] - 0.1 + product_height
                    ]
                )
                
                # 获取商品颜色（根据库存状态，使用更鲜艳的颜色）
                if quantity >= 70:
                    product_color = [0.0, 0.8, 0.0]  # 深绿色 - 高库存
                elif quantity >= 30:
                    product_color = [1.0, 0.8, 0.0]  # 橙色 - 中等库存
                else:
                    product_color = [0.8, 0.0, 0.0]  # 深红色 - 低库存
                
                # 添加商品
                product_actor = self.plotter.add_mesh(
                    product_box,
                    color=product_color,
                    opacity=0.9,
                    show_edges=True,
                    edge_color='black',
                    line_width=0.5,
                    name=f"product_{location_id}"
                )
                
                self.product_actors[location_id] = {
                    'actor': product_actor,
                    'mesh': product_box,
                    'inventory': inventory,
                    'location_data': location_data
                }
                
            except Exception as e:
                self.logger.error(f"渲染商品 {location_id} 失败: {e}")
    
    def on_point_picked(self, point):
        """点选回调"""
        try:
            # 查找最近的库位
            closest_location = self.find_closest_location(point)
            if closest_location:
                self.select_location(closest_location)
                # 显示库位简单信息
                self.show_location_tooltip(closest_location, point)
        except Exception as e:
            self.logger.error(f"点选处理失败: {e}")
    
    def on_cell_picked(self, mesh):
        """单元格选择回调"""
        try:
            # 从网格名称获取位置ID
            if hasattr(mesh, 'name') and mesh.name:
                if mesh.name.startswith('location_'):
                    location_id = int(mesh.name.split('_')[1])
                    self.select_location_by_id(location_id)
                    # 如果库位有商品，显示商品信息
                    inventory = self.inventory_data.get(location_id, {})
                    if inventory.get('quantity', 0) > 0:
                        self.show_product_info(location_id)
                elif mesh.name.startswith('product_'):
                    location_id = int(mesh.name.split('_')[1])
                    self.show_product_info(location_id)
        except Exception as e:
            self.logger.error(f"单元格选择处理失败: {e}")
    
    def find_closest_location(self, point) -> Optional[int]:
        """查找最近的库位"""
        min_distance = float('inf')
        closest_location_id = None
        
        for location_id, actor_info in self.location_actors.items():
            pos = actor_info['position']
            distance = np.linalg.norm(np.array(point) - np.array(pos))
            
            if distance < min_distance:
                min_distance = distance
                closest_location_id = location_id
        
        return closest_location_id if min_distance < 1.0 else None
    
    def select_location(self, location_id: int):
        """选择库位"""
        self.selected_location = location_id
        
        # 更新选择信息显示
        if location_id in self.location_actors:
            location_data = self.location_actors[location_id]['location_data']
            inventory = self.inventory_data.get(location_id, {})
            
            info_text = f"库位: {location_data['location_code']} | "
            info_text += f"层: {location_data['layer']} | "
            info_text += f"位置: {location_data['position']} | "
            info_text += f"库存: {inventory.get('quantity', 0)}"
            
            self.selection_label.setText(info_text)
        
        # 高亮显示选中的库位
        self.highlight_selected_location()
    
    def select_location_by_id(self, location_id: int):
        """根据ID选择库位"""
        self.select_location(location_id)
    
    def highlight_selected_location(self):
        """高亮显示选中的库位"""
        if not self.selected_location or not self.plotter:
            return
        
        try:
            # 移除之前的高亮
            if hasattr(self, 'highlight_actor') and self.highlight_actor:
                self.plotter.remove_actor(self.highlight_actor)
            
            # 添加新的高亮
            if self.selected_location in self.location_actors:
                actor_info = self.location_actors[self.selected_location]
                mesh = actor_info['mesh']
                
                # 创建高亮边框
                self.highlight_actor = self.plotter.add_mesh(
                    mesh,
                    style='wireframe',
                    color='yellow',
                    line_width=4,
                    name='highlight'
                )
                
                self.plotter.render()
                
        except Exception as e:
            self.logger.error(f"高亮显示失败: {e}")
    
    def show_location_tooltip(self, location_id: int, point):
        """显示库位简单信息（鼠标悬停）"""
        if location_id not in self.location_actors:
            return
        
        location_data = self.location_actors[location_id]['location_data']
        inventory = self.inventory_data.get(location_id, {})
        
        # 构建简单信息文本
        tooltip_text = f"库位: {location_data['location_code']}\n"
        if inventory.get('product_name'):
            tooltip_text += f"商品: {inventory['product_name']}\n"
            tooltip_text += f"库存: {inventory['quantity']}"
        else:
            tooltip_text += "空库位"
        
        # 更新状态栏显示
        self.selection_label.setText(tooltip_text.replace('\n', ' | '))
    
    def show_product_info(self, location_id: int):
        """显示商品详细信息"""
        if location_id not in self.inventory_data:
            QMessageBox.information(self, "提示", "该库位无商品信息")
            return
        
        inventory = self.inventory_data[location_id]
        if inventory.get('quantity', 0) == 0:
            QMessageBox.information(self, "提示", "该库位为空库位")
            return
        
        location_data = self.location_actors[location_id]['location_data']
        
        # 创建信息对话框
        dialog = ProductInfoDialog(self, location_data, inventory)
        dialog.exec()
    
    def toggle_products_display(self, checked: bool):
        """切换商品显示"""
        self.show_products = checked
        
        if not self.plotter:
            return
        
        # 移除或添加商品
        for location_id, product_info in self.product_actors.items():
            if checked:
                # 重新添加商品
                pass  # 商品已经存在
            else:
                # 移除商品
                self.plotter.remove_actor(product_info['actor'])
        
        if checked and not self.product_actors:
            # 重新渲染商品
            self.render_products()
        
        self.plotter.render()
    
    def toggle_wireframe_display(self, checked: bool):
        """切换线框显示"""
        self.show_wireframe = checked
        
        if not self.plotter:
            return
        
        # 重新渲染场景
        self.render_3d_scene()
    
    def on_opacity_changed(self, value: int):
        """透明度改变"""
        self.opacity_value = value / 100.0
        self.opacity_label.setText(f"{value}%")
        
        # 更新所有库位的透明度
        for location_id, actor_info in self.location_actors.items():
            actor_info['actor'].GetProperty().SetOpacity(self.opacity_value)
        
        if self.plotter:
            self.plotter.render()
    
    def reset_camera_view(self):
        """重置相机视角"""
        if self.plotter:
            self.plotter.reset_camera()
            # 设置适合6m×10m仓库的相机位置
            self.plotter.camera_position = [(8, -5, 8), (3, 5, 1), (0, 0, 1)]
            self.plotter.render()
    
    def refresh_data(self):
        """刷新数据"""
        if self.current_warehouse_id:
            self.load_warehouse_data()
    
    def update_statistics(self):
        """更新统计信息"""
        if not self.shelves_data or not self.locations_data:
            self.stats_label.setText("无数据")
            return
        
        total_shelves = len(self.shelves_data)
        total_locations = len(self.locations_data)
        occupied_locations = sum(1 for inv in self.inventory_data.values() if inv['quantity'] > 0)
        
        stats_text = f"货架: {total_shelves} | 库位: {total_locations} | 已占用: {occupied_locations}"
        self.stats_label.setText(stats_text)
    
    def show_error_message(self, message: str = None):
        """显示错误信息"""
        if not message:
            message = "PyVista未安装或初始化失败，请安装PyVista以使用3D功能"
        
        QMessageBox.critical(self, "错误", message)
    
    def cleanup_3d(self):
        """清理3D资源"""
        try:
            if self.plotter and PYVISTA_AVAILABLE:
                # 清空场景
                self.plotter.clear()
                
                # 禁用交互
                try:
                    self.plotter.disable_picking()
                except:
                    pass
                
                # 关闭绘图器
                try:
                    self.plotter.close()
                except:
                    pass
                    
                self.plotter = None
                
            # 清空数据
            self.shelf_actors = {}
            self.location_actors = {}
            self.product_actors = {}
            
        except Exception as e:
            self.logger.error(f"清理3D资源时出现错误: {e}")
    
    def set_styles(self):
        """设置样式"""
        self.setStyleSheet("""
            QWidget {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #E3F2FD, stop:1 #BBDEFB);  /* 设置背景颜色 */
                font-family: "Microsoft YaHei", Arial, sans-serif;  /* 设置字体 */
            }
            
            #controlPanel {
                background: rgba(255, 255, 255, 0.9);
                border: 1px solid #CCCCCC;
                border-radius: 8px;
                font-weight: bold;
                padding: 10px;
            }
            
            #statusFrame {
                background: rgba(255, 255, 255, 0.8);  /* 设置背景颜色 */
                border: 1px solid #CCCCCC;
                border-radius: 4px;
            }
            
            QComboBox {
                border: 1px solid #CCCCCC;  /* 设置边框颜色 */
                border-radius: 4px;  /* 设置圆角 */
                padding: 5px;  /* 设置内边距 */
                min-width: 150px;  /* 设置最小宽度 */
            }
            
            QPushButton {
                border: none;   /* 设置边框为无 */
                border-radius: 4px;  /* 设置圆角 */
                padding: 8px 16px;  /* 设置内边距 */
                font-weight: bold;  /* 设置字体加粗 */
                color: black;  /* 设置字体颜色 */
                min-width: 80px;  /* 设置最小宽度 */
            }
            
            #refreshButton {
                background: #4CAF50;  /* 设置背景颜色 */
                color: white !important;  /* 设置字体颜色为白色，使用!important确保优先级 */
            }
            
            #refreshButton:hover {
                background: #45A049;    /* 设置鼠标悬停时的背景颜色 */
                color: white !important;   /* 设置鼠标悬停时的字体颜色为白色 */
            }
            
            #resetViewButton {
                background: #2196F3;  /* 设置背景颜色 */
                color: white !important;  /* 设置字体颜色为白色，使用!important确保优先级 */
            }
            
            #resetViewButton:hover {
                background: #1976D2;  /* 设置鼠标悬停时的背景颜色 */
                color: white !important;  /* 设置鼠标悬停时的字体颜色为白色 */
            }
            
            QCheckBox {
                font-size: 14px;
                spacing: 8px;
            }
            
            QSlider::groove:horizontal {
                border: 1px solid #CCCCCC;
                height: 6px;
                background: #F0F0F0;
                border-radius: 3px;
            }
            
            QSlider::handle:horizontal {
                background: #2196F3;
                border: 1px solid #1976D2;
                width: 16px;
                height: 16px;
                border-radius: 8px;
                margin: -5px 0;
            }
            
            #statsLabel, #selectionLabel {
                font-size: 12px;
                color: #333333;
                background: transparent;
            }
            
            #selectionLabel {
                font-weight: bold;
                color: #1976D2;
            }
        """)


class ProductInfoDialog(QDialog):
    """商品信息对话框"""
    
    def __init__(self, parent=None, location_data=None, inventory_data=None):
        super().__init__(parent)
        self.location_data = location_data or {}
        self.inventory_data = inventory_data or {}
        
        self.init_ui()
    
    def init_ui(self):
        """初始化界面"""
        self.setWindowTitle("库位详细信息")
        self.setModal(True)
        self.resize(400, 300)
        
        layout = QVBoxLayout(self)
        layout.setSpacing(15)
        
        # 标题
        title = QLabel(f"库位信息 - {self.location_data.get('location_code', 'N/A')}")
        title.setObjectName("dialogTitle")
        title.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(title)
        
        # 信息表单
        form_layout = QFormLayout()
        
        # 位置信息
        form_layout.addRow("仓库名称:", QLabel(self.inventory_data.get('warehouse_name', 'N/A')))
        form_layout.addRow("货架编码:", QLabel(self.inventory_data.get('shelf_code', 'N/A')))
        form_layout.addRow("层数:", QLabel(f"第{self.inventory_data.get('layer', 'N/A')}层"))
        form_layout.addRow("位置:", QLabel(f"第{self.inventory_data.get('position', 'N/A')}列"))
        form_layout.addRow("库位编码:", QLabel(self.inventory_data.get('location_code', 'N/A')))
        
        # 分隔线
        separator = QLabel("━━━━━━━━━━━━━━━━━━━━")
        separator.setAlignment(Qt.AlignmentFlag.AlignCenter)
        separator.setStyleSheet("color: #CCCCCC;")
        form_layout.addRow("", separator)
        
        # 商品信息
        if self.inventory_data.get('product_name'):
            form_layout.addRow("商品名称:", QLabel(self.inventory_data['product_name']))
        
        # 库存信息
        quantity = self.inventory_data.get('quantity', 0)
        warning_threshold = self.inventory_data.get('warning_threshold', 30)
        
        form_layout.addRow("库存数量:", QLabel(f"{quantity} 件"))
        form_layout.addRow("库存预警值:", QLabel(f"{warning_threshold} 件"))
        form_layout.addRow("库存状态:", QLabel(self.get_status_text()))
        
        # 库存占用率
        fill_ratio = min(quantity / 100.0, 1.0) * 100
        form_layout.addRow("占用率:", QLabel(f"{fill_ratio:.1f}%"))
        
        # 库存等级颜色指示（基于预警值）
        color_indicator = QLabel("●")
        if quantity == 0:
            color_indicator.setStyleSheet("color: gray; font-size: 20px;")
            color_text = "空库位"
        elif quantity < warning_threshold:
            color_indicator.setStyleSheet("color: red; font-size: 20px;")
            color_text = "低库存预警"
        else:
            color_indicator.setStyleSheet("color: green; font-size: 20px;")
            color_text = "库存充足"
        
        color_layout = QHBoxLayout()
        color_layout.addWidget(color_indicator)
        color_layout.addWidget(QLabel(color_text))
        color_layout.addStretch()
        
        color_widget = QWidget()
        color_widget.setLayout(color_layout)
        form_layout.addRow("库存等级:", color_widget)
        
        layout.addLayout(form_layout)
        
        # 按钮
        button_layout = QHBoxLayout()
        button_layout.addStretch()
        
        close_btn = QPushButton("关闭")
        close_btn.clicked.connect(self.accept)
        button_layout.addWidget(close_btn)
        
        layout.addLayout(button_layout)
        
        self.set_styles()
    
    def get_status_text(self) -> str:
        """获取状态文本"""
        status = self.inventory_data.get('status', 'empty')
        quantity = self.inventory_data.get('quantity', 0)
        warning_threshold = self.inventory_data.get('warning_threshold', 30)
        
        if status == 'empty':
            return '空库位'
        elif status == 'low':
            return f'低库存预警（低于{warning_threshold}件）'
        elif status == 'high':
            return f'库存充足（高于{warning_threshold}件）'
        else:
            return '未知状态'
    
    def set_styles(self):
        """设置样式"""
        self.setStyleSheet("""
            QDialog {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #E3F2FD, stop:1 #BBDEFB);
                font-family: "Microsoft YaHei", Arial, sans-serif;
            }
            
            #dialogTitle {
                font-size: 16px;
                font-weight: bold;
                color: #1976D2;
                padding: 10px;
            }
            
            QLabel {
                color: #333333;
                background: transparent;
            }
            
            QPushButton {
                border: none;
                border-radius: 4px;
                padding: 8px 16px;
                font-weight: bold;
                color: white;
                background: #2196F3;
                min-width: 80px;
            }
            
            QPushButton:hover {
                background: #1976D2;
            }
        """) 