#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
仓库添加/编辑对话框
"""

import os
import sys
from typing import Dict, Any, Optional
from PyQt6.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QFormLayout, QLabel, 
    QLineEdit, QTextEdit, QPushButton, QMessageBox, QCheckBox, QComboBox
)
from PyQt6.QtCore import Qt

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

class WarehouseDialog(QDialog):
    """仓库添加/编辑对话框"""
    
    def __init__(self, parent=None, controller=None, warehouse_data=None):
        super().__init__(parent)
        self.controller = controller
        self.warehouse_data = warehouse_data
        self.is_edit_mode = warehouse_data is not None
        
        self.init_ui()
        self.load_data()
        
        if self.is_edit_mode:
            self.populate_form()
    
    def init_ui(self):
        """初始化界面"""
        self.setWindowTitle("编辑仓库" if self.is_edit_mode else "添加仓库")
        self.setModal(True)
        self.resize(600, 500)
        
        layout = QVBoxLayout(self)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(20)
        
        # 创建表单
        self.create_form()
        
        # 创建按钮
        self.create_buttons()
        
        # 设置样式
        self.set_styles()
    
    def create_form(self):
        """创建表单"""
        form_layout = QFormLayout()
        form_layout.setSpacing(15)
        
        # 所属厂区
        self.factory_combo = QComboBox()
        form_layout.addRow("所属厂区*:", self.factory_combo)
        
        # 仓库编码
        self.code_edit = QLineEdit()
        self.code_edit.setPlaceholderText("如：ZCK（主仓库）、WZK（物资仓库）、PJK（配件仓库）")
        form_layout.addRow("仓库编码*:", self.code_edit)
        
        # 仓库名称
        self.name_edit = QLineEdit()
        self.name_edit.setPlaceholderText("请输入仓库名称")
        form_layout.addRow("仓库名称*:", self.name_edit)
        
        # 仓库类型
        self.type_combo = QComboBox()
        self.type_combo.addItem("通用仓库", "general")
        self.type_combo.addItem("物资仓库", "material")
        self.type_combo.addItem("配件仓库", "parts")
        self.type_combo.addItem("成品仓库", "finished")
        form_layout.addRow("仓库类型*:", self.type_combo)
        
        # 负责人
        self.manager_edit = QLineEdit()
        self.manager_edit.setPlaceholderText("请输入仓库负责人")
        form_layout.addRow("负责人:", self.manager_edit)
        
        # 联系电话
        self.phone_edit = QLineEdit()
        self.phone_edit.setPlaceholderText("请输入联系电话")
        form_layout.addRow("联系电话:", self.phone_edit)
        
        # 仓库面积
        self.area_edit = QLineEdit()
        self.area_edit.setPlaceholderText("如：1000平方米")
        form_layout.addRow("仓库面积:", self.area_edit)
        
        # 仓库容量
        self.capacity_edit = QLineEdit()
        self.capacity_edit.setPlaceholderText("如：5000立方米")
        form_layout.addRow("仓库容量:", self.capacity_edit)
        
        # 仓库地址
        self.address_edit = QTextEdit()
        self.address_edit.setPlaceholderText("请输入仓库详细地址")
        self.address_edit.setMaximumHeight(80)
        form_layout.addRow("仓库地址:", self.address_edit)
        
        # 仓库描述
        self.description_edit = QTextEdit()
        self.description_edit.setPlaceholderText("请输入仓库描述")
        self.description_edit.setMaximumHeight(80)
        form_layout.addRow("仓库描述:", self.description_edit)
        
        # 是否启用
        self.active_checkbox = QCheckBox("启用该仓库")
        self.active_checkbox.setChecked(True)
        form_layout.addRow("状态:", self.active_checkbox)
        
        self.layout().addLayout(form_layout)
    
    def create_buttons(self):
        """创建按钮"""
        button_layout = QHBoxLayout()
        button_layout.addStretch()
        
        # 取消按钮
        cancel_btn = QPushButton("取消")
        cancel_btn.setObjectName("cancelButton")
        cancel_btn.clicked.connect(self.reject)
        button_layout.addWidget(cancel_btn)
        
        # 保存按钮
        save_btn = QPushButton("保存")
        save_btn.setObjectName("saveButton")
        save_btn.clicked.connect(self.save_warehouse)
        button_layout.addWidget(save_btn)
        
        self.layout().addLayout(button_layout)
    
    def load_data(self):
        """加载数据"""
        # 加载厂区
        factories = self.controller.get_factories()
        self.factory_combo.clear()
        self.factory_combo.addItem("请选择厂区", None)
        for factory in factories:
            self.factory_combo.addItem(factory['name'], factory['id'])
    
    def populate_form(self):
        """填充表单（编辑模式）"""
        if not self.warehouse_data:
            return
        
        # 设置厂区
        factory_id = self.warehouse_data.get('factory_id')
        if factory_id:
            for i in range(self.factory_combo.count()):
                if self.factory_combo.itemData(i) == factory_id:
                    self.factory_combo.setCurrentIndex(i)
                    break
        
        self.code_edit.setText(self.warehouse_data.get('code', ''))
        self.name_edit.setText(self.warehouse_data.get('name', ''))
        
        # 设置仓库类型
        warehouse_type = self.warehouse_data.get('type', 'general')
        for i in range(self.type_combo.count()):
            if self.type_combo.itemData(i) == warehouse_type:
                self.type_combo.setCurrentIndex(i)
                break
        
        self.manager_edit.setText(self.warehouse_data.get('manager', ''))
        self.phone_edit.setText(self.warehouse_data.get('phone', ''))
        self.area_edit.setText(self.warehouse_data.get('area', ''))
        self.capacity_edit.setText(self.warehouse_data.get('capacity', ''))
        self.address_edit.setPlainText(self.warehouse_data.get('address', ''))
        self.description_edit.setPlainText(self.warehouse_data.get('description', ''))
        self.active_checkbox.setChecked(self.warehouse_data.get('is_active', True))
    
    def validate_form(self) -> bool:
        """验证表单"""
        # 检查必填字段
        if self.factory_combo.currentData() is None:
            QMessageBox.warning(self, "验证失败", "请选择所属厂区")
            self.factory_combo.setFocus()
            return False
        
        if not self.code_edit.text().strip():
            QMessageBox.warning(self, "验证失败", "请输入仓库编码")
            self.code_edit.setFocus()
            return False
        
        if not self.name_edit.text().strip():
            QMessageBox.warning(self, "验证失败", "请输入仓库名称")
            self.name_edit.setFocus()
            return False
        
        if self.type_combo.currentData() is None:
            QMessageBox.warning(self, "验证失败", "请选择仓库类型")
            self.type_combo.setFocus()
            return False
        
        # 验证编码格式
        code = self.code_edit.text().strip()
        if len(code) < 2:
            QMessageBox.warning(self, "验证失败", "仓库编码长度至少为2个字符")
            self.code_edit.setFocus()
            return False
        
        return True
    
    def get_form_data(self) -> Dict[str, Any]:
        """获取表单数据"""
        return {
            'factory_id': self.factory_combo.currentData(),
            'code': self.code_edit.text().strip(),
            'name': self.name_edit.text().strip(),
            'type': self.type_combo.currentData(),
            'manager': self.manager_edit.text().strip() or None,
            'phone': self.phone_edit.text().strip() or None,
            'area': self.area_edit.text().strip() or None,
            'capacity': self.capacity_edit.text().strip() or None,
            'address': self.address_edit.toPlainText().strip() or None,
            'description': self.description_edit.toPlainText().strip() or None,
            'is_active': self.active_checkbox.isChecked()
        }
    
    def save_warehouse(self):
        """保存仓库"""
        if not self.validate_form():
            return
        
        data = self.get_form_data()
        
        try:
            if self.is_edit_mode:
                # 编辑模式
                success = self.controller.update_warehouse(self.warehouse_data['id'], data)
            else:
                # 添加模式
                success = self.controller.create_warehouse(data)
            
            if success:
                self.accept()
        
        except Exception as e:
            QMessageBox.critical(self, "错误", f"保存失败: {str(e)}")
    
    def set_styles(self):
        """设置样式"""
        self.setStyleSheet("""
            QDialog {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #E3F2FD, stop:1 #BBDEFB);
                font-family: "Microsoft YaHei", Arial, sans-serif;
            }
            
            QLabel {
                color: #333333;
                font-size: 14px;
                font-weight: normal;
                background: transparent;
            }
            
            QLineEdit {
                border: 1px solid #D0D0D0;
                border-radius: 4px;
                padding: 8px 12px;
                font-size: 14px;
                background: white;
                min-height: 20px;
                color: #333333;
            }
            
            QLineEdit:focus {
                border-color: #2196F3;
                background: white;
            }
            
            QComboBox {
                border: 1px solid #D0D0D0;
                border-radius: 4px;
                padding: 8px 12px;
                font-size: 14px;
                background: white;
                min-width: 120px;
                color: #333333;
            }
            
            QComboBox:focus {
                border-color: #2196F3;
            }
            
            QComboBox::drop-down {
                border: none;
                width: 20px;
                background: transparent;
            }
            
            QComboBox::down-arrow {
                image: none;
                border-left: 5px solid transparent;
                border-right: 5px solid transparent;
                border-top: 5px solid #666666;
                margin-right: 5px;
            }
            
            QComboBox QAbstractItemView {
                background: white;
                border: 1px solid #D0D0D0;
                selection-background-color: #E3F2FD;
                color: #333333;
            }
            
            QTextEdit {
                border: 1px solid #D0D0D0;
                border-radius: 4px;
                padding: 8px;
                font-size: 14px;
                background: white;
                color: #333333;
            }
            
            QTextEdit:focus {
                border-color: #2196F3;
                background: white;
            }
            
            QCheckBox {
                font-size: 14px;
                color: #333333;
                spacing: 8px;
            }
            
            QCheckBox::indicator {
                width: 18px;
                height: 18px;
                border: 1px solid #D0D0D0;
                border-radius: 3px;
                background: white;
            }
            
            QCheckBox::indicator:checked {
                background: #2196F3;
                border-color: #2196F3;
            }
            
            QPushButton {
                border: none;
                border-radius: 4px;
                padding: 8px 16px;
                font-size: 14px;
                font-weight: normal;
                min-width: 70px;
                color: white;
            }
            
            #saveButton {
                background: #4CAF50;
            }
            
            #saveButton:hover {
                background: #45A049;
            }
            
            #cancelButton {
                background: #9E9E9E;
            }
            
            #cancelButton:hover {
                background: #757575;
            }
        """) 