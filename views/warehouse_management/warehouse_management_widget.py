#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
仓库管理主组件
包含厂区管理、仓库管理、货架管理、库位布局
支持仓储设施的层级管理和3D可视化布局
"""

import os
import sys
from typing import Dict, Any, List
from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QTabWidget, QTableWidget, 
    QTableWidgetItem, QPushButton, QLineEdit, QComboBox, QLabel,
    QDialog, QMessageBox, QHeaderView, QFrame, QSplitter
)
from PyQt6.QtCore import Qt, pyqtSignal
from PyQt6.QtGui import QFont

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from controllers.warehouse_controller import WarehouseController
from services.log_service import LogService

class WarehouseManagementWidget(QWidget):
    """仓库管理主组件

    功能模块：
    - 厂区管理：工厂厂区的基础信息管理
    - 仓库管理：各类仓库的建立和维护
    - 货架管理：仓库内货架的布局和管理
    - 库位布局：3D可视化的库位空间管理
    - 层级筛选：支持厂区-仓库-货架的层级筛选
    """
    
    def __init__(self):
        super().__init__()
        self.controller = WarehouseController()
        self.logger = LogService.get_logger(__name__)
        
        self.init_ui()
        self.connect_signals()
        self.load_initial_data()
    
    def init_ui(self):
        """初始化界面"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(5, 5, 5, 5)
        layout.setSpacing(15)
        
        # 创建标签页
        self.tab_widget = QTabWidget()
        self.tab_widget.setObjectName("subTabWidget")
        
        # 厂区管理标签页
        self.create_factory_tab()
        
        # 仓库管理标签页
        self.create_warehouse_tab()
        
        # 货架管理标签页
        self.create_shelf_tab()
        
        # 库位布局标签页
        self.create_layout_tab()
        
        layout.addWidget(self.tab_widget)
        
        # 设置样式
        self.set_styles()
    
    def create_factory_tab(self):
        """创建厂区管理标签页"""
        factory_widget = QWidget()
        layout = QVBoxLayout(factory_widget)
        layout.setContentsMargins(5, 5, 5, 5)
        layout.setSpacing(15)
        
        top_layout = QHBoxLayout()
        
        top_layout.addStretch()

        # 操作按钮
        button_layout = QHBoxLayout()
        
        add_factory_btn = QPushButton("添加厂区")
        add_factory_btn.setObjectName("addButton")
        add_factory_btn.clicked.connect(self.add_factory)
        button_layout.addWidget(add_factory_btn)
        
        self.edit_factory_btn = QPushButton("编辑厂区")
        self.edit_factory_btn.setObjectName("editButton")
        self.edit_factory_btn.clicked.connect(self.edit_factory)
        self.edit_factory_btn.setEnabled(False)
        button_layout.addWidget(self.edit_factory_btn)
        
        self.delete_factory_btn = QPushButton("删除厂区")
        self.delete_factory_btn.setObjectName("deleteButton")
        self.delete_factory_btn.clicked.connect(self.delete_factory)
        self.delete_factory_btn.setEnabled(False)
        button_layout.addWidget(self.delete_factory_btn)
        
        button_layout.addStretch()
        layout.addLayout(button_layout)
        
        # 厂区列表表格
        self.factory_table = QTableWidget()
        self.factory_table.setObjectName("factoryTable")
        
        headers = ["ID", "厂区编码", "厂区名称", "负责人", "联系电话", "地址", "状态"]
        self.factory_table.setColumnCount(len(headers))
        self.factory_table.setHorizontalHeaderLabels(headers)
        
        self.factory_table.setSelectionBehavior(QTableWidget.SelectionBehavior.SelectRows)
        self.factory_table.setSelectionMode(QTableWidget.SelectionMode.SingleSelection)
        self.factory_table.setAlternatingRowColors(True)
        
        # 设置列宽
        header = self.factory_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.ResizeMode.Fixed)
        header.setSectionResizeMode(1, QHeaderView.ResizeMode.Fixed)
        header.setSectionResizeMode(2, QHeaderView.ResizeMode.Stretch)
        header.setSectionResizeMode(3, QHeaderView.ResizeMode.Fixed)
        header.setSectionResizeMode(4, QHeaderView.ResizeMode.Fixed)
        header.setSectionResizeMode(5, QHeaderView.ResizeMode.Stretch)
        header.setSectionResizeMode(6, QHeaderView.ResizeMode.Fixed)
        
        self.factory_table.setColumnWidth(0, 60)
        self.factory_table.setColumnWidth(1, 120)
        self.factory_table.setColumnWidth(3, 100)
        self.factory_table.setColumnWidth(4, 120)
        self.factory_table.setColumnWidth(6, 80)
        
        self.factory_table.itemSelectionChanged.connect(self.on_factory_selection_changed)
        self.factory_table.itemDoubleClicked.connect(self.edit_factory)
        
        layout.addWidget(self.factory_table)
        
        self.tab_widget.addTab(factory_widget, "厂区管理")
    
    def create_warehouse_tab(self):
        """创建仓库管理标签页"""
        warehouse_widget = QWidget()
        layout = QVBoxLayout(warehouse_widget)
        layout.setContentsMargins(5, 5, 5, 5)
        layout.setSpacing(15)
        
        # 操作按钮
        button_layout = QHBoxLayout()
        
        add_warehouse_btn = QPushButton("添加仓库")
        add_warehouse_btn.setObjectName("addButton")
        add_warehouse_btn.clicked.connect(self.add_warehouse)
        button_layout.addWidget(add_warehouse_btn)
        
        self.edit_warehouse_btn = QPushButton("编辑仓库")
        self.edit_warehouse_btn.setObjectName("editButton")
        self.edit_warehouse_btn.clicked.connect(self.edit_warehouse)
        self.edit_warehouse_btn.setEnabled(False)
        button_layout.addWidget(self.edit_warehouse_btn)
        
        self.delete_warehouse_btn = QPushButton("删除仓库")
        self.delete_warehouse_btn.setObjectName("deleteButton")
        self.delete_warehouse_btn.clicked.connect(self.delete_warehouse)
        self.delete_warehouse_btn.setEnabled(False)
        button_layout.addWidget(self.delete_warehouse_btn)
        
        self.view_layout_btn = QPushButton("查看布局")
        self.view_layout_btn.setObjectName("viewButton")
        self.view_layout_btn.clicked.connect(self.view_warehouse_layout)
        self.view_layout_btn.setEnabled(False)
        button_layout.addWidget(self.view_layout_btn)

        button_layout.addStretch()  # 添加一个伸缩空间，使其他控件可以自动填充剩余空间

        # 厂区筛选
        button_layout.addWidget(QLabel("厂区:"))
        self.factory_filter_combo = QComboBox()
        self.factory_filter_combo.setObjectName("factoryFilterCombo")
        self.factory_filter_combo.currentTextChanged.connect(self.filter_warehouses)
        button_layout.addWidget(self.factory_filter_combo)
        
        layout.addLayout(button_layout) # 将操作按钮添加到布局中
        
        # 仓库列表表格
        self.warehouse_table = QTableWidget()
        self.warehouse_table.setObjectName("warehouseTable")
        
        headers = ["ID", "厂区", "仓库编码", "仓库名称", "类型", "负责人", "面积", "容量", "状态"]
        self.warehouse_table.setColumnCount(len(headers))
        self.warehouse_table.setHorizontalHeaderLabels(headers)
        
        self.warehouse_table.setSelectionBehavior(QTableWidget.SelectionBehavior.SelectRows)
        self.warehouse_table.setSelectionMode(QTableWidget.SelectionMode.SingleSelection)
        self.warehouse_table.setAlternatingRowColors(True)
        
        # 设置列宽
        header = self.warehouse_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.ResizeMode.Fixed)
        header.setSectionResizeMode(1, QHeaderView.ResizeMode.Fixed)
        header.setSectionResizeMode(2, QHeaderView.ResizeMode.Fixed)
        header.setSectionResizeMode(3, QHeaderView.ResizeMode.Stretch)
        header.setSectionResizeMode(4, QHeaderView.ResizeMode.Fixed)
        header.setSectionResizeMode(5, QHeaderView.ResizeMode.Fixed)
        header.setSectionResizeMode(6, QHeaderView.ResizeMode.Fixed)
        header.setSectionResizeMode(7, QHeaderView.ResizeMode.Fixed)
        header.setSectionResizeMode(8, QHeaderView.ResizeMode.Fixed)
        
        self.warehouse_table.setColumnWidth(0, 60)
        self.warehouse_table.setColumnWidth(1, 100)
        self.warehouse_table.setColumnWidth(2, 120)
        self.warehouse_table.setColumnWidth(4, 80)
        self.warehouse_table.setColumnWidth(5, 100)
        self.warehouse_table.setColumnWidth(6, 80)
        self.warehouse_table.setColumnWidth(7, 80)
        self.warehouse_table.setColumnWidth(8, 80)
        
        self.warehouse_table.itemSelectionChanged.connect(self.on_warehouse_selection_changed)
        self.warehouse_table.itemDoubleClicked.connect(self.edit_warehouse)
        
        layout.addWidget(self.warehouse_table)
        
        self.tab_widget.addTab(warehouse_widget, "仓库管理")
    
    def create_shelf_tab(self):
        """创建货架管理标签页"""
        shelf_widget = QWidget()
        layout = QVBoxLayout(shelf_widget)
        layout.setContentsMargins(5, 5, 5, 5)
        layout.setSpacing(15)
        

        
        # 操作按钮
        button_layout = QHBoxLayout()
        
        add_shelf_btn = QPushButton("添加货架")
        add_shelf_btn.setObjectName("addButton")
        add_shelf_btn.clicked.connect(self.add_shelf)
        button_layout.addWidget(add_shelf_btn)
        
        self.edit_shelf_btn = QPushButton("编辑货架")
        self.edit_shelf_btn.setObjectName("editButton")
        self.edit_shelf_btn.clicked.connect(self.edit_shelf)
        self.edit_shelf_btn.setEnabled(False)
        button_layout.addWidget(self.edit_shelf_btn)
        
        self.delete_shelf_btn = QPushButton("删除货架")
        self.delete_shelf_btn.setObjectName("deleteButton")
        self.delete_shelf_btn.clicked.connect(self.delete_shelf)
        self.delete_shelf_btn.setEnabled(False)
        button_layout.addWidget(self.delete_shelf_btn)
        
        button_layout.addStretch()

        button_layout.addWidget(QLabel("仓库:"))
        self.warehouse_filter_combo = QComboBox()
        self.warehouse_filter_combo.setObjectName("warehouseFilterCombo")
        self.warehouse_filter_combo.currentTextChanged.connect(self.filter_shelves)
        button_layout.addWidget(self.warehouse_filter_combo)
        button_layout.addWidget(self.warehouse_filter_combo)
        layout.addLayout(button_layout)
        
        # 货架列表表格
        self.shelf_table = QTableWidget()
        self.shelf_table.setObjectName("shelfTable")
        
        headers = ["ID", "仓库", "货架编码", "货架名称", "层数", "每层位置数", "X坐标", "Y坐标", "状态"]
        self.shelf_table.setColumnCount(len(headers))
        self.shelf_table.setHorizontalHeaderLabels(headers)
        
        self.shelf_table.setSelectionBehavior(QTableWidget.SelectionBehavior.SelectRows)
        self.shelf_table.setSelectionMode(QTableWidget.SelectionMode.SingleSelection)
        self.shelf_table.setAlternatingRowColors(True)
        
        # 设置列宽
        header = self.shelf_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.ResizeMode.Fixed)
        header.setSectionResizeMode(1, QHeaderView.ResizeMode.Fixed)
        header.setSectionResizeMode(2, QHeaderView.ResizeMode.Fixed)
        header.setSectionResizeMode(3, QHeaderView.ResizeMode.Stretch)
        header.setSectionResizeMode(4, QHeaderView.ResizeMode.Fixed)
        header.setSectionResizeMode(5, QHeaderView.ResizeMode.Fixed)
        header.setSectionResizeMode(6, QHeaderView.ResizeMode.Fixed)
        header.setSectionResizeMode(7, QHeaderView.ResizeMode.Fixed)
        header.setSectionResizeMode(8, QHeaderView.ResizeMode.Fixed)
        
        self.shelf_table.setColumnWidth(0, 60)
        self.shelf_table.setColumnWidth(1, 120)
        self.shelf_table.setColumnWidth(2, 120)
        self.shelf_table.setColumnWidth(4, 60)
        self.shelf_table.setColumnWidth(5, 80)
        self.shelf_table.setColumnWidth(6, 60)
        self.shelf_table.setColumnWidth(7, 60)
        self.shelf_table.setColumnWidth(8, 80)
        
        self.shelf_table.itemSelectionChanged.connect(self.on_shelf_selection_changed)
        self.shelf_table.itemDoubleClicked.connect(self.edit_shelf)
        
        layout.addWidget(self.shelf_table)
        
        self.tab_widget.addTab(shelf_widget, "货架管理")
    
    def create_layout_tab(self):
        """创建库位布局标签页（3D展示）"""
        try:
            # 导入3D展示器
            from views.warehouse_management.shelf_3d_viewer import Shelf3DViewer
            
            # 创建3D展示器
            self.shelf_3d_viewer = Shelf3DViewer(self)
            
            # 加载仓库数据
            self.shelf_3d_viewer.load_warehouses()
            
            self.tab_widget.addTab(self.shelf_3d_viewer, "3D库位布局")
            
        except ImportError as e:
            # 如果PyVista未安装，使用简单的2D布局
            layout_widget = QWidget()
            layout = QVBoxLayout(layout_widget)
            layout.setContentsMargins(5, 5, 5, 5)
            layout.setSpacing(15)
            
            # 错误提示
            error_label = QLabel("3D功能不可用：PyVista未安装")
            error_label.setObjectName("errorLabel")
            error_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
            error_label.setStyleSheet("color: red; font-size: 16px; font-weight: bold;")
            layout.addWidget(error_label)
            
            # 安装提示
            install_label = QLabel("请运行以下命令安装3D支持：\npip install pyvista vtk")
            install_label.setObjectName("installLabel")
            install_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
            install_label.setStyleSheet("color: #666; font-size: 14px; margin: 20px;")
            layout.addWidget(install_label)
            
            # 简单的2D布局显示
            self.create_simple_layout_display(layout)
            
            self.tab_widget.addTab(layout_widget, "库位布局")
            
        except Exception as e:
            self.logger.error(f"创建3D布局标签页失败: {e}")
            # 创建错误显示页面
            error_widget = QWidget()
            error_layout = QVBoxLayout(error_widget)
            
            # 显示详细错误信息
            error_title = QLabel("3D布局加载失败")
            error_title.setStyleSheet("color: red; font-size: 16px; font-weight: bold;")
            error_layout.addWidget(error_title)
            
            error_detail = QLabel(f"错误详情: {str(e)}")
            error_detail.setWordWrap(True)
            error_detail.setStyleSheet("color: #666; font-size: 12px; margin: 10px;")
            error_layout.addWidget(error_detail)
            
            # 简单的2D布局显示
            self.create_simple_layout_display(error_layout)
            
            self.tab_widget.addTab(error_widget, "库位布局")
    
    def create_simple_layout_display(self, layout):
        """创建简单的2D布局显示"""
        # 选择器区域
        top_layout = QHBoxLayout()

        top_layout.addStretch()

        # 仓库选择
        top_layout.addWidget(QLabel("选择仓库:"))
        self.layout_warehouse_combo = QComboBox()
        self.layout_warehouse_combo.setObjectName("layoutWarehouseCombo")
        self.layout_warehouse_combo.currentTextChanged.connect(self.load_warehouse_layout)
        top_layout.addWidget(self.layout_warehouse_combo)
        
        layout.addLayout(top_layout)
        
        # 布局显示区域
        self.layout_info_label = QLabel("请选择仓库查看布局\n\n建议安装PyVista以获得完整的3D体验")
        self.layout_info_label.setObjectName("layoutInfoLabel")
        self.layout_info_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.layout_info_label.setMinimumHeight(400)
        layout.addWidget(self.layout_info_label)
    
    def connect_signals(self):
        """连接信号"""
        # 控制器信号
        self.controller.factory_created.connect(self.on_factory_created)
        self.controller.factory_updated.connect(self.on_factory_updated)
        self.controller.factory_deleted.connect(self.on_factory_deleted)
        
        self.controller.warehouse_created.connect(self.on_warehouse_created)
        self.controller.warehouse_updated.connect(self.on_warehouse_updated)
        self.controller.warehouse_deleted.connect(self.on_warehouse_deleted)
        
        self.controller.shelf_created.connect(self.on_shelf_created)
        self.controller.shelf_updated.connect(self.on_shelf_updated)
        self.controller.shelf_deleted.connect(self.on_shelf_deleted)
        
        self.controller.error_occurred.connect(self.show_error)
    
    def load_initial_data(self):
        """加载初始数据"""
        self.load_factories()
        self.load_warehouses()
        self.load_shelves()
        self.load_filter_combos()
    
    def load_factories(self):
        """加载厂区列表"""
        factories = self.controller.get_factories()
        
        self.factory_table.setRowCount(len(factories))
        
        for row, factory in enumerate(factories):
            self.factory_table.setItem(row, 0, QTableWidgetItem(str(factory['id'])))
            self.factory_table.setItem(row, 1, QTableWidgetItem(factory['code'] or ''))
            self.factory_table.setItem(row, 2, QTableWidgetItem(factory['name'] or ''))
            self.factory_table.setItem(row, 3, QTableWidgetItem(factory['manager'] or ''))
            self.factory_table.setItem(row, 4, QTableWidgetItem(factory['phone'] or ''))
            self.factory_table.setItem(row, 5, QTableWidgetItem(factory['address'] or ''))
            self.factory_table.setItem(row, 6, QTableWidgetItem("正常" if factory['is_active'] else "停用"))
    
    def load_warehouses(self):
        """加载仓库列表"""
        # 获取当前选择的厂区
        factory_id = None
        if hasattr(self, 'factory_filter_combo'):
            current_factory = self.factory_filter_combo.currentData()
            if current_factory:
                factory_id = current_factory
        
        warehouses = self.controller.get_warehouses(factory_id)
        
        self.warehouse_table.setRowCount(len(warehouses))
        
        type_names = {
            'general': '通用仓库',
            'material': '物资仓库', 
            'parts': '配件仓库',
            'finished': '成品仓库'
        }
        
        for row, warehouse in enumerate(warehouses):
            self.warehouse_table.setItem(row, 0, QTableWidgetItem(str(warehouse['id'])))
            self.warehouse_table.setItem(row, 1, QTableWidgetItem(warehouse['factory_name'] or ''))
            self.warehouse_table.setItem(row, 2, QTableWidgetItem(warehouse['code'] or ''))
            self.warehouse_table.setItem(row, 3, QTableWidgetItem(warehouse['name'] or ''))
            self.warehouse_table.setItem(row, 4, QTableWidgetItem(type_names.get(warehouse['type'], warehouse['type'])))
            self.warehouse_table.setItem(row, 5, QTableWidgetItem(warehouse['manager'] or ''))
            self.warehouse_table.setItem(row, 6, QTableWidgetItem(warehouse['area'] or ''))
            self.warehouse_table.setItem(row, 7, QTableWidgetItem(warehouse['capacity'] or ''))
            self.warehouse_table.setItem(row, 8, QTableWidgetItem("正常" if warehouse['is_active'] else "停用"))
    
    def load_shelves(self):
        """加载货架列表"""
        # 获取当前选择的仓库
        warehouse_id = None
        if hasattr(self, 'warehouse_filter_combo'):
            current_warehouse = self.warehouse_filter_combo.currentData()
            if current_warehouse:
                warehouse_id = current_warehouse
        
        shelves = self.controller.get_shelves(warehouse_id)
        
        self.shelf_table.setRowCount(len(shelves))
        
        for row, shelf in enumerate(shelves):
            self.shelf_table.setItem(row, 0, QTableWidgetItem(str(shelf['id'])))
            self.shelf_table.setItem(row, 1, QTableWidgetItem(shelf['warehouse_name'] or ''))
            self.shelf_table.setItem(row, 2, QTableWidgetItem(shelf['shelf_code'] or ''))
            self.shelf_table.setItem(row, 3, QTableWidgetItem(shelf['name'] or ''))
            self.shelf_table.setItem(row, 4, QTableWidgetItem(str(shelf['max_layers'] or 0)))
            self.shelf_table.setItem(row, 5, QTableWidgetItem(str(shelf['positions_per_layer'] or 0)))
            self.shelf_table.setItem(row, 6, QTableWidgetItem(str(shelf['x_position'] or '')))
            self.shelf_table.setItem(row, 7, QTableWidgetItem(str(shelf['y_position'] or '')))
            self.shelf_table.setItem(row, 8, QTableWidgetItem("正常" if shelf['is_active'] else "停用"))
    
    def load_filter_combos(self):
        """加载筛选下拉框"""
        # 加载厂区筛选
        factories = self.controller.get_factories()
        self.factory_filter_combo.clear()
        self.factory_filter_combo.addItem("全部厂区", None)
        for factory in factories:
            self.factory_filter_combo.addItem(factory['name'], factory['id'])
        
        # 加载仓库筛选
        warehouses = self.controller.get_warehouses()
        self.warehouse_filter_combo.clear()
        self.warehouse_filter_combo.addItem("全部仓库", None)
        for warehouse in warehouses:
            self.warehouse_filter_combo.addItem(f"{warehouse['factory_name']}-{warehouse['name']}", warehouse['id'])
        
        # 加载布局仓库选择（仅在2D模式下存在）
        if hasattr(self, 'layout_warehouse_combo'):
            self.layout_warehouse_combo.clear()
            self.layout_warehouse_combo.addItem("请选择仓库", None)
            for warehouse in warehouses:
                self.layout_warehouse_combo.addItem(f"{warehouse['factory_name']}-{warehouse['name']}", warehouse['id'])
    
    # ==================== 事件处理 ====================
    
    def add_factory(self):
        """添加厂区"""
        from .factory_dialog import FactoryDialog
        dialog = FactoryDialog(self, self.controller)
        dialog.exec()
    
    def edit_factory(self):
        """编辑厂区"""
        current_row = self.factory_table.currentRow()
        if current_row >= 0:
            factory_id = int(self.factory_table.item(current_row, 0).text())
            factory = self.controller.get_factory_by_id(factory_id)
            if factory:
                from .factory_dialog import FactoryDialog
                dialog = FactoryDialog(self, self.controller, factory)
                dialog.exec()
    
    def delete_factory(self):
        """删除厂区"""
        current_row = self.factory_table.currentRow()
        if current_row >= 0:
            factory_name = self.factory_table.item(current_row, 2).text()
            reply = QMessageBox.question(
                self, "确认删除", 
                f"确定要删除厂区 '{factory_name}' 吗？",
                QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
            )
            
            if reply == QMessageBox.StandardButton.Yes:
                factory_id = int(self.factory_table.item(current_row, 0).text())
                self.controller.delete_factory(factory_id)
    
    def add_warehouse(self):
        """添加仓库"""
        from .warehouse_dialog import WarehouseDialog
        dialog = WarehouseDialog(self, self.controller)
        dialog.exec()
    
    def edit_warehouse(self):
        """编辑仓库"""
        current_row = self.warehouse_table.currentRow()
        if current_row >= 0:
            warehouse_id = int(self.warehouse_table.item(current_row, 0).text())
            warehouse = self.controller.get_warehouse_by_id(warehouse_id)
            if warehouse:
                from .warehouse_dialog import WarehouseDialog
                dialog = WarehouseDialog(self, self.controller, warehouse)
                dialog.exec()
    
    def delete_warehouse(self):
        """删除仓库"""
        current_row = self.warehouse_table.currentRow()
        if current_row >= 0:
            warehouse_name = self.warehouse_table.item(current_row, 3).text()
            reply = QMessageBox.question(
                self, "确认删除", 
                f"确定要删除仓库 '{warehouse_name}' 吗？",
                QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
            )
            
            if reply == QMessageBox.StandardButton.Yes:
                warehouse_id = int(self.warehouse_table.item(current_row, 0).text())
                self.controller.delete_warehouse(warehouse_id)
    
    def view_warehouse_layout(self):
        """查看仓库布局"""
        current_row = self.warehouse_table.currentRow()
        if current_row >= 0:
            warehouse_id = int(self.warehouse_table.item(current_row, 0).text())
            # 切换到布局标签页
            self.tab_widget.setCurrentIndex(3)  # 布局标签页索引
            
            # 如果是3D模式，直接在3D展示器中选择仓库
            if hasattr(self, 'shelf_3d_viewer'):
                # 在3D展示器的下拉框中选择对应仓库
                for i in range(self.shelf_3d_viewer.warehouse_combo.count()):
                    if self.shelf_3d_viewer.warehouse_combo.itemData(i) == warehouse_id:
                        self.shelf_3d_viewer.warehouse_combo.setCurrentIndex(i)
                        break
            # 如果是2D模式，在布局下拉框中选择对应仓库
            elif hasattr(self, 'layout_warehouse_combo'):
                for i in range(self.layout_warehouse_combo.count()):
                    if self.layout_warehouse_combo.itemData(i) == warehouse_id:
                        self.layout_warehouse_combo.setCurrentIndex(i)
                        break
    
    def add_shelf(self):
        """添加货架"""
        from .shelf_dialog import ShelfDialog
        dialog = ShelfDialog(self, self.controller)
        dialog.exec()
    
    def edit_shelf(self):
        """编辑货架"""
        current_row = self.shelf_table.currentRow()
        if current_row >= 0:
            shelf_id = int(self.shelf_table.item(current_row, 0).text())
            shelf = self.controller.get_shelf_by_id(shelf_id)
            if shelf:
                from .shelf_dialog import ShelfDialog
                dialog = ShelfDialog(self, self.controller, shelf)
                dialog.exec()
    
    def delete_shelf(self):
        """删除货架"""
        current_row = self.shelf_table.currentRow()
        if current_row >= 0:
            shelf_code = self.shelf_table.item(current_row, 2).text()
            reply = QMessageBox.question(
                self, "确认删除", 
                f"确定要删除货架 '{shelf_code}' 吗？",
                QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
            )
            
            if reply == QMessageBox.StandardButton.Yes:
                shelf_id = int(self.shelf_table.item(current_row, 0).text())
                self.controller.delete_shelf(shelf_id)
    
    def filter_warehouses(self):
        """筛选仓库"""
        self.load_warehouses()
    
    def filter_shelves(self):
        """筛选货架"""
        self.load_shelves()
    
    def load_warehouse_layout(self):
        """加载仓库布局（仅2D模式）"""
        if not hasattr(self, 'layout_warehouse_combo'):
            return
            
        warehouse_id = self.layout_warehouse_combo.currentData()
        if warehouse_id:
            layout_data = self.controller.get_warehouse_layout(warehouse_id)
            if layout_data:
                warehouse = layout_data['warehouse']
                stats = layout_data['statistics']
                
                info_text = f"""
仓库信息：{warehouse['name']} ({warehouse['code']})
所属厂区：{warehouse['factory_name']}
仓库类型：{warehouse['type']}
负责人：{warehouse['manager'] or '未设置'}

布局统计：
货架总数：{stats['total_shelves']} 个
库位总数：{stats['total_locations']} 个
已占用库位：{stats['occupied_locations']} 个
可用库位：{stats['available_locations']} 个
占用率：{stats['occupancy_rate']}%
                """.strip()
                
                self.layout_info_label.setText(info_text)
            else:
                self.layout_info_label.setText("无法加载仓库布局信息")
        else:
            self.layout_info_label.setText("请选择仓库查看布局")
    
    # ==================== 选择事件 ====================
    
    def on_factory_selection_changed(self):
        """厂区选择改变"""
        has_selection = self.factory_table.currentRow() >= 0
        self.edit_factory_btn.setEnabled(has_selection)
        self.delete_factory_btn.setEnabled(has_selection)
    
    def on_warehouse_selection_changed(self):
        """仓库选择改变"""
        has_selection = self.warehouse_table.currentRow() >= 0
        self.edit_warehouse_btn.setEnabled(has_selection)
        self.delete_warehouse_btn.setEnabled(has_selection)
        self.view_layout_btn.setEnabled(has_selection)
    
    def on_shelf_selection_changed(self):
        """货架选择改变"""
        has_selection = self.shelf_table.currentRow() >= 0
        self.edit_shelf_btn.setEnabled(has_selection)
        self.delete_shelf_btn.setEnabled(has_selection)
    
    # ==================== 信号处理 ====================
    
    def on_factory_created(self, factory):
        """厂区创建成功"""
        QMessageBox.information(self, "成功", "厂区创建成功！")
        self.load_factories()
        self.load_filter_combos()
    
    def on_factory_updated(self, factory):
        """厂区更新成功"""
        QMessageBox.information(self, "成功", "厂区更新成功！")
        self.load_factories()
        self.load_filter_combos()
    
    def on_factory_deleted(self, factory_id):
        """厂区删除成功"""
        QMessageBox.information(self, "成功", "厂区删除成功！")
        self.load_factories()
        self.load_filter_combos()
    
    def on_warehouse_created(self, warehouse):
        """仓库创建成功"""
        QMessageBox.information(self, "成功", "仓库创建成功！")
        self.load_warehouses()
        self.load_filter_combos()
    
    def on_warehouse_updated(self, warehouse):
        """仓库更新成功"""
        QMessageBox.information(self, "成功", "仓库更新成功！")
        self.load_warehouses()
        self.load_filter_combos()
    
    def on_warehouse_deleted(self, warehouse_id):
        """仓库删除成功"""
        QMessageBox.information(self, "成功", "仓库删除成功！")
        self.load_warehouses()
        self.load_filter_combos()
    
    def on_shelf_created(self, shelf):
        """货架创建成功"""
        QMessageBox.information(self, "成功", "货架创建成功！")
        self.load_shelves()
    
    def on_shelf_updated(self, shelf):
        """货架更新成功"""
        QMessageBox.information(self, "成功", "货架更新成功！")
        self.load_shelves()
    
    def on_shelf_deleted(self, shelf_id):
        """货架删除成功"""
        QMessageBox.information(self, "成功", "货架删除成功！")
        self.load_shelves()
    
    def show_error(self, message):
        """显示错误信息"""
        QMessageBox.critical(self, "错误", message)
    
    def cleanup_3d(self):
        """清理3D资源"""
        try:
            # 如果存在3D视图器，清理其资源
            if hasattr(self, 'shelf_3d_viewer') and self.shelf_3d_viewer:
                self.shelf_3d_viewer.cleanup_3d()
                self.shelf_3d_viewer = None
        except Exception as e:
            self.logger.error(f"清理仓库管理3D资源时出现错误: {e}")
    
    def set_styles(self):
        """设置样式"""
        self.setStyleSheet("""
            /* 主容器样式 */
            QWidget {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #E3F2FD, stop:1 #BBDEFB);
                font-family: "Microsoft YaHei", "SimHei", Arial, sans-serif;
            }
            
            /* 标签页样式 */
            QTabWidget::pane {
                border: 1px solid #CCCCCC;
                border-radius: 8px;
                background: white;
            }
            
            QTabWidget::tab-bar {
                alignment: left;
            }
            
            QTabBar::tab {
                background: #F5F5F5;
                border: 1px solid #CCCCCC;
                border-bottom: none;
                border-top-left-radius: 8px;
                border-top-right-radius: 8px;
                padding: 8px 16px;
                margin-right: 2px;
                min-width: 100px;
                color: #333333;
            }
            
            QTabBar::tab:selected {
                background: white;
                border-bottom: 1px solid white;
                color: #1976D2;
                font-weight: bold;
            }
            
            QTabBar::tab:hover {
                background: #E8F4FD;
            }
            
            /* 标题样式 */
            #tabTitle {
                font-size: 18px;
                font-weight: bold;
                color: #1976D2;
                margin-bottom: 10px;
                background: transparent;
            }
            
            /* 下拉框样式 */
            QComboBox {
                border: 1px solid #D0D0D0;
                border-radius: 4px;
                padding: 8px 12px;
                font-size: 14px;
                background: white;
                min-width: 120px;
                color: #333333;
            }
            
            QComboBox:focus {
                border-color: #2196F3;
            }
            
            QComboBox::drop-down {
                border: none;
                width: 20px;
                background: transparent;
            }
            
            QComboBox::down-arrow {
                image: none;
                border-left: 5px solid transparent;
                border-right: 5px solid transparent;
                border-top: 5px solid #666666;
                margin-right: 5px;
            }
            
            QComboBox QAbstractItemView {
                background: white;
                border: 1px solid #D0D0D0;
                selection-background-color: #E3F2FD;
                color: #333333;
            }
            
            /* 按钮样式 */
            QPushButton {
                border: none;
                border-radius: 4px;
                padding: 8px 16px;
                font-size: 14px;
                font-weight: normal;
                min-width: 70px;
                color: white;
            }
            
            #addButton {
                background: #4CAF50;
            }
            
            #addButton:hover {
                background: #45A049;
            }
            
            #editButton {
                background: #2196F3;
            }
            
            #editButton:hover {
                background: #1976D2;
            }
            
            #editButton:disabled {
                background: #CCCCCC;
                color: #666666;
            }
            
            #deleteButton {
                background: #F44336;
            }
            
            #deleteButton:hover {
                background: #D32F2F;
            }
            
            #deleteButton:disabled {
                background: #CCCCCC;
                color: #666666;
            }
            
            #viewButton {
                background: #FF9800;
            }
            
            #viewButton:hover {
                background: #F57C00;
            }
            
            #viewButton:disabled {
                background: #CCCCCC;
                color: #666666;
            }
            
            /* 表格样式 */
            QTableWidget {
                border: 1px solid #E0E0E0;
                border-radius: 4px;
                background: white;
                gridline-color: #F0F0F0;
                font-size: 14px;
                color: #333333;
            }
            
            QTableWidget::item {
                padding: 8px;
                border-bottom: 1px solid #F0F0F0;
                background: transparent;
            }
            
            QTableWidget::item:selected {
                background: #E3F2FD;
                color: #1976D2;
            }
            
            QTableWidget::item:alternate {
                background: #FAFAFA;
            }
            
            QHeaderView::section {
                background: #F5F5F5;
                border: 1px solid #CCCCCC;
                border-left: none;
                padding: 8px;
                font-weight: bold;
                color: #333333;
            }
            
            QHeaderView::section:first {
                border-left: 1px solid #CCCCCC;
                border-top-left-radius: 4px;
            }
            
            QHeaderView::section:last {
                border-top-right-radius: 4px;
            }
            
            /* 标签样式 */
            QLabel {
                color: #333333;
                font-size: 14px;
                background: transparent;
            }
            
            #layoutInfoLabel {
                background: white;
                border: 1px solid #E0E0E0;
                border-radius: 4px;
                padding: 20px;
                font-size: 16px;
                line-height: 1.5;
            }
        """)


# 导入对话框类（在文件末尾导入避免循环导入）
from .factory_dialog import FactoryDialog
from .warehouse_dialog import WarehouseDialog
from .shelf_dialog import ShelfDialog 