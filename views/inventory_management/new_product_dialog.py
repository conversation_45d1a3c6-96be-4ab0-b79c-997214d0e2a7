#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import sys
from typing import Dict, Any, Optional
from PyQt6.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QFormLayout, QLabel, 
    QLineEdit, QComboBox, QTextEdit, QSpinBox, QDoubleSpinBox,
    QPushButton, QMessageBox, QCheckBox, QGroupBox
)
from PyQt6.QtCore import Qt, pyqtSignal

sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

class NewProductDialog(QDialog):
    product_created = pyqtSignal(dict)
    
    def __init__(self, parent=None, product_controller=None):
        super().__init__(parent)
        self.product_controller = product_controller
        self.created_product = None
        
        self.init_ui()
        self.load_data()
    
    def init_ui(self):
        self.setWindowTitle("新增商品")
        self.setModal(True)
        self.resize(500, 600)
        
        layout = QVBoxLayout(self)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(20)
        
        self.create_form()
        self.create_buttons()
        self.set_styles()
    
    def create_form(self):
        basic_group = QGroupBox("基本信息")
        basic_layout = QFormLayout(basic_group)
        basic_layout.setSpacing(15)
        
        self.code_edit = QLineEdit()
        self.code_edit.setPlaceholderText("留空自动生成")
        basic_layout.addRow("商品编码:", self.code_edit)
        
        self.name_edit = QLineEdit()
        self.name_edit.setPlaceholderText("请输入商品名称")
        basic_layout.addRow("商品名称*:", self.name_edit)
        
        self.model_edit = QLineEdit()
        self.model_edit.setPlaceholderText("请输入商品型号")
        basic_layout.addRow("商品型号:", self.model_edit)
        
        # 分类种类选择
        self.category_type_combo = QComboBox()
        self.category_type_combo.addItem("请选择分类种类", None)
        self.category_type_combo.addItems(["配件", "物资", "通用", "其他"])
        self.category_type_combo.currentTextChanged.connect(self.on_category_type_changed)
        basic_layout.addRow("分类种类*:", self.category_type_combo)
        
        # 具体分类选择
        self.category_combo = QComboBox()
        self.category_combo.addItem("请先选择分类种类", None)
        self.category_combo.setEnabled(False)
        basic_layout.addRow("商品分类*:", self.category_combo)
        
        self.supplier_combo = QComboBox()
        self.supplier_combo.addItem("无供应商", None)
        basic_layout.addRow("供应商:", self.supplier_combo)
        
        self.unit_edit = QLineEdit()
        self.unit_edit.setPlaceholderText("如：个、台、套、米等")
        basic_layout.addRow("计量单位*:", self.unit_edit)
        
        self.layout().addWidget(basic_group)
        
        price_group = QGroupBox("价格与库存")
        price_layout = QFormLayout(price_group)
        price_layout.setSpacing(15)
        
        self.price_spinbox = QDoubleSpinBox()
        self.price_spinbox.setRange(0, 999999.99)
        self.price_spinbox.setDecimals(2)
        self.price_spinbox.setSuffix(" 元")
        price_layout.addRow("单价:", self.price_spinbox)
        
        self.min_stock_spinbox = QSpinBox()
        self.min_stock_spinbox.setRange(0, 999999)
        self.min_stock_spinbox.setValue(10)
        self.min_stock_spinbox.setSuffix(" 个")
        price_layout.addRow("最小库存:", self.min_stock_spinbox)
        

        
        self.layout().addWidget(price_group)
        
        other_group = QGroupBox("其他信息")
        other_layout = QFormLayout(other_group)
        other_layout.setSpacing(15)
        
        self.description_edit = QTextEdit()
        self.description_edit.setPlaceholderText("请输入商品描述信息")
        self.description_edit.setMaximumHeight(80)
        other_layout.addRow("商品描述:", self.description_edit)
        
        self.active_checkbox = QCheckBox("启用该商品")
        self.active_checkbox.setChecked(True)
        other_layout.addRow("状态:", self.active_checkbox)
        
        self.layout().addWidget(other_group)
        
        self.unit_edit.textChanged.connect(self.update_stock_suffix)
    
    def create_buttons(self):
        button_layout = QHBoxLayout()
        button_layout.addStretch()
        
        cancel_btn = QPushButton("取消")
        cancel_btn.setObjectName("cancelButton")
        cancel_btn.clicked.connect(self.reject)
        button_layout.addWidget(cancel_btn)
        
        save_btn = QPushButton("保存并使用")
        save_btn.setObjectName("saveButton")
        save_btn.clicked.connect(self.save_product)
        button_layout.addWidget(save_btn)
        
        self.layout().addLayout(button_layout)
    
    def load_data(self):
        if not self.product_controller:
            return
            
        try:
            # 加载所有分类数据，用于后续筛选
            self.all_categories = self.product_controller.get_categories()
        except Exception as e:
            print(f"加载分类数据失败: {e}")
            self.all_categories = []
        
        try:
            suppliers = self.product_controller.get_suppliers()
            # 保留"无供应商"选项，只添加其他供应商
            for supplier in suppliers:
                self.supplier_combo.addItem(supplier['name'], supplier['id'])
        except Exception as e:
            print(f"加载供应商数据失败: {e}")
    
    def on_category_type_changed(self, category_type):
        """分类种类改变时更新分类列表"""
        self.category_combo.clear()
        
        if not category_type or category_type == "请选择分类种类":
            self.category_combo.addItem("请先选择分类种类", None)
            self.category_combo.setEnabled(False)
            return
        
        # 根据选择的分类种类筛选分类
        filtered_categories = [cat for cat in self.all_categories if cat.get('type') == category_type]
        
        if filtered_categories:
            self.category_combo.addItem("请选择分类", None)
            for category in filtered_categories:
                self.category_combo.addItem(category['name'], category['id'])
            self.category_combo.setEnabled(True)
        else:
            self.category_combo.addItem("该种类下暂无分类", None)
            self.category_combo.setEnabled(False)
    
    def update_stock_suffix(self):
        unit = self.unit_edit.text() or "个"
        self.min_stock_spinbox.setSuffix(f" {unit}")
    
    def validate_form(self) -> bool:
        if not self.name_edit.text().strip():
            QMessageBox.warning(self, "验证失败", "请输入商品名称")
            self.name_edit.setFocus()
            return False
        
        if not self.unit_edit.text().strip():
            QMessageBox.warning(self, "验证失败", "请输入计量单位")
            self.unit_edit.setFocus()
            return False
        
        if self.category_type_combo.currentData() is None or self.category_type_combo.currentText() == "请选择分类种类":
            QMessageBox.warning(self, "验证失败", "请选择分类种类")
            self.category_type_combo.setFocus()
            return False
        
        if self.category_combo.currentData() is None:
            QMessageBox.warning(self, "验证失败", "请选择商品分类")
            self.category_combo.setFocus()
            return False
        
        # 移除最大库存验证
        
        return True
    
    def get_form_data(self) -> Dict[str, Any]:
        data = {
            'name': self.name_edit.text().strip(),
            'model': self.model_edit.text().strip() or None,
            'category_id': self.category_combo.currentData(),
            'supplier_id': self.supplier_combo.currentData(),
            'unit': self.unit_edit.text().strip(),
            'standard_price': self.price_spinbox.value() if self.price_spinbox.value() > 0 else None,
            'min_stock': self.min_stock_spinbox.value(),
            'description': self.description_edit.toPlainText().strip() or None,
            'is_active': self.active_checkbox.isChecked()
        }
        
        code = self.code_edit.text().strip()
        if code:
            data['code'] = code
        
        return data
    
    def save_product(self):
        if not self.validate_form():
            return
        
        data = self.get_form_data()
        
        try:
            success = self.product_controller.create_product(data)
            
            if success:
                products_result = self.product_controller.get_products(page=1, page_size=1)
                if products_result and 'products' in products_result and products_result['products']:
                    self.created_product = products_result['products'][0]
                    self.product_created.emit(self.created_product)
                    QMessageBox.information(self, "成功", "商品创建成功！")
                    self.accept()
                else:
                    QMessageBox.warning(self, "警告", "商品创建成功，但无法获取商品信息")
                    self.accept()
            else:
                QMessageBox.warning(self, "失败", "商品创建失败，请检查输入信息")
        
        except Exception as e:
            QMessageBox.critical(self, "错误", f"保存失败: {str(e)}")
    
    def get_created_product(self) -> Optional[Dict[str, Any]]:
        return self.created_product
    
    def set_styles(self):
        self.setStyleSheet("""
            QDialog {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #E3F2FD, stop:1 #BBDEFB);
                font-family: "Microsoft YaHei", Arial, sans-serif;
            }
            
            QGroupBox {
                font-weight: bold;
                font-size: 14px;
                color: #1976D2;
                border: 2px solid #E0E0E0;
                border-radius: 8px;
                margin-top: 10px;
                padding-top: 10px;
                background: white;
            }
            
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 8px 0 8px;
                background: white;
            }
            
            QLabel {
                color: #333333;
                font-size: 14px;
                font-weight: normal;
                background: transparent;
            }
            
            QLineEdit, QComboBox, QSpinBox, QDoubleSpinBox {
                border: 1px solid #D0D0D0;
                border-radius: 4px;
                padding: 8px 12px;
                font-size: 14px;
                background: white;
                min-height: 20px;
                color: #333333;
            }
            
            QLineEdit:focus, QComboBox:focus, QSpinBox:focus, QDoubleSpinBox:focus {
                border-color: #2196F3;
                background: white;
            }
            
            QTextEdit {
                border: 1px solid #D0D0D0;
                border-radius: 4px;
                padding: 8px;
                font-size: 14px;
                background: white;
                color: #333333;
            }
            
            QTextEdit:focus {
                border-color: #2196F3;
                background: white;
            }
            
            QCheckBox {
                font-size: 14px;
                color: #333333;
                spacing: 8px;
            }
            
            QCheckBox::indicator {
                width: 18px;
                height: 18px;
                border: 1px solid #D0D0D0;
                border-radius: 3px;
                background: white;
            }
            
            QCheckBox::indicator:checked {
                background: #2196F3;
                border-color: #2196F3;
            }
            
            QPushButton {
                border: none;
                border-radius: 4px;
                padding: 8px 16px;
                font-size: 14px;
                font-weight: normal;
                min-width: 70px;
                color: white;
            }
            
            #saveButton {
                background: #4CAF50;
            }
            
            #saveButton:hover {
                background: #45A049;
            }
            
            #cancelButton {
                background: #9E9E9E;
            }
            
            #cancelButton:hover {
                background: #757575;
            }
        """)