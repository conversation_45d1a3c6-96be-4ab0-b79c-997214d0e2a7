#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
入库单对话框
支持创建和编辑入库单，包含入库明细管理
"""

import sys
import os
from datetime import datetime, date
from decimal import Decimal

from PyQt6.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QLabel, QPushButton, 
    QLineEdit, QComboBox, QTextEdit, QTableWidget, QTableWidgetItem,
    QHeaderView, QMessageBox, QDateEdit, QDoubleSpinBox, QSpinBox,
    QFrame, QGridLayout, QGroupBox, QFormLayout, QSplitter,
    QAbstractItemView, QCheckBox, QApplication
)
from PyQt6.QtCore import Qt, QDate, pyqtSignal
from PyQt6.QtGui import QFont, QDoubleValidator, QIntValidator

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

try:
    from controllers.inventory_controller import InventoryController
    from controllers.product_controller import ProductController
    from controllers.warehouse_controller import WarehouseController
    from styles.unified_styles import get_unified_stylesheet
except ImportError as e:
    print(f"导入失败: {e}")
    InventoryController = None
    ProductController = None
    WarehouseController = None
    def get_unified_stylesheet():
        return ""


class InboundOrderDialog(QDialog):
    """入库单对话框"""
    
    # 定义信号
    order_created = pyqtSignal(dict)
    order_updated = pyqtSignal(dict)
    
    def __init__(self, parent=None, order_id=None, mode='create'):
        super().__init__(parent)
        self.order_id = order_id
        self.mode = mode  # 'create' 或 'edit'
        
        # 初始化控制器
        self.inventory_controller = InventoryController() if InventoryController else None
        self.product_controller = ProductController() if ProductController else None
        self.warehouse_controller = WarehouseController() if WarehouseController else None
        
        # 数据缓存
        self.products_data = []
        self.warehouses_data = []
        self.suppliers_data = []
        self.locations_data = []
        self.order_details = []
        
        self.setWindowTitle("新建入库单" if mode == 'create' else "编辑入库单")
        self.setModal(True)
        
        # 设置合适的窗口大小，适应不同屏幕
        screen = QApplication.primaryScreen()
        screen_size = screen.availableSize()
        
        # 设置窗口大小为屏幕的80%，但不超过最大尺寸
        max_width = min(1200, int(screen_size.width() * 0.8))
        max_height = min(800, int(screen_size.height() * 0.8))
        
        self.resize(max_width, max_height)
        
        # 居中显示
        self.move(
            (screen_size.width() - max_width) // 2,
            (screen_size.height() - max_height) // 2
        )
        
        self.init_ui()
        self.load_basic_data()
        self.setup_connections()
        self.set_styles()
        
        if mode == 'edit' and order_id:
            self.load_order_data()
    
    def init_ui(self):
        """初始化界面"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(15, 15, 15, 15)
        layout.setSpacing(10)
        
        # 创建分割器
        splitter = QSplitter(Qt.Orientation.Vertical)
        
        # 上半部分：入库单基本信息
        self.create_order_info_section(splitter)
        
        # 下半部分：入库明细
        self.create_order_details_section(splitter)
        
        # 设置分割器比例 - 基本信息占较少空间，明细占更多空间
        splitter.setSizes([250, 450])
        layout.addWidget(splitter)
        
        # 底部按钮
        self.create_button_section(layout)
    
    def create_order_info_section(self, parent):
        """创建入库单基本信息区域"""
        info_widget = QGroupBox("入库单基本信息")
        info_widget.setMaximumHeight(220)  # 限制基本信息区域高度
        
        # 使用网格布局来更好地利用空间
        info_layout = QGridLayout(info_widget)
        info_layout.setSpacing(8)
        
        # 设置列宽比例为1:1:1:1
        info_layout.setColumnStretch(0, 0)  # 标签列不拉伸
        info_layout.setColumnStretch(1, 1)  # 第一列控件，比例1
        info_layout.setColumnStretch(2, 0)  # 标签列不拉伸
        info_layout.setColumnStretch(3, 1)  # 第二列控件，比例1
        
        # 第一行
        info_layout.addWidget(QLabel("入库单号:"), 0, 0)
        self.order_no_edit = QLineEdit()
        self.order_no_edit.setReadOnly(True)
        self.order_no_edit.setPlaceholderText("系统自动生成")
        info_layout.addWidget(self.order_no_edit, 0, 1)
        
        info_layout.addWidget(QLabel("入库类型:"), 0, 2)
        self.type_combo = QComboBox()
        type_items = [
            ("purchase", "采购入库"),
            ("return", "归还入库"),
            ("transfer", "调拨入库"),
            ("other", "其他入库")
        ]
        for value, text in type_items:
            self.type_combo.addItem(text, value)
        self.type_combo.setCurrentIndex(0)
        info_layout.addWidget(self.type_combo, 0, 3)
        
        # 第二行
        info_layout.addWidget(QLabel("供应商:"), 1, 0)
        self.supplier_combo = QComboBox()
        self.supplier_combo.addItem("请选择供应商", None)
        info_layout.addWidget(self.supplier_combo, 1, 1)
        
        info_layout.addWidget(QLabel("目标仓库:"), 1, 2)
        self.warehouse_combo = QComboBox()
        self.warehouse_combo.addItem("请选择仓库", None)
        self.warehouse_combo.currentTextChanged.connect(self.on_warehouse_changed)
        info_layout.addWidget(self.warehouse_combo, 1, 3)
        
        # 第三行
        info_layout.addWidget(QLabel("操作员:"), 2, 0)
        self.operator_edit = QLineEdit()
        self.operator_edit.setReadOnly(True)
        self.operator_edit.setText("当前用户")  # 实际应用中从登录信息获取
        info_layout.addWidget(self.operator_edit, 2, 1)
        
        info_layout.addWidget(QLabel("入库日期:"), 2, 2)
        self.date_edit = QDateEdit()
        self.date_edit.setDate(QDate.currentDate())
        self.date_edit.setCalendarPopup(True)
        info_layout.addWidget(self.date_edit, 2, 3)
        
        # 第四行 - 备注占整行
        info_layout.addWidget(QLabel("备注:"), 3, 0)
        self.remark_edit = QTextEdit()
        self.remark_edit.setMaximumHeight(60)  # 减少备注区域高度
        self.remark_edit.setPlaceholderText("请输入备注信息...")
        info_layout.addWidget(self.remark_edit, 3, 1, 1, 3)  # 跨3列
        
        parent.addWidget(info_widget)
    
    def create_order_details_section(self, parent):
        """创建入库明细区域"""
        details_widget = QGroupBox("入库明细")
        details_layout = QVBoxLayout(details_widget)
        
        # 明细操作按钮
        button_layout = QHBoxLayout()
        
        add_detail_btn = QPushButton("➕ 添加明细")
        add_detail_btn.setObjectName("addButton")
        add_detail_btn.clicked.connect(self.add_detail_row)
        button_layout.addWidget(add_detail_btn)
        
        # 新增商品按钮
        add_product_btn = QPushButton("🆕 新增商品")
        add_product_btn.setObjectName("newProductButton")
        add_product_btn.clicked.connect(self.add_new_product)
        button_layout.addWidget(add_product_btn)
        
        self.remove_detail_btn = QPushButton("➖ 删除明细")
        self.remove_detail_btn.setObjectName("deleteButton")
        self.remove_detail_btn.clicked.connect(self.remove_detail_row)
        self.remove_detail_btn.setEnabled(False)
        button_layout.addWidget(self.remove_detail_btn)
        
        button_layout.addStretch()
        
        # 总计信息
        self.total_quantity_label = QLabel("总数量: 0")
        self.total_quantity_label.setObjectName("totalLabel")
        button_layout.addWidget(self.total_quantity_label)
        
        self.total_amount_label = QLabel("总金额: ¥0.00")
        self.total_amount_label.setObjectName("totalLabel")
        button_layout.addWidget(self.total_amount_label)
        
        details_layout.addLayout(button_layout)
        
        # 明细表格
        self.details_table = QTableWidget()
        self.details_table.setObjectName("detailsTable")
        self.details_table.setColumnCount(9)
        self.details_table.setHorizontalHeaderLabels([
            "商品编码", "商品名称", "规格型号", "库位", "数量", "单价", "金额", "批次号", "备注"
        ])
        
        # 设置表格属性
        self.details_table.setSelectionBehavior(QAbstractItemView.SelectionBehavior.SelectRows)
        self.details_table.setAlternatingRowColors(True)
        self.details_table.itemSelectionChanged.connect(self.on_detail_selection_changed)
        
        # 设置列宽
        header = self.details_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.ResizeMode.Fixed)  # 商品编码
        header.setSectionResizeMode(1, QHeaderView.ResizeMode.Stretch)  # 商品名称
        header.setSectionResizeMode(2, QHeaderView.ResizeMode.Fixed)  # 规格型号
        header.setSectionResizeMode(3, QHeaderView.ResizeMode.Fixed)  # 库位
        header.setSectionResizeMode(4, QHeaderView.ResizeMode.Fixed)  # 数量
        header.setSectionResizeMode(5, QHeaderView.ResizeMode.Fixed)  # 单价
        header.setSectionResizeMode(6, QHeaderView.ResizeMode.Fixed)  # 金额
        header.setSectionResizeMode(7, QHeaderView.ResizeMode.Fixed)  # 批次号
        header.setSectionResizeMode(8, QHeaderView.ResizeMode.Stretch)  # 备注
        
        self.details_table.setColumnWidth(0, 120)  # 商品编码
        self.details_table.setColumnWidth(2, 100)  # 规格型号
        self.details_table.setColumnWidth(3, 100)  # 库位
        self.details_table.setColumnWidth(4, 80)   # 数量
        self.details_table.setColumnWidth(5, 100)  # 单价
        self.details_table.setColumnWidth(6, 100)  # 金额
        self.details_table.setColumnWidth(7, 100)  # 批次号
        
        details_layout.addWidget(self.details_table)
        parent.addWidget(details_widget)
    
    def create_button_section(self, layout):
        """创建底部按钮区域"""
        button_layout = QHBoxLayout()
        button_layout.addStretch()
        
        # 保存按钮
        save_btn = QPushButton("💾 保存入库单")
        save_btn.setObjectName("primaryButton")
        save_btn.clicked.connect(self.save_order)
        button_layout.addWidget(save_btn)
        
        # 保存并审批按钮（仅创建模式）
        if self.mode == 'create':
            save_approve_btn = QPushButton("✅ 保存并审批")
            save_approve_btn.setObjectName("successButton")
            save_approve_btn.clicked.connect(self.save_and_approve_order)
            button_layout.addWidget(save_approve_btn)
        
        # 取消按钮
        cancel_btn = QPushButton("❌ 取消")
        cancel_btn.setObjectName("cancelButton")
        cancel_btn.clicked.connect(self.reject)
        button_layout.addWidget(cancel_btn)
        
        layout.addLayout(button_layout)

    def load_basic_data(self):
        """加载基础数据"""
        try:
            # 初始化数据列表
            self.products_data = []
            self.warehouses_data = []
            self.locations_data = []
            
            print("开始加载基础数据...")
            
            # 加载供应商数据
            if self.product_controller:
                try:
                    suppliers = self.product_controller.get_suppliers()
                    if suppliers:
                        for supplier in suppliers:
                            self.supplier_combo.addItem(supplier.get('name', ''), supplier.get('id'))
                        print(f"成功加载 {len(suppliers)} 个供应商")
                    else:
                        print("未找到供应商数据")
                except Exception as e:
                    print(f"加载供应商数据失败: {e}")

            # 加载仓库数据
            if self.warehouse_controller:
                try:
                    warehouses = self.warehouse_controller.get_warehouses()
                    if warehouses:
                        self.warehouses_data = warehouses
                        for warehouse in warehouses:
                            self.warehouse_combo.addItem(warehouse.get('name', ''), warehouse.get('id'))
                        print(f"成功加载 {len(warehouses)} 个仓库")
                    else:
                        print("未找到仓库数据")
                        self.warehouses_data = []
                except Exception as e:
                    print(f"加载仓库数据失败: {e}")
                    self.warehouses_data = []

            # 加载商品数据
            if self.product_controller:
                try:
                    products_result = self.product_controller.get_products(page=1, page_size=1000)
                    if products_result and isinstance(products_result, dict) and 'products' in products_result:
                        products = products_result['products']
                        if isinstance(products, list):
                            self.products_data = products
                            print(f"成功加载 {len(products)} 个商品")
                            # 调试：打印前几个商品信息
                            for i, product in enumerate(products[:3]):
                                print(f"商品 {i+1}: {product}")
                        else:
                            print(f"商品数据格式错误，期望列表但得到: {type(products)}")
                            self.products_data = []
                    else:
                        print(f"未找到商品数据，返回结果: {products_result}")
                        self.products_data = []
                except Exception as e:
                    print(f"加载商品数据失败: {e}")
                    import traceback
                    traceback.print_exc()
                    self.products_data = []
            else:
                print("商品控制器未初始化")

        except Exception as e:
            print(f"加载基础数据时发生未知错误: {e}")
            import traceback
            traceback.print_exc()
            QMessageBox.warning(self, "警告", f"加载基础数据失败: {str(e)}")
            # 确保数据列表已初始化
            if not hasattr(self, 'products_data'):
                self.products_data = []
            if not hasattr(self, 'warehouses_data'):
                self.warehouses_data = []
            if not hasattr(self, 'locations_data'):
                self.locations_data = []

    def on_warehouse_changed(self):
        """仓库选择改变时更新库位列表"""
        warehouse_id = self.warehouse_combo.currentData()
        if warehouse_id and self.warehouse_controller:
            try:
                locations = self.warehouse_controller.get_locations_by_warehouse(warehouse_id)
                self.locations_data = locations
            except Exception as e:
                print(f"加载库位数据失败: {e}")
                self.locations_data = []

    def setup_connections(self):
        """设置信号连接"""
        # 表格数据变化时更新总计
        self.details_table.itemChanged.connect(self.update_totals)

    def add_new_product(self):
        """新增商品"""
        try:
            from views.inventory_management.new_product_dialog import NewProductDialog
            
            dialog = NewProductDialog(self, self.product_controller)
            
            # 连接信号
            dialog.product_created.connect(self.on_new_product_created)
            
            if dialog.exec() == QDialog.DialogCode.Accepted:
                # 刷新商品数据
                self.load_basic_data()
                
        except ImportError as e:
            QMessageBox.warning(self, "错误", f"无法加载新增商品对话框: {e}")
        except Exception as e:
            QMessageBox.critical(self, "错误", f"新增商品失败: {e}")
    
    def on_new_product_created(self, product):
        """新商品创建成功后的处理"""
        try:
            # 刷新商品数据
            self.load_basic_data()
            
            # 自动添加明细行并选择新商品
            self.add_detail_row()
            
            # 获取最后一行
            last_row = self.details_table.rowCount() - 1
            if last_row >= 0:
                product_combo = self.details_table.cellWidget(last_row, 0)
                if product_combo:
                    # 查找新商品在下拉框中的位置
                    for i in range(product_combo.count()):
                        item_data = product_combo.itemData(i)
                        if item_data and item_data.get('id') == product.get('id'):
                            product_combo.setCurrentIndex(i)
                            break
                            
        except Exception as e:
            print(f"处理新商品创建失败: {e}")

    def add_detail_row(self):
        """添加明细行"""
        print(f"添加明细行，当前商品数据数量: {len(self.products_data) if hasattr(self, 'products_data') else 0}")
        
        # 检查是否有基础数据
        if not hasattr(self, 'products_data') or not self.products_data:
            QMessageBox.warning(self, "警告", "商品数据未加载，请先选择仓库或刷新页面")
            return
            
        row = self.details_table.rowCount()
        self.details_table.insertRow(row)

        # 商品编码下拉框
        product_combo = QComboBox()
        product_combo.addItem("请选择商品", None)
        try:
            for product in self.products_data:
                if product and isinstance(product, dict):
                    code = product.get('code', '')
                    name = product.get('name', '')
                    display_text = f"{code} - {name}" if code and name else (name or code or "未知商品")
                    product_combo.addItem(display_text, product)
                    print(f"添加商品选项: {display_text}")  # 调试信息
        except Exception as e:
            print(f"添加商品选项失败: {e}")
        
        print(f"商品下拉框总项目数: {product_combo.count()}")
        
        # 修复lambda闭包问题，使用partial函数或者直接在这里定义函数
        def make_product_changed_handler(row_index):
            return lambda: self.on_product_changed(row_index)
        product_combo.currentTextChanged.connect(make_product_changed_handler(row))
        self.details_table.setCellWidget(row, 0, product_combo)

        # 商品名称（只读）
        name_item = QTableWidgetItem("")
        name_item.setFlags(name_item.flags() & ~Qt.ItemFlag.ItemIsEditable)
        self.details_table.setItem(row, 1, name_item)

        # 规格型号（只读）
        model_item = QTableWidgetItem("")
        model_item.setFlags(model_item.flags() & ~Qt.ItemFlag.ItemIsEditable)
        self.details_table.setItem(row, 2, model_item)

        # 库位下拉框
        location_combo = QComboBox()
        location_combo.addItem("请选择库位", None)
        location_combo.addItem("🆕 新增库位", "new_location")  # 添加新增库位选项
        try:
            if hasattr(self, 'locations_data') and self.locations_data:
                for location in self.locations_data:
                    if location and isinstance(location, dict):
                        location_code = location.get('location_code', '')
                        shelf_code = location.get('shelf_code', '')
                        warehouse_name = location.get('warehouse_name', '')
                        is_occupied = location.get('is_occupied', False)
                        current_quantity = location.get('current_quantity', 0)
                        max_capacity = location.get('max_capacity', 0)
                        location_id = location.get('id')
                        
                        if location_code and location_id:
                            # 构建显示文本
                            display_text = f"{location_code}"
                            if shelf_code:
                                display_text += f" ({shelf_code})"
                            
                            # 添加容量信息
                            if max_capacity > 0:
                                display_text += f" [{current_quantity}/{max_capacity}]"
                            elif current_quantity > 0:
                                display_text += f" [已用:{current_quantity}]"
                                
                            # 如果库位已满，添加标识
                            if is_occupied and max_capacity > 0 and current_quantity >= max_capacity:
                                display_text += " (已满)"
                                
                            location_combo.addItem(display_text, location_id)
        except Exception as e:
            print(f"添加库位选项失败: {e}")
        
        # 连接库位选择变化信号
        def make_location_changed_handler(row_index):
            return lambda: self.on_location_changed(row_index)
        location_combo.currentTextChanged.connect(make_location_changed_handler(row))
        
        self.details_table.setCellWidget(row, 3, location_combo)

        # 数量输入框
        quantity_spin = QSpinBox()
        quantity_spin.setMinimum(1)
        quantity_spin.setMaximum(999999)
        quantity_spin.setValue(1)
        # 修复lambda闭包问题
        def make_quantity_changed_handler(row_index):
            return lambda: self.calculate_row_amount(row_index)
        quantity_spin.valueChanged.connect(make_quantity_changed_handler(row))
        self.details_table.setCellWidget(row, 4, quantity_spin)

        # 单价输入框
        price_spin = QDoubleSpinBox()
        price_spin.setMinimum(0.01)
        price_spin.setMaximum(999999.99)
        price_spin.setDecimals(2)
        price_spin.setValue(0.01)
        # 修复lambda闭包问题
        def make_price_changed_handler(row_index):
            return lambda: self.calculate_row_amount(row_index)
        price_spin.valueChanged.connect(make_price_changed_handler(row))
        self.details_table.setCellWidget(row, 5, price_spin)

        # 金额（只读）
        amount_item = QTableWidgetItem("0.01")
        amount_item.setFlags(amount_item.flags() & ~Qt.ItemFlag.ItemIsEditable)
        self.details_table.setItem(row, 6, amount_item)

        # 批次号
        batch_item = QTableWidgetItem("")
        self.details_table.setItem(row, 7, batch_item)

        # 备注
        remark_item = QTableWidgetItem("")
        self.details_table.setItem(row, 8, remark_item)

        self.update_totals()

    def remove_detail_row(self):
        """删除选中的明细行"""
        current_row = self.details_table.currentRow()
        if current_row >= 0:
            self.details_table.removeRow(current_row)
            self.update_totals()

    def on_detail_selection_changed(self):
        """明细选择改变"""
        has_selection = len(self.details_table.selectedItems()) > 0
        self.remove_detail_btn.setEnabled(has_selection)

    def on_location_changed(self, row):
        """库位选择改变"""
        try:
            location_combo = self.details_table.cellWidget(row, 3)
            if location_combo:
                selected_data = location_combo.currentData()
                if selected_data == "new_location":
                    # 处理新增库位
                    self.add_new_location(row)
                elif selected_data:
                    # 选择了现有库位，可以在这里添加额外的处理逻辑
                    print(f"选择了库位ID: {selected_data}")
        except Exception as e:
            print(f"库位选择变化处理失败: {e}")
    
    def add_new_location(self, row):
        """新增库位"""
        try:
            # 检查是否选择了仓库
            warehouse_id = self.warehouse_combo.currentData()
            if not warehouse_id:
                QMessageBox.warning(self, "警告", "请先选择仓库")
                # 重置库位选择
                location_combo = self.details_table.cellWidget(row, 3)
                if location_combo:
                    location_combo.setCurrentIndex(0)
                return
            
            # 这里可以添加新增库位的对话框
            # 暂时显示提示信息
            QMessageBox.information(self, "提示", "新增库位功能正在开发中，请联系管理员添加新库位")
            
            # 重置库位选择
            location_combo = self.details_table.cellWidget(row, 3)
            if location_combo:
                location_combo.setCurrentIndex(0)
                
        except Exception as e:
            QMessageBox.critical(self, "错误", f"新增库位失败: {e}")

    def on_product_changed(self, row):
        """商品选择改变"""
        try:
            product_combo = self.details_table.cellWidget(row, 0)
            if product_combo:
                product_data = product_combo.currentData()
                if product_data:
                    # 安全地更新商品名称和规格型号
                    name_item = self.details_table.item(row, 1)
                    model_item = self.details_table.item(row, 2)
                    
                    if name_item:
                        name_item.setText(product_data.get('name', ''))
                    if model_item:
                        model_item.setText(product_data.get('model', ''))

                    # 设置默认单价
                    price_spin = self.details_table.cellWidget(row, 5)
                    if price_spin and product_data.get('price'):
                        try:
                            price_value = float(product_data.get('price', 0))
                            if price_value > 0:
                                price_spin.setValue(price_value)
                                self.calculate_row_amount(row)
                        except (ValueError, TypeError) as e:
                            print(f"设置商品价格失败: {e}")
        except Exception as e:
            print(f"商品选择变化处理失败: {e}")
            # 可选：显示用户友好的错误消息
            # QMessageBox.warning(self, "警告", f"商品信息更新失败: {str(e)}")

    def calculate_row_amount(self, row):
        """计算行金额"""
        try:
            quantity_spin = self.details_table.cellWidget(row, 4)
            price_spin = self.details_table.cellWidget(row, 5)
            amount_item = self.details_table.item(row, 6)

            if quantity_spin and price_spin and amount_item:
                quantity = quantity_spin.value()
                price = price_spin.value()
                amount = quantity * price
                amount_item.setText(f"{amount:.2f}")

                self.update_totals()
        except Exception as e:
            print(f"计算行金额失败: {e}")
            # 设置默认值避免显示错误
            if self.details_table.item(row, 6):
                self.details_table.item(row, 6).setText("0.00")

    def update_totals(self):
        """更新总计"""
        try:
            total_quantity = 0
            total_amount = 0.0

            for row in range(self.details_table.rowCount()):
                # 统计数量
                quantity_spin = self.details_table.cellWidget(row, 4)
                if quantity_spin:
                    total_quantity += quantity_spin.value()

                # 统计金额
                amount_item = self.details_table.item(row, 6)
                if amount_item and amount_item.text():
                    try:
                        total_amount += float(amount_item.text())
                    except ValueError:
                        pass

            self.total_quantity_label.setText(f"总数量: {total_quantity}")
            self.total_amount_label.setText(f"总金额: ¥{total_amount:.2f}")

        except Exception as e:
            print(f"更新总计失败: {e}")

    def validate_data(self):
        """验证数据"""
        # 检查基本信息
        if not self.type_combo.currentData():
            QMessageBox.warning(self, "验证失败", "请选择入库类型")
            return False

        if not self.warehouse_combo.currentData():
            QMessageBox.warning(self, "验证失败", "请选择目标仓库")
            return False

        # 检查明细
        if self.details_table.rowCount() == 0:
            QMessageBox.warning(self, "验证失败", "请至少添加一条入库明细")
            return False

        # 检查每行明细的完整性
        for row in range(self.details_table.rowCount()):
            product_combo = self.details_table.cellWidget(row, 0)
            location_combo = self.details_table.cellWidget(row, 3)
            quantity_spin = self.details_table.cellWidget(row, 4)
            price_spin = self.details_table.cellWidget(row, 5)

            if not product_combo or not product_combo.currentData():
                QMessageBox.warning(self, "验证失败", f"第{row+1}行请选择商品")
                return False

            if not location_combo or not location_combo.currentData():
                QMessageBox.warning(self, "验证失败", f"第{row+1}行请选择库位")
                return False

            if not quantity_spin or quantity_spin.value() <= 0:
                QMessageBox.warning(self, "验证失败", f"第{row+1}行数量必须大于0")
                return False

            if not price_spin or price_spin.value() <= 0:
                QMessageBox.warning(self, "验证失败", f"第{row+1}行单价必须大于0")
                return False

        return True

    def collect_order_data(self):
        """收集入库单数据"""
        try:
            # 基本信息
            order_data = {
                'type': self.type_combo.currentData(),
                'supplier_id': self.supplier_combo.currentData(),
                'warehouse_id': self.warehouse_combo.currentData(),
                'operator_id': 1,  # 实际应用中从登录信息获取
                'remark': self.remark_edit.toPlainText().strip(),
                'details': []
            }

            # 收集明细数据
            for row in range(self.details_table.rowCount()):
                product_combo = self.details_table.cellWidget(row, 0)
                location_combo = self.details_table.cellWidget(row, 3)
                quantity_spin = self.details_table.cellWidget(row, 4)
                price_spin = self.details_table.cellWidget(row, 5)
                batch_item = self.details_table.item(row, 7)
                remark_item = self.details_table.item(row, 8)

                product_data = product_combo.currentData()
                detail = {
                    'product_id': product_data.get('id'),
                    'location_id': location_combo.currentData(),
                    'quantity': quantity_spin.value(),
                    'unit_price': price_spin.value(),
                    'total_price': quantity_spin.value() * price_spin.value(),
                    'batch_no': batch_item.text() if batch_item else '',
                    'remark': remark_item.text() if remark_item else ''
                }
                order_data['details'].append(detail)

            return order_data

        except Exception as e:
            QMessageBox.critical(self, "错误", f"收集数据失败: {str(e)}")
            return None

    def save_order(self):
        """保存入库单"""
        if not self.validate_data():
            return

        order_data = self.collect_order_data()
        if not order_data:
            return

        try:
            if self.inventory_controller:
                if self.mode == 'create':
                    success = self.inventory_controller.create_inbound_order(order_data)
                    if success:
                        QMessageBox.information(self, "成功", "入库单创建成功")
                        self.order_created.emit(order_data)
                        self.accept()
                    else:
                        QMessageBox.warning(self, "失败", "入库单创建失败")
                else:
                    # 编辑模式的保存逻辑
                    success = self.inventory_controller.update_inbound_order(self.order_id, order_data)
                    if success:
                        QMessageBox.information(self, "成功", "入库单更新成功")
                        self.order_updated.emit(order_data)
                        self.accept()
                    else:
                        QMessageBox.warning(self, "失败", "入库单更新失败")
            else:
                QMessageBox.information(self, "模拟", "入库单保存成功（模拟模式）")
                self.accept()

        except Exception as e:
            QMessageBox.critical(self, "错误", f"保存入库单失败: {str(e)}")

    def save_and_approve_order(self):
        """保存并审批入库单"""
        if not self.validate_data():
            return

        order_data = self.collect_order_data()
        if not order_data:
            return

        # 添加自动审批标记
        order_data['auto_approve'] = True

        try:
            if self.inventory_controller:
                success = self.inventory_controller.create_inbound_order(order_data)
                if success:
                    QMessageBox.information(self, "成功", "入库单创建并审批成功")
                    self.order_created.emit(order_data)
                    self.accept()
                else:
                    QMessageBox.warning(self, "失败", "入库单创建失败")
            else:
                QMessageBox.information(self, "模拟", "入库单创建并审批成功（模拟模式）")
                self.accept()

        except Exception as e:
            QMessageBox.critical(self, "错误", f"保存并审批入库单失败: {str(e)}")

    def load_order_data(self):
        """加载入库单数据（编辑模式）"""
        if not self.order_id or not self.inventory_controller:
            return

        try:
            order_data = self.inventory_controller.get_inbound_order_by_id(self.order_id)
            if order_data:
                # 填充基本信息
                self.order_no_edit.setText(order_data.get('order_no', ''))

                # 设置入库类型
                type_value = order_data.get('type', '')
                for i in range(self.type_combo.count()):
                    if self.type_combo.itemData(i) == type_value:
                        self.type_combo.setCurrentIndex(i)
                        break

                # 设置供应商
                supplier_id = order_data.get('supplier_id')
                if supplier_id:
                    for i in range(self.supplier_combo.count()):
                        if self.supplier_combo.itemData(i) == supplier_id:
                            self.supplier_combo.setCurrentIndex(i)
                            break

                # 设置仓库
                warehouse_id = order_data.get('warehouse_id')
                if warehouse_id:
                    for i in range(self.warehouse_combo.count()):
                        if self.warehouse_combo.itemData(i) == warehouse_id:
                            self.warehouse_combo.setCurrentIndex(i)
                            break

                # 设置备注
                self.remark_edit.setPlainText(order_data.get('remark', ''))

                # 加载明细数据
                details = order_data.get('details', [])
                for detail in details:
                    self.add_detail_row()
                    row = self.details_table.rowCount() - 1

                    # 设置商品
                    product_combo = self.details_table.cellWidget(row, 0)
                    product_id = detail.get('product_id')
                    for i in range(product_combo.count()):
                        product_data = product_combo.itemData(i)
                        if product_data and product_data.get('id') == product_id:
                            product_combo.setCurrentIndex(i)
                            break

                    # 设置库位
                    location_combo = self.details_table.cellWidget(row, 3)
                    location_id = detail.get('location_id')
                    for i in range(location_combo.count()):
                        if location_combo.itemData(i) == location_id:
                            location_combo.setCurrentIndex(i)
                            break

                    # 设置数量和单价
                    quantity_spin = self.details_table.cellWidget(row, 4)
                    quantity_spin.setValue(detail.get('quantity', 1))

                    price_spin = self.details_table.cellWidget(row, 5)
                    price_spin.setValue(detail.get('unit_price', 0.01))

                    # 设置批次号和备注
                    batch_item = self.details_table.item(row, 7)
                    batch_item.setText(detail.get('batch_no', ''))

                    remark_item = self.details_table.item(row, 8)
                    remark_item.setText(detail.get('remark', ''))

                self.update_totals()

        except Exception as e:
            QMessageBox.warning(self, "警告", f"加载入库单数据失败: {str(e)}")

    def set_styles(self):
        """设置样式"""
        try:
            # 应用统一样式
            unified_style = get_unified_stylesheet()

            # 添加对话框特定样式
            dialog_style = """
                /* ==================== 对话框特定样式 ==================== */
                QDialog {
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 #E3F2FD, stop:1 #BBDEFB);
                    border: 2px solid #1976D2;
                    border-radius: 12px;
                }

                QGroupBox {
                    font-weight: bold;
                    color: #1976D2;
                    border: 2px solid #42A5F5;
                    border-radius: 8px;
                    margin-top: 12px;
                    padding-top: 12px;
                    background: rgba(255, 255, 255, 0.95);
                }

                QGroupBox::title {
                    subcontrol-origin: margin;
                    left: 12px;
                    padding: 0 8px 0 8px;
                    background: #FFFFFF;
                    font-size: 14px;
                }

                QFormLayout QLabel {
                    font-weight: bold;
                    color: #424242;
                    min-width: 80px;
                }

                #detailsTable {
                    background: #FFFFFF;
                    border: 2px solid #E0E0E0;
                    border-radius: 8px;
                    gridline-color: #F5F5F5;
                    selection-background-color: #BBDEFB;
                    alternate-background-color: #FAFAFA;
                }

                #detailsTable::item {
                    padding: 8px;
                    border-bottom: 1px solid #F5F5F5;
                }

                #detailsTable::item:selected {
                    background: #BBDEFB;
                    color: #1976D2;
                }

                #detailsTable QHeaderView::section {
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                        stop:0 #BBDEFB, stop:1 #90CAF9);
                    color: #1976D2;
                    padding: 12px;
                    border: none;
                    border-right: 1px solid #64B5F6;
                    font-weight: bold;
                    font-size: 13px;
                }

                #totalLabel {
                    font-weight: bold;
                    color: #1976D2;
                    font-size: 14px;
                    padding: 4px 8px;
                    background: rgba(187, 222, 251, 0.3);
                    border-radius: 4px;
                }

                #addButton {
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                        stop:0 #4CAF50, stop:1 #388E3C);
                    color: white;
                    border: none;
                    border-radius: 6px;
                    padding: 8px 16px;
                    font-weight: bold;
                    min-width: 100px;
                    min-height: 32px;
                }

                #addButton:hover {
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                        stop:0 #388E3C, stop:1 #2E7D32);
                }

                #newProductButton {
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                        stop:0 #2196F3, stop:1 #1976D2);
                    color: white;
                    border: none;
                    border-radius: 6px;
                    padding: 8px 16px;
                    font-weight: bold;
                    min-width: 100px;
                    min-height: 32px;
                }

                #newProductButton:hover {
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                        stop:0 #1976D2, stop:1 #1565C0);
                }

                #deleteButton {
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                        stop:0 #F44336, stop:1 #D32F2F);
                    color: white;
                    border: none;
                    border-radius: 6px;
                    padding: 8px 16px;
                    font-weight: bold;
                    min-width: 100px;
                    min-height: 32px;
                }

                #deleteButton:hover {
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                        stop:0 #D32F2F, stop:1 #C62828);
                }

                #successButton {
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                        stop:0 #4CAF50, stop:1 #388E3C);
                    color: white;
                    border: none;
                    border-radius: 6px;
                    padding: 8px 16px;
                    font-weight: bold;
                    min-width: 120px;
                    min-height: 36px;
                }

                #successButton:hover {
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                        stop:0 #388E3C, stop:1 #2E7D32);
                }

                #cancelButton {
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                        stop:0 #9E9E9E, stop:1 #757575);
                    color: white;
                    border: none;
                    border-radius: 6px;
                    padding: 8px 16px;
                    font-weight: bold;
                    min-width: 80px;
                    min-height: 36px;
                }

                #cancelButton:hover {
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                        stop:0 #757575, stop:1 #616161);
                }
            """

            self.setStyleSheet(unified_style + dialog_style)

        except Exception as e:
            print(f"设置样式失败: {e}")
