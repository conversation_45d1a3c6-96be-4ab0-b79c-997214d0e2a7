#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Excel处理服务
用于处理需求计划Excel文件的导入和导出
"""

import os
import sys
from datetime import datetime
from typing import List, Dict, Tuple, Optional

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

try:
    import pandas as pd
    import openpyxl
    from openpyxl.styles import Font, PatternFill, Alignment
    EXCEL_AVAILABLE = True
except ImportError:
    EXCEL_AVAILABLE = False
    print("警告: pandas 或 openpyxl 未安装，Excel功能将不可用")
    # 创建占位符类，避免导入错误
    class Font:
        def __init__(self, **kwargs): pass
    class PatternFill:
        def __init__(self, **kwargs): pass
    class Alignment:
        def __init__(self, **kwargs): pass

class ExcelService:
    """Excel处理服务类"""
    
    def __init__(self):
        self.template_columns = [
            '序号', '商品编码', '商品名称', '规格型号', '单位', 
            '需求数量', '需求日期', '备注'
        ]
    
    def is_available(self) -> bool:
        """检查Excel功能是否可用"""
        return EXCEL_AVAILABLE
    
    def read_demand_plan_excel(self, file_path: str) -> Tuple[bool, List[Dict], str]:
        """读取需求计划Excel文件"""
        if not self.is_available():
            return False, [], "Excel处理库未安装"
        
        try:
            # 读取Excel文件
            df = pd.read_excel(file_path, sheet_name=0)
            
            # 验证列名
            required_columns = ['商品编码', '商品名称', '需求数量']
            missing_columns = [col for col in required_columns if col not in df.columns]
            
            if missing_columns:
                return False, [], f"缺少必要列: {', '.join(missing_columns)}"
            
            # 转换为字典列表
            demand_items = []
            for index, row in df.iterrows():
                # 跳过空行
                if pd.isna(row.get('商品编码')) or pd.isna(row.get('商品名称')):
                    continue
                
                # 验证数量
                quantity = row.get('需求数量', 0)
                try:
                    quantity = int(float(quantity))
                    if quantity <= 0:
                        return False, [], f"第{index+2}行：需求数量必须大于0"
                except (ValueError, TypeError):
                    return False, [], f"第{index+2}行：需求数量格式错误"
                
                # 处理日期
                required_date = row.get('需求日期')
                if pd.notna(required_date):
                    if isinstance(required_date, str):
                        try:
                            required_date = datetime.strptime(required_date, '%Y-%m-%d').date()
                        except ValueError:
                            try:
                                required_date = datetime.strptime(required_date, '%Y/%m/%d').date()
                            except ValueError:
                                return False, [], f"第{index+2}行：需求日期格式错误，请使用YYYY-MM-DD格式"
                    elif hasattr(required_date, 'date'):
                        required_date = required_date.date()
                else:
                    required_date = None
                
                demand_item = {
                    'row_number': index + 2,
                    'product_code': str(row.get('商品编码', '')).strip(),
                    'product_name': str(row.get('商品名称', '')).strip(),
                    'model': str(row.get('规格型号', '')).strip() if pd.notna(row.get('规格型号')) else '',
                    'unit': str(row.get('单位', '')).strip() if pd.notna(row.get('单位')) else '',
                    'quantity': quantity,
                    'required_date': required_date,
                    'remark': str(row.get('备注', '')).strip() if pd.notna(row.get('备注')) else ''
                }
                
                demand_items.append(demand_item)
            
            if not demand_items:
                return False, [], "Excel文件中没有有效的需求数据"
            
            return True, demand_items, f"成功读取{len(demand_items)}条需求记录"
            
        except FileNotFoundError:
            return False, [], "文件不存在"
        except PermissionError:
            return False, [], "文件被占用，请关闭Excel后重试"
        except Exception as e:
            return False, [], f"读取Excel文件失败: {str(e)}"
    
    def create_demand_plan_template(self, file_path: str) -> Tuple[bool, str]:
        """创建需求计划模板文件"""
        if not self.is_available():
            return False, "Excel处理库未安装"
        
        try:
            # 创建示例数据
            sample_data = [
                {
                    '序号': 1,
                    '商品编码': 'P001',
                    '商品名称': '螺栓M8*20',
                    '规格型号': 'M8*20',
                    '单位': '个',
                    '需求数量': 100,
                    '需求日期': '2023-12-15',
                    '备注': '生产线急需'
                },
                {
                    '序号': 2,
                    '商品编码': 'P002',
                    '商品名称': '轴承6205',
                    '规格型号': '6205',
                    '单位': '个',
                    '需求数量': 20,
                    '需求日期': '2023-12-20',
                    '备注': '设备维修用'
                }
            ]
            
            # 创建DataFrame
            df = pd.DataFrame(sample_data)
            
            # 创建Excel文件
            with pd.ExcelWriter(file_path, engine='openpyxl') as writer:
                df.to_excel(writer, sheet_name='需求计划', index=False)
                
                # 获取工作表
                worksheet = writer.sheets['需求计划']
                
                # 设置列宽
                column_widths = {
                    'A': 8,   # 序号
                    'B': 15,  # 商品编码
                    'C': 25,  # 商品名称
                    'D': 15,  # 规格型号
                    'E': 8,   # 单位
                    'F': 12,  # 需求数量
                    'G': 15,  # 需求日期
                    'H': 20   # 备注
                }
                
                for col, width in column_widths.items():
                    worksheet.column_dimensions[col].width = width
                
                # 设置标题行样式
                title_font = Font(bold=True, color='FFFFFF')
                title_fill = PatternFill(start_color='366092', end_color='366092', fill_type='solid')
                title_alignment = Alignment(horizontal='center', vertical='center')
                
                for cell in worksheet[1]:
                    cell.font = title_font
                    cell.fill = title_fill
                    cell.alignment = title_alignment
                
                # 添加说明
                worksheet['A10'] = '填写说明：'
                worksheet['A11'] = '1. 商品编码和商品名称为必填项'
                worksheet['A12'] = '2. 需求数量必须为正整数'
                worksheet['A13'] = '3. 需求日期格式：YYYY-MM-DD（如：2023-12-15）'
                worksheet['A14'] = '4. 请勿修改表头，可以删除示例数据'
                
                # 设置说明样式
                for row in range(10, 15):
                    worksheet[f'A{row}'].font = Font(color='666666', size=10)
            
            return True, "模板文件创建成功"
            
        except PermissionError:
            return False, "文件被占用，请关闭Excel后重试"
        except Exception as e:
            return False, f"创建模板文件失败: {str(e)}"
    
    def export_inventory_report(self, inventory_data: List[Dict], file_path: str) -> Tuple[bool, str]:
        """导出库存报表"""
        if not self.is_available():
            return False, "Excel处理库未安装"
        
        try:
            # 转换数据格式
            report_data = []
            for item in inventory_data:
                report_data.append({
                    '商品编码': item.get('product_code', ''),
                    '商品名称': item.get('product_name', ''),
                    '规格型号': item.get('model', ''),
                    '分类': item.get('category_name', ''),
                    '库位': item.get('location_code', ''),
                    '当前库存': item.get('quantity', 0),
                    '预留库存': item.get('reserved_quantity', 0),
                    '可用库存': item.get('available_quantity', 0),
                    '状态': item.get('status', '')
                })
            
            # 创建DataFrame
            df = pd.DataFrame(report_data)
            
            # 创建Excel文件
            with pd.ExcelWriter(file_path, engine='openpyxl') as writer:
                df.to_excel(writer, sheet_name='库存报表', index=False)
                
                # 获取工作表
                worksheet = writer.sheets['库存报表']
                
                # 设置列宽
                for column in worksheet.columns:
                    max_length = 0
                    column_letter = column[0].column_letter
                    for cell in column:
                        try:
                            if len(str(cell.value)) > max_length:
                                max_length = len(str(cell.value))
                        except:
                            pass
                    adjusted_width = min(max_length + 2, 30)
                    worksheet.column_dimensions[column_letter].width = adjusted_width
                
                # 设置标题行样式
                title_font = Font(bold=True, color='FFFFFF')
                title_fill = PatternFill(start_color='366092', end_color='366092', fill_type='solid')
                title_alignment = Alignment(horizontal='center', vertical='center')
                
                for cell in worksheet[1]:
                    cell.font = title_font
                    cell.fill = title_fill
                    cell.alignment = title_alignment
                
                # 添加报表信息
                info_row = len(report_data) + 3
                worksheet[f'A{info_row}'] = f'报表生成时间：{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}'
                worksheet[f'A{info_row+1}'] = f'总记录数：{len(report_data)}'
            
            return True, "库存报表导出成功"
            
        except PermissionError:
            return False, "文件被占用，请关闭Excel后重试"
        except Exception as e:
            return False, f"导出库存报表失败: {str(e)}"
    
    def export_inbound_report(self, inbound_data: List[Dict], file_path: str) -> Tuple[bool, str]:
        """导出入库报表"""
        if not self.is_available():
            return False, "Excel处理库未安装"
        
        try:
            # 转换数据格式
            report_data = []
            for item in inbound_data:
                report_data.append({
                    '入库单号': item.get('order_no', ''),
                    '入库类型': item.get('type', ''),
                    '供应商': item.get('supplier_name', ''),
                    '操作员': item.get('operator_name', ''),
                    '状态': item.get('status', ''),
                    '总金额': item.get('total_amount', 0),
                    '创建时间': item.get('created_at', ''),
                    '审批时间': item.get('approved_at', '')
                })
            
            # 创建DataFrame
            df = pd.DataFrame(report_data)
            
            # 创建Excel文件
            with pd.ExcelWriter(file_path, engine='openpyxl') as writer:
                df.to_excel(writer, sheet_name='入库报表', index=False)
                
                # 设置样式（类似库存报表）
                worksheet = writer.sheets['入库报表']
                
                # 自动调整列宽
                for column in worksheet.columns:
                    max_length = 0
                    column_letter = column[0].column_letter
                    for cell in column:
                        try:
                            if len(str(cell.value)) > max_length:
                                max_length = len(str(cell.value))
                        except:
                            pass
                    adjusted_width = min(max_length + 2, 30)
                    worksheet.column_dimensions[column_letter].width = adjusted_width
            
            return True, "入库报表导出成功"
            
        except Exception as e:
            return False, f"导出入库报表失败: {str(e)}"
    
    def export_outbound_report(self, outbound_data: List[Dict], file_path: str) -> Tuple[bool, str]:
        """导出出库报表"""
        if not self.is_available():
            return False, "Excel处理库未安装"
        
        try:
            # 转换数据格式
            report_data = []
            for item in outbound_data:
                report_data.append({
                    '出库单号': item.get('order_no', ''),
                    '出库类型': item.get('type', ''),
                    '部门': item.get('department', ''),
                    '申请人': item.get('applicant', ''),
                    '操作员': item.get('operator_name', ''),
                    '状态': item.get('status', ''),
                    '创建时间': item.get('created_at', ''),
                    '审批时间': item.get('approved_at', '')
                })
            
            # 创建DataFrame
            df = pd.DataFrame(report_data)
            
            # 创建Excel文件
            with pd.ExcelWriter(file_path, engine='openpyxl') as writer:
                df.to_excel(writer, sheet_name='出库报表', index=False)
                
                # 设置样式
                worksheet = writer.sheets['出库报表']
                
                # 自动调整列宽
                for column in worksheet.columns:
                    max_length = 0
                    column_letter = column[0].column_letter
                    for cell in column:
                        try:
                            if len(str(cell.value)) > max_length:
                                max_length = len(str(cell.value))
                        except:
                            pass
                    adjusted_width = min(max_length + 2, 30)
                    worksheet.column_dimensions[column_letter].width = adjusted_width
            
            return True, "出库报表导出成功"
            
        except Exception as e:
            return False, f"导出出库报表失败: {str(e)}"
    
    def validate_excel_file(self, file_path: str) -> Tuple[bool, str]:
        """验证Excel文件格式"""
        if not self.is_available():
            return False, "Excel处理库未安装"
        
        try:
            # 检查文件是否存在
            if not os.path.exists(file_path):
                return False, "文件不存在"
            
            # 检查文件扩展名
            _, ext = os.path.splitext(file_path)
            if ext.lower() not in ['.xlsx', '.xls']:
                return False, "文件格式不正确，请选择Excel文件"
            
            # 尝试读取文件
            df = pd.read_excel(file_path, sheet_name=0, nrows=1)
            
            return True, "文件格式验证通过"
            
        except Exception as e:
            return False, f"文件验证失败: {str(e)}"

    def read_inbound_orders_excel(self, file_path: str) -> Tuple[bool, List[Dict], str]:
        """读取入库单Excel文件"""
        if not self.is_available():
            return False, [], "Excel处理库未安装"

        try:
            # 读取Excel文件
            df = pd.read_excel(file_path, sheet_name=0)

            # 验证列名
            required_columns = ['入库类型', '供应商', '仓库', '操作员']
            missing_columns = [col for col in required_columns if col not in df.columns]

            if missing_columns:
                return False, [], f"缺少必要列: {', '.join(missing_columns)}"

            # 转换数据
            orders_data = []
            for index, row in df.iterrows():
                try:
                    order_data = {
                        'type': self._convert_inbound_type(row.get('入库类型', '')),
                        'supplier_name': row.get('供应商', ''),
                        'warehouse_name': row.get('仓库', ''),
                        'operator_name': row.get('操作员', ''),
                        'remark': row.get('备注', ''),
                        'total_amount': float(row.get('总金额', 0)) if row.get('总金额') else None
                    }

                    # 验证必要字段
                    if not order_data['type']:
                        continue

                    orders_data.append(order_data)

                except Exception as e:
                    print(f"处理第{index+1}行数据时出错: {str(e)}")
                    continue

            return True, orders_data, f"成功读取 {len(orders_data)} 条入库单数据"

        except Exception as e:
            return False, [], f"读取入库单Excel文件失败: {str(e)}"

    def read_outbound_orders_excel(self, file_path: str) -> Tuple[bool, List[Dict], str]:
        """读取出库单Excel文件"""
        if not self.is_available():
            return False, [], "Excel处理库未安装"

        try:
            # 读取Excel文件
            df = pd.read_excel(file_path, sheet_name=0)

            # 验证列名
            required_columns = ['出库类型', '申请部门', '申请人', '仓库', '操作员']
            missing_columns = [col for col in required_columns if col not in df.columns]

            if missing_columns:
                return False, [], f"缺少必要列: {', '.join(missing_columns)}"

            # 转换数据
            orders_data = []
            for index, row in df.iterrows():
                try:
                    order_data = {
                        'type': self._convert_outbound_type(row.get('出库类型', '')),
                        'department': row.get('申请部门', ''),
                        'applicant': row.get('申请人', ''),
                        'warehouse_name': row.get('仓库', ''),
                        'operator_name': row.get('操作员', ''),
                        'remark': row.get('备注', '')
                    }

                    # 验证必要字段
                    if not order_data['type'] or not order_data['department']:
                        continue

                    orders_data.append(order_data)

                except Exception as e:
                    print(f"处理第{index+1}行数据时出错: {str(e)}")
                    continue

            return True, orders_data, f"成功读取 {len(orders_data)} 条出库单数据"

        except Exception as e:
            return False, [], f"读取出库单Excel文件失败: {str(e)}"

    def create_inbound_template(self, file_path: str) -> Tuple[bool, str]:
        """创建入库单导入模板"""
        if not self.is_available():
            return False, "Excel处理库未安装"

        try:
            # 创建示例数据
            template_data = [
                {
                    '序号': 1,
                    '入库类型': '采购入库',
                    '供应商': '北京机械配件有限公司',
                    '仓库': 'Main Warehouse',
                    '操作员': 'admin',
                    '总金额': 15000.00,
                    '备注': '采购入库示例'
                },
                {
                    '序号': 2,
                    '入库类型': '退货入库',
                    '供应商': '上海电气设备公司',
                    '仓库': 'Main Warehouse',
                    '操作员': 'admin',
                    '总金额': 8500.00,
                    '备注': '退货入库示例'
                }
            ]

            # 创建DataFrame
            df = pd.DataFrame(template_data)

            # 创建Excel文件
            with pd.ExcelWriter(file_path, engine='openpyxl') as writer:
                df.to_excel(writer, sheet_name='入库单模板', index=False)

                # 获取工作表
                worksheet = writer.sheets['入库单模板']

                # 设置列宽
                column_widths = {
                    'A': 8,   # 序号
                    'B': 15,  # 入库类型
                    'C': 25,  # 供应商
                    'D': 20,  # 仓库
                    'E': 15,  # 操作员
                    'F': 12,  # 总金额
                    'G': 20   # 备注
                }

                for col, width in column_widths.items():
                    worksheet.column_dimensions[col].width = width

                # 设置标题行样式
                title_font = Font(bold=True, color='FFFFFF')
                title_fill = PatternFill(start_color='366092', end_color='366092', fill_type='solid')
                title_alignment = Alignment(horizontal='center', vertical='center')

                for cell in worksheet[1]:
                    cell.font = title_font
                    cell.fill = title_fill
                    cell.alignment = title_alignment

                # 添加说明
                worksheet['A5'] = '填写说明：'
                worksheet['A6'] = '1. 入库类型：采购入库、退货入库、调拨入库'
                worksheet['A7'] = '2. 供应商、仓库、操作员名称必须与系统中已有的名称一致'
                worksheet['A8'] = '3. 总金额为可选项，可以为空'
                worksheet['A9'] = '4. 请勿修改表头，可以删除示例数据'

                # 设置说明样式
                for row in range(5, 10):
                    worksheet[f'A{row}'].font = Font(color='666666', size=10)

            return True, "入库单模板创建成功"

        except Exception as e:
            return False, f"创建入库单模板失败: {str(e)}"

    def create_outbound_template(self, file_path: str) -> Tuple[bool, str]:
        """创建出库单导入模板"""
        if not self.is_available():
            return False, "Excel处理库未安装"

        try:
            # 创建示例数据
            template_data = [
                {
                    '序号': 1,
                    '出库类型': '领用出库',
                    '申请部门': '生产部',
                    '申请人': '王五',
                    '仓库': 'Main Warehouse',
                    '操作员': 'admin',
                    '备注': '生产领用示例'
                },
                {
                    '序号': 2,
                    '出库类型': '借用出库',
                    '申请部门': '维修部',
                    '申请人': '赵六',
                    '仓库': 'Main Warehouse',
                    '操作员': 'admin',
                    '备注': '维修借用示例'
                }
            ]

            # 创建DataFrame
            df = pd.DataFrame(template_data)

            # 创建Excel文件
            with pd.ExcelWriter(file_path, engine='openpyxl') as writer:
                df.to_excel(writer, sheet_name='出库单模板', index=False)

                # 获取工作表
                worksheet = writer.sheets['出库单模板']

                # 设置列宽
                column_widths = {
                    'A': 8,   # 序号
                    'B': 15,  # 出库类型
                    'C': 15,  # 申请部门
                    'D': 15,  # 申请人
                    'E': 20,  # 仓库
                    'F': 15,  # 操作员
                    'G': 20   # 备注
                }

                for col, width in column_widths.items():
                    worksheet.column_dimensions[col].width = width

                # 设置标题行样式
                title_font = Font(bold=True, color='FFFFFF')
                title_fill = PatternFill(start_color='366092', end_color='366092', fill_type='solid')
                title_alignment = Alignment(horizontal='center', vertical='center')

                for cell in worksheet[1]:
                    cell.font = title_font
                    cell.fill = title_fill
                    cell.alignment = title_alignment

                # 添加说明
                worksheet['A5'] = '填写说明：'
                worksheet['A6'] = '1. 出库类型：借用出库、领用出库、调拨出库'
                worksheet['A7'] = '2. 申请部门和申请人为必填项'
                worksheet['A8'] = '3. 仓库、操作员名称必须与系统中已有的名称一致'
                worksheet['A9'] = '4. 请勿修改表头，可以删除示例数据'

                # 设置说明样式
                for row in range(5, 10):
                    worksheet[f'A{row}'].font = Font(color='666666', size=10)

            return True, "出库单模板创建成功"

        except Exception as e:
            return False, f"创建出库单模板失败: {str(e)}"

    def _convert_inbound_type(self, type_name: str) -> str:
        """转换入库类型"""
        type_mapping = {
            '采购入库': 'purchase',
            '退货入库': 'return',
            '调拨入库': 'transfer'
        }
        return type_mapping.get(type_name, '')

    def _convert_outbound_type(self, type_name: str) -> str:
        """转换出库类型"""
        type_mapping = {
            '借用出库': 'borrow',
            '领用出库': 'issue',
            '调拨出库': 'transfer'
        }
        return type_mapping.get(type_name, '')