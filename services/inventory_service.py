#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
库存管理服务类
处理入库、出库、库存查询、需求计划等业务逻辑
"""

import os
import sys
from datetime import datetime, date
from typing import List, Dict, Optional, Tuple

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from config.database import get_connection

class InventoryService:
    """库存管理服务类"""
    
    def __init__(self):
        self.conn = None
        
    def get_connection(self):
        """获取数据库连接"""
        if not self.conn:
            self.conn = get_connection()
        return self.conn
    
    # 入库管理
    def create_inbound_order(self, order_data: Dict) -> Tuple[bool, str]:
        """创建入库单"""
        try:
            conn = self.get_connection()
            cursor = conn.cursor()
            
            # 生成入库单号
            order_no = self.generate_inbound_order_no()
            
            # 插入入库单主表
            insert_sql = """
                INSERT INTO inbound_orders (order_no, type, supplier_id, warehouse_id, 
                                          operator_id, status, remark)
                VALUES (%s, %s, %s, %s, %s, %s, %s)
                RETURNING id
            """
            
            cursor.execute(insert_sql, (
                order_no,
                order_data.get('type'),
                order_data.get('supplier_id'),
                order_data.get('warehouse_id'),
                order_data.get('operator_id'),
                'pending',
                order_data.get('remark', '')
            ))
            
            order_id = cursor.fetchone()[0]
            conn.commit()
            
            return True, f"入库单创建成功，单号: {order_no}"
            
        except Exception as e:
            if conn:
                conn.rollback()
            return False, f"创建入库单失败: {str(e)}"
    
    def add_inbound_detail(self, order_id: int, detail_data: Dict) -> Tuple[bool, str]:
        """添加入库明细"""
        try:
            conn = self.get_connection()
            cursor = conn.cursor()
            
            # 插入入库明细
            insert_sql = """
                INSERT INTO inbound_details (inbound_order_id, product_id, location_id,
                                           quantity, unit_price, total_price, batch_no, remark)
                VALUES (%s, %s, %s, %s, %s, %s, %s, %s)
            """
            
            cursor.execute(insert_sql, (
                order_id,
                detail_data.get('product_id'),
                detail_data.get('location_id'),
                detail_data.get('quantity'),
                detail_data.get('unit_price'),
                detail_data.get('total_price'),
                detail_data.get('batch_no', ''),
                detail_data.get('remark', '')
            ))
            
            conn.commit()
            return True, "入库明细添加成功"
            
        except Exception as e:
            if conn:
                conn.rollback()
            return False, f"添加入库明细失败: {str(e)}"
    
    def confirm_inbound(self, order_id: int, operator_id: int) -> Tuple[bool, str]:
        """确认入库，更新库存"""
        try:
            conn = self.get_connection()
            cursor = conn.cursor()
            
            # 获取入库明细
            cursor.execute("""
                SELECT product_id, location_id, quantity 
                FROM inbound_details 
                WHERE inbound_order_id = %s
            """, (order_id,))
            
            details = cursor.fetchall()
            
            # 更新库存
            for product_id, location_id, quantity in details:
                # 检查库存记录是否存在
                cursor.execute("""
                    SELECT id, quantity FROM inventory 
                    WHERE product_id = %s AND location_id = %s
                """, (product_id, location_id))
                
                inventory_record = cursor.fetchone()
                
                if inventory_record:
                    # 更新现有库存
                    new_quantity = inventory_record[1] + quantity
                    cursor.execute("""
                        UPDATE inventory 
                        SET quantity = %s, last_updated = CURRENT_TIMESTAMP
                        WHERE id = %s
                    """, (new_quantity, inventory_record[0]))
                else:
                    # 创建新库存记录
                    cursor.execute("""
                        INSERT INTO inventory (product_id, location_id, quantity)
                        VALUES (%s, %s, %s)
                    """, (product_id, location_id, quantity))
            
            # 更新入库单状态
            cursor.execute("""
                UPDATE inbound_orders 
                SET status = 'completed', approved_at = CURRENT_TIMESTAMP, approved_by = %s
                WHERE id = %s
            """, (operator_id, order_id))
            
            conn.commit()
            return True, "入库确认成功，库存已更新"
            
        except Exception as e:
            if conn:
                conn.rollback()
            return False, f"入库确认失败: {str(e)}"
    
    # 出库管理
    def create_outbound_order(self, order_data: Dict) -> Tuple[bool, str]:
        """创建出库单"""
        try:
            conn = self.get_connection()
            cursor = conn.cursor()
            
            # 生成出库单号
            order_no = self.generate_outbound_order_no()
            
            # 插入出库单主表
            insert_sql = """
                INSERT INTO outbound_orders (order_no, type, department, applicant,
                                           warehouse_id, operator_id, status, remark)
                VALUES (%s, %s, %s, %s, %s, %s, %s, %s)
                RETURNING id
            """
            
            cursor.execute(insert_sql, (
                order_no,
                order_data.get('type'),
                order_data.get('department'),
                order_data.get('applicant'),
                order_data.get('warehouse_id'),
                order_data.get('operator_id'),
                'pending',
                order_data.get('remark', '')
            ))
            
            order_id = cursor.fetchone()[0]
            conn.commit()
            
            return True, f"出库单创建成功，单号: {order_no}"

        except Exception as e:
            if conn:
                conn.rollback()
            return False, f"创建出库单失败: {str(e)}"

    def add_outbound_detail(self, order_id: int, detail_data: Dict) -> Tuple[bool, str]:
        """添加出库明细"""
        try:
            conn = self.get_connection()
            cursor = conn.cursor()

            # 插入出库明细
            insert_sql = """
                INSERT INTO outbound_details (outbound_order_id, product_id, location_id,
                                           quantity, batch_no, remark)
                VALUES (%s, %s, %s, %s, %s, %s)
            """

            cursor.execute(insert_sql, (
                order_id,
                detail_data.get('product_id'),
                detail_data.get('location_id'),
                detail_data.get('quantity'),
                detail_data.get('batch_no', ''),
                detail_data.get('remark', '')
            ))

            conn.commit()
            return True, "出库明细添加成功"

        except Exception as e:
            if conn:
                conn.rollback()
            return False, f"添加出库明细失败: {str(e)}"
    
    def confirm_outbound(self, order_id: int, operator_id: int) -> Tuple[bool, str]:
        """确认出库，更新库存"""
        try:
            conn = self.get_connection()
            cursor = conn.cursor()
            
            # 获取出库明细
            cursor.execute("""
                SELECT product_id, location_id, quantity 
                FROM outbound_details 
                WHERE outbound_order_id = %s
            """, (order_id,))
            
            details = cursor.fetchall()
            
            # 检查库存是否充足
            for product_id, location_id, quantity in details:
                cursor.execute("""
                    SELECT quantity FROM inventory 
                    WHERE product_id = %s AND location_id = %s
                """, (product_id, location_id))
                
                current_stock = cursor.fetchone()
                if not current_stock or current_stock[0] < quantity:
                    return False, f"商品库存不足，无法出库"
            
            # 更新库存
            for product_id, location_id, quantity in details:
                cursor.execute("""
                    UPDATE inventory 
                    SET quantity = quantity - %s, last_updated = CURRENT_TIMESTAMP
                    WHERE product_id = %s AND location_id = %s
                """, (quantity, product_id, location_id))
            
            # 更新出库单状态
            cursor.execute("""
                UPDATE outbound_orders 
                SET status = 'completed', approved_at = CURRENT_TIMESTAMP, approved_by = %s
                WHERE id = %s
            """, (operator_id, order_id))
            
            conn.commit()
            return True, "出库确认成功，库存已更新"
            
        except Exception as e:
            if conn:
                conn.rollback()
            return False, f"出库确认失败: {str(e)}"
    
    # 库存查询
    def get_inventory_list(self, filters: Dict = None) -> List[Dict]:
        """获取库存列表"""
        try:
            conn = self.get_connection()
            cursor = conn.cursor()
            
            base_sql = """
                SELECT p.code, p.name, p.model, c.name as category_name,
                       l.location_code, i.quantity, i.reserved_quantity,
                       (i.quantity - i.reserved_quantity) as available_quantity,
                       CASE
                           WHEN i.quantity = 0 THEN '缺货'
                           WHEN i.quantity <= p.min_stock THEN '低库存'
                           ELSE '正常'
                       END as status,
                       i.product_id, i.location_id, l.warehouse_id
                FROM inventory i
                JOIN products p ON i.product_id = p.id
                JOIN locations l ON i.location_id = l.id
                LEFT JOIN categories c ON p.category_id = c.id
                WHERE p.is_active = TRUE
            """

            params = []

            # 添加过滤条件
            if filters:
                if filters.get('product_code'):
                    base_sql += " AND p.code LIKE %s"
                    params.append(f"%{filters['product_code']}%")

                if filters.get('product_name'):
                    base_sql += " AND p.name LIKE %s"
                    params.append(f"%{filters['product_name']}%")

                if filters.get('category_id'):
                    base_sql += " AND p.category_id = %s"
                    params.append(filters['category_id'])

                if filters.get('warehouse_id'):
                    base_sql += " AND l.warehouse_id = %s"
                    params.append(filters['warehouse_id'])
            
            base_sql += " ORDER BY p.code"
            
            cursor.execute(base_sql, params)
            results = cursor.fetchall()
            
            inventory_list = []
            for row in results:
                inventory_list.append({
                    'product_code': row[0],
                    'product_name': row[1],
                    'model': row[2],
                    'category_name': row[3],
                    'location_code': row[4],
                    'quantity': row[5],
                    'reserved_quantity': row[6],
                    'available_quantity': row[7],
                    'status': row[8],
                    'product_id': row[9],
                    'location_id': row[10],
                    'warehouse_id': row[11]
                })
            
            return inventory_list
            
        except Exception as e:
            print(f"获取库存列表失败: {e}")
            return []
    
    def get_low_stock_products(self) -> List[Dict]:
        """获取低库存商品"""
        try:
            conn = self.get_connection()
            cursor = conn.cursor()
            
            cursor.execute("""
                SELECT p.code, p.name, p.min_stock, SUM(i.quantity) as total_quantity
                FROM products p
                LEFT JOIN inventory i ON p.id = i.product_id
                WHERE p.is_active = TRUE
                GROUP BY p.id, p.code, p.name, p.min_stock
                HAVING SUM(COALESCE(i.quantity, 0)) <= p.min_stock
                ORDER BY p.code
            """)
            
            results = cursor.fetchall()
            
            low_stock_list = []
            for row in results:
                low_stock_list.append({
                    'product_code': row[0],
                    'product_name': row[1],
                    'min_stock': row[2],
                    'current_stock': row[3] or 0
                })
            
            return low_stock_list
            
        except Exception as e:
            print(f"获取低库存商品失败: {e}")
            return []
    
    # 需求计划管理
    def create_demand_plan(self, plan_data: Dict) -> Tuple[bool, str]:
        """创建需求计划"""
        try:
            conn = self.get_connection()
            cursor = conn.cursor()
            
            # 生成计划编号
            plan_no = self.generate_demand_plan_no()
            
            # 插入需求计划主表
            insert_sql = """
                INSERT INTO demand_plans (plan_no, department, applicant, applicant_id,
                                        file_path, remark)
                VALUES (%s, %s, %s, %s, %s, %s)
                RETURNING id
            """
            
            cursor.execute(insert_sql, (
                plan_no,
                plan_data.get('department'),
                plan_data.get('applicant'),
                plan_data.get('applicant_id'),
                plan_data.get('file_path', ''),
                plan_data.get('remark', '')
            ))
            
            plan_id = cursor.fetchone()[0]
            conn.commit()
            
            return True, f"需求计划创建成功，编号: {plan_no}"
            
        except Exception as e:
            if conn:
                conn.rollback()
            return False, f"创建需求计划失败: {str(e)}"

    def approve_inbound_order(self, order_id: int, approver_id: int) -> Tuple[bool, str]:
        """审批入库单"""
        try:
            conn = self.get_connection()
            cursor = conn.cursor()

            # 检查入库单状态
            cursor.execute("""
                SELECT status FROM inbound_orders WHERE id = %s
            """, (order_id,))

            result = cursor.fetchone()
            if not result:
                return False, "入库单不存在"

            if result[0] != 'pending':
                return False, f"入库单状态不正确，当前状态: {result[0]}"

            # 更新入库单状态
            cursor.execute("""
                UPDATE inbound_orders
                SET status = 'approved', approved_at = NOW(), approved_by = %s
                WHERE id = %s
            """, (approver_id, order_id))

            # 更新库存
            cursor.execute("""
                SELECT id.product_id, id.location_id, id.quantity
                FROM inbound_details id
                WHERE id.inbound_order_id = %s
            """, (order_id,))

            details = cursor.fetchall()
            for product_id, location_id, quantity in details:
                # 检查库存记录是否存在
                cursor.execute("""
                    SELECT id FROM inventory
                    WHERE product_id = %s AND location_id = %s
                """, (product_id, location_id))

                if cursor.fetchone():
                    # 更新现有库存
                    cursor.execute("""
                        UPDATE inventory
                        SET quantity = quantity + %s, last_updated = NOW()
                        WHERE product_id = %s AND location_id = %s
                    """, (quantity, product_id, location_id))
                else:
                    # 创建新库存记录
                    cursor.execute("""
                        INSERT INTO inventory (product_id, location_id, quantity, last_updated)
                        VALUES (%s, %s, %s, NOW())
                    """, (product_id, location_id, quantity))

            conn.commit()
            return True, "入库单审批成功，库存已更新"

        except Exception as e:
            if conn:
                conn.rollback()
            return False, f"审批入库单失败: {str(e)}"

    def approve_outbound_order(self, order_id: int, approver_id: int) -> Tuple[bool, str]:
        """审批出库单"""
        try:
            conn = self.get_connection()
            cursor = conn.cursor()

            # 检查出库单状态
            cursor.execute("""
                SELECT status FROM outbound_orders WHERE id = %s
            """, (order_id,))

            result = cursor.fetchone()
            if not result:
                return False, "出库单不存在"

            if result[0] != 'pending':
                return False, f"出库单状态不正确，当前状态: {result[0]}"

            # 检查库存是否充足
            cursor.execute("""
                SELECT od.product_id, od.location_id, od.quantity, i.quantity as stock_quantity
                FROM outbound_details od
                LEFT JOIN inventory i ON od.product_id = i.product_id AND od.location_id = i.location_id
                WHERE od.outbound_order_id = %s
            """, (order_id,))

            details = cursor.fetchall()
            for product_id, location_id, required_qty, stock_qty in details:
                if not stock_qty or stock_qty < required_qty:
                    return False, f"商品ID {product_id} 在库位ID {location_id} 库存不足"

            # 更新出库单状态
            cursor.execute("""
                UPDATE outbound_orders
                SET status = 'approved', approved_at = NOW(), approved_by = %s
                WHERE id = %s
            """, (approver_id, order_id))

            # 更新库存
            for product_id, location_id, required_qty, stock_qty in details:
                cursor.execute("""
                    UPDATE inventory
                    SET quantity = quantity - %s, last_updated = NOW()
                    WHERE product_id = %s AND location_id = %s
                """, (required_qty, product_id, location_id))

            conn.commit()
            return True, "出库单审批成功，库存已更新"

        except Exception as e:
            if conn:
                conn.rollback()
            return False, f"审批出库单失败: {str(e)}"
    
    def approve_demand_plan(self, plan_id: int, operator_id: int, approved: bool) -> Tuple[bool, str]:
        """审批需求计划"""
        try:
            conn = self.get_connection()
            cursor = conn.cursor()
            
            status = 'approved' if approved else 'rejected'
            
            cursor.execute("""
                UPDATE demand_plans 
                SET status = %s, approved_at = CURRENT_TIMESTAMP, approved_by = %s
                WHERE id = %s
            """, (status, operator_id, plan_id))
            
            conn.commit()
            
            action = "审批通过" if approved else "审批拒绝"
            return True, f"需求计划{action}成功"
            
        except Exception as e:
            if conn:
                conn.rollback()
            return False, f"审批需求计划失败: {str(e)}"
    
    # 辅助方法
    def generate_inbound_order_no(self) -> str:
        """生成入库单号"""
        today = datetime.now().strftime("%Y%m%d")
        
        try:
            conn = self.get_connection()
            cursor = conn.cursor()
            
            cursor.execute("""
                SELECT COUNT(*) FROM inbound_orders 
                WHERE order_no LIKE %s
            """, (f"RK{today}%",))
            
            count = cursor.fetchone()[0] + 1
            return f"RK{today}{count:04d}"
            
        except Exception:
            return f"RK{today}0001"
    
    def generate_outbound_order_no(self) -> str:
        """生成出库单号"""
        today = datetime.now().strftime("%Y%m%d")
        
        try:
            conn = self.get_connection()
            cursor = conn.cursor()
            
            cursor.execute("""
                SELECT COUNT(*) FROM outbound_orders 
                WHERE order_no LIKE %s
            """, (f"CK{today}%",))
            
            count = cursor.fetchone()[0] + 1
            return f"CK{today}{count:04d}"
            
        except Exception:
            return f"CK{today}0001"

    def delete_inbound_order(self, order_id: int) -> Tuple[bool, str]:
        """删除入库单"""
        try:
            conn = self.get_connection()
            cursor = conn.cursor()

            # 检查入库单状态
            cursor.execute("""
                SELECT status FROM inbound_orders WHERE id = %s
            """, (order_id,))

            result = cursor.fetchone()
            if not result:
                return False, "入库单不存在"

            status = result[0]
            if status not in ['pending', 'cancelled']:
                return False, "只能删除待审核或已取消的入库单"

            # 删除入库明细
            cursor.execute("""
                DELETE FROM inbound_details WHERE inbound_order_id = %s
            """, (order_id,))

            # 删除入库单
            cursor.execute("""
                DELETE FROM inbound_orders WHERE id = %s
            """, (order_id,))

            conn.commit()
            return True, "入库单删除成功"

        except Exception as e:
            if conn:
                conn.rollback()
            return False, f"删除入库单失败: {str(e)}"

    def delete_outbound_order(self, order_id: int) -> Tuple[bool, str]:
        """删除出库单"""
        try:
            conn = self.get_connection()
            cursor = conn.cursor()

            # 检查出库单状态
            cursor.execute("""
                SELECT status FROM outbound_orders WHERE id = %s
            """, (order_id,))

            result = cursor.fetchone()
            if not result:
                return False, "出库单不存在"

            status = result[0]
            if status not in ['pending', 'cancelled']:
                return False, "只能删除待审核或已取消的出库单"

            # 删除出库明细
            cursor.execute("""
                DELETE FROM outbound_details WHERE outbound_order_id = %s
            """, (order_id,))

            # 删除出库单
            cursor.execute("""
                DELETE FROM outbound_orders WHERE id = %s
            """, (order_id,))

            conn.commit()
            return True, "出库单删除成功"

        except Exception as e:
            if conn:
                conn.rollback()
            return False, f"删除出库单失败: {str(e)}"

    def get_inbound_orders(self, filters: Dict = None) -> List[Dict]:
        """获取入库单列表"""
        try:
            conn = self.get_connection()
            cursor = conn.cursor()

            base_sql = """
                SELECT io.id, io.order_no, io.type, s.name as supplier_name,
                       w.name as warehouse_name, u.real_name as operator_name,
                       io.status, io.total_amount, io.created_at, io.approved_at
                FROM inbound_orders io
                LEFT JOIN suppliers s ON io.supplier_id = s.id
                LEFT JOIN warehouses w ON io.warehouse_id = w.id
                LEFT JOIN users u ON io.operator_id = u.id
                WHERE 1=1
            """

            params = []

            # 添加过滤条件
            if filters:
                if filters.get('status'):
                    base_sql += " AND io.status = %s"
                    params.append(filters['status'])

                if filters.get('type'):
                    base_sql += " AND io.type = %s"
                    params.append(filters['type'])

                if filters.get('start_date'):
                    base_sql += " AND DATE(io.created_at) >= %s"
                    params.append(filters['start_date'])

                if filters.get('end_date'):
                    base_sql += " AND DATE(io.created_at) <= %s"
                    params.append(filters['end_date'])

            base_sql += " ORDER BY io.created_at DESC"

            cursor.execute(base_sql, params)
            results = cursor.fetchall()

            inbound_orders = []
            for row in results:
                inbound_orders.append({
                    'id': row[0],
                    'order_no': row[1],
                    'type': row[2],
                    'supplier_name': row[3],
                    'warehouse_name': row[4],
                    'operator_name': row[5],
                    'status': row[6],
                    'total_amount': float(row[7]) if row[7] else 0,
                    'created_at': row[8].strftime('%Y-%m-%d %H:%M:%S') if row[8] else '',
                    'approved_at': row[9].strftime('%Y-%m-%d %H:%M:%S') if row[9] else ''
                })

            return inbound_orders

        except Exception as e:
            print(f"获取入库单列表失败: {str(e)}")
            return []

    def get_outbound_orders(self, filters: Dict = None) -> List[Dict]:
        """获取出库单列表"""
        try:
            conn = self.get_connection()
            cursor = conn.cursor()

            base_sql = """
                SELECT oo.id, oo.order_no, oo.type, oo.department, oo.applicant,
                       w.name as warehouse_name, u.real_name as operator_name,
                       oo.status, oo.created_at, oo.approved_at
                FROM outbound_orders oo
                LEFT JOIN warehouses w ON oo.warehouse_id = w.id
                LEFT JOIN users u ON oo.operator_id = u.id
                WHERE 1=1
            """

            params = []

            # 添加过滤条件
            if filters:
                if filters.get('status'):
                    base_sql += " AND oo.status = %s"
                    params.append(filters['status'])

                if filters.get('type'):
                    base_sql += " AND oo.type = %s"
                    params.append(filters['type'])

                if filters.get('start_date'):
                    base_sql += " AND DATE(oo.created_at) >= %s"
                    params.append(filters['start_date'])

                if filters.get('end_date'):
                    base_sql += " AND DATE(oo.created_at) <= %s"
                    params.append(filters['end_date'])

            base_sql += " ORDER BY oo.created_at DESC"

            cursor.execute(base_sql, params)
            results = cursor.fetchall()

            outbound_orders = []
            for row in results:
                outbound_orders.append({
                    'id': row[0],
                    'order_no': row[1],
                    'type': row[2],
                    'department': row[3],
                    'applicant': row[4],
                    'warehouse_name': row[5],
                    'operator_name': row[6],
                    'status': row[7],
                    'created_at': row[8].strftime('%Y-%m-%d %H:%M:%S') if row[8] else '',
                    'approved_at': row[9].strftime('%Y-%m-%d %H:%M:%S') if row[9] else ''
                })

            return outbound_orders

        except Exception as e:
            print(f"获取出库单列表失败: {str(e)}")
            return []
    
    def generate_demand_plan_no(self) -> str:
        """生成需求计划编号"""
        today = datetime.now().strftime("%Y%m%d")
        
        try:
            conn = self.get_connection()
            cursor = conn.cursor()
            
            cursor.execute("""
                SELECT COUNT(*) FROM demand_plans 
                WHERE plan_no LIKE %s
            """, (f"XQ{today}%",))
            
            count = cursor.fetchone()[0] + 1
            return f"XQ{today}{count:04d}"
            
        except Exception:
            return f"XQ{today}0001"

    # 数据分析功能
    def get_inventory_analysis(self, time_range: str = 'month') -> Dict:
        """获取库存分析数据"""
        try:
            conn = self.get_connection()
            cursor = conn.cursor()

            # 获取库存总览
            cursor.execute("""
                SELECT
                    COUNT(DISTINCT p.id) as total_products,
                    SUM(i.quantity) as total_quantity,
                    SUM(i.quantity * p.unit_price) as total_value,
                    COUNT(CASE WHEN i.quantity <= p.min_stock THEN 1 END) as low_stock_count,
                    COUNT(CASE WHEN i.quantity = 0 THEN 1 END) as out_of_stock_count
                FROM inventory i
                JOIN products p ON i.product_id = p.id
                WHERE p.is_active = TRUE
            """)

            overview = cursor.fetchone()

            # 获取分类库存分布
            cursor.execute("""
                SELECT
                    c.name as category_name,
                    COUNT(DISTINCT p.id) as product_count,
                    SUM(i.quantity) as total_quantity,
                    SUM(i.quantity * p.unit_price) as total_value
                FROM inventory i
                JOIN products p ON i.product_id = p.id
                LEFT JOIN categories c ON p.category_id = c.id
                WHERE p.is_active = TRUE
                GROUP BY c.id, c.name
                ORDER BY total_value DESC
            """)

            category_distribution = cursor.fetchall()

            # 获取库存状态分布
            cursor.execute("""
                SELECT
                    CASE
                        WHEN i.quantity = 0 THEN '缺货'
                        WHEN i.quantity <= p.min_stock THEN '低库存'
                        ELSE '正常'
                    END as status,
                    COUNT(*) as count
                FROM inventory i
                JOIN products p ON i.product_id = p.id
                WHERE p.is_active = TRUE
                GROUP BY
                    CASE
                        WHEN i.quantity = 0 THEN '缺货'
                        WHEN i.quantity <= p.min_stock THEN '低库存'
                        ELSE '正常'
                    END
            """)

            status_distribution = cursor.fetchall()

            return {
                'overview': {
                    'total_products': overview[0] or 0,
                    'total_quantity': overview[1] or 0,
                    'total_value': float(overview[2]) if overview[2] else 0,
                    'low_stock_count': overview[3] or 0,
                    'out_of_stock_count': overview[4] or 0
                },
                'category_distribution': [
                    {
                        'category_name': row[0] or '未分类',
                        'product_count': row[1],
                        'total_quantity': row[2],
                        'total_value': float(row[3]) if row[3] else 0
                    }
                    for row in category_distribution
                ],
                'status_distribution': [
                    {
                        'status': row[0],
                        'count': row[1]
                    }
                    for row in status_distribution
                ]
            }

        except Exception as e:
            print(f"获取库存分析数据失败: {str(e)}")
            return {}

    def get_inbound_outbound_statistics(self, time_range: str = 'month') -> Dict:
        """获取出入库统计数据"""
        try:
            conn = self.get_connection()
            cursor = conn.cursor()

            # 计算时间范围
            if time_range == 'week':
                days = 7
            elif time_range == 'month':
                days = 30
            elif time_range == 'quarter':
                days = 90
            elif time_range == 'year':
                days = 365
            else:
                days = 30

            # 获取入库统计
            cursor.execute("""
                SELECT
                    io.type,
                    COUNT(*) as order_count,
                    SUM(io.total_amount) as total_amount
                FROM inbound_orders io
                WHERE io.created_at >= CURRENT_DATE - INTERVAL '%s days'
                GROUP BY io.type
            """, (days,))

            inbound_stats = cursor.fetchall()

            # 获取出库统计
            cursor.execute("""
                SELECT
                    oo.type,
                    COUNT(*) as order_count
                FROM outbound_orders oo
                WHERE oo.created_at >= CURRENT_DATE - INTERVAL '%s days'
                GROUP BY oo.type
            """, (days,))

            outbound_stats = cursor.fetchall()

            # 获取每日出入库趋势
            cursor.execute("""
                SELECT
                    DATE(created_at) as date,
                    'inbound' as type,
                    COUNT(*) as count
                FROM inbound_orders
                WHERE created_at >= CURRENT_DATE - INTERVAL '%s days'
                GROUP BY DATE(created_at)
                UNION ALL
                SELECT
                    DATE(created_at) as date,
                    'outbound' as type,
                    COUNT(*) as count
                FROM outbound_orders
                WHERE created_at >= CURRENT_DATE - INTERVAL '%s days'
                GROUP BY DATE(created_at)
                ORDER BY date
            """, (days, days))

            daily_trends = cursor.fetchall()

            return {
                'inbound_statistics': [
                    {
                        'type': row[0],
                        'order_count': row[1],
                        'total_amount': float(row[2]) if row[2] else 0
                    }
                    for row in inbound_stats
                ],
                'outbound_statistics': [
                    {
                        'type': row[0],
                        'order_count': row[1]
                    }
                    for row in outbound_stats
                ],
                'daily_trends': [
                    {
                        'date': row[0].strftime('%Y-%m-%d'),
                        'type': row[1],
                        'count': row[2]
                    }
                    for row in daily_trends
                ]
            }

        except Exception as e:
            print(f"获取出入库统计数据失败: {str(e)}")
            return {}

    def get_stock_warning_analysis(self) -> Dict:
        """获取库存预警分析"""
        try:
            conn = self.get_connection()
            cursor = conn.cursor()

            # 获取低库存商品
            cursor.execute("""
                SELECT
                    p.code, p.name, p.model, c.name as category_name,
                    i.quantity, p.min_stock, p.unit,
                    (p.min_stock - i.quantity) as shortage
                FROM inventory i
                JOIN products p ON i.product_id = p.id
                LEFT JOIN categories c ON p.category_id = c.id
                WHERE p.is_active = TRUE AND i.quantity <= p.min_stock AND i.quantity > 0
                ORDER BY shortage DESC
            """)

            low_stock_items = cursor.fetchall()

            # 获取缺货商品
            cursor.execute("""
                SELECT
                    p.code, p.name, p.model, c.name as category_name,
                    p.min_stock, p.unit
                FROM inventory i
                JOIN products p ON i.product_id = p.id
                LEFT JOIN categories c ON p.category_id = c.id
                WHERE p.is_active = TRUE AND i.quantity = 0
                ORDER BY p.min_stock DESC
            """)

            out_of_stock_items = cursor.fetchall()

            # 获取超储商品
            cursor.execute("""
                SELECT
                    p.code, p.name, p.model, c.name as category_name,
                    i.quantity, p.max_stock, p.unit,
                    (i.quantity - p.max_stock) as excess
                FROM inventory i
                JOIN products p ON i.product_id = p.id
                LEFT JOIN categories c ON p.category_id = c.id
                WHERE p.is_active = TRUE AND p.max_stock IS NOT NULL
                AND i.quantity > p.max_stock
                ORDER BY excess DESC
            """)

            overstock_items = cursor.fetchall()

            return {
                'low_stock_items': [
                    {
                        'product_code': row[0],
                        'product_name': row[1],
                        'model': row[2],
                        'category_name': row[3] or '未分类',
                        'current_quantity': row[4],
                        'min_stock': row[5],
                        'unit': row[6],
                        'shortage': row[7]
                    }
                    for row in low_stock_items
                ],
                'out_of_stock_items': [
                    {
                        'product_code': row[0],
                        'product_name': row[1],
                        'model': row[2],
                        'category_name': row[3] or '未分类',
                        'min_stock': row[4],
                        'unit': row[5]
                    }
                    for row in out_of_stock_items
                ],
                'overstock_items': [
                    {
                        'product_code': row[0],
                        'product_name': row[1],
                        'model': row[2],
                        'category_name': row[3] or '未分类',
                        'current_quantity': row[4],
                        'max_stock': row[5],
                        'unit': row[6],
                        'excess': row[7]
                    }
                    for row in overstock_items
                ]
            }

        except Exception as e:
            print(f"获取库存预警分析失败: {str(e)}")
            return {}

    def get_low_stock_items(self) -> List[Dict]:
        """获取低库存商品列表"""
        try:
            conn = self.get_connection()
            cursor = conn.cursor()

            cursor.execute("""
                SELECT p.code, p.name, p.model, c.name as category_name,
                       l.location_code, i.quantity, p.min_stock, p.unit
                FROM inventory i
                JOIN products p ON i.product_id = p.id
                JOIN locations l ON i.location_id = l.id
                LEFT JOIN categories c ON p.category_id = c.id
                WHERE p.is_active = TRUE AND i.quantity <= p.min_stock AND i.quantity > 0
                ORDER BY i.quantity ASC
            """)

            results = cursor.fetchall()

            return [
                {
                    'product_code': row[0],
                    'product_name': row[1],
                    'model': row[2],
                    'category_name': row[3] or '未分类',
                    'location_code': row[4],
                    'quantity': row[5],
                    'min_stock': row[6],
                    'unit': row[7],
                    'status': '低库存'
                }
                for row in results
            ]

        except Exception as e:
            print(f"获取低库存商品失败: {str(e)}")
            return []

    def get_out_of_stock_items(self) -> List[Dict]:
        """获取缺货商品列表"""
        try:
            conn = self.get_connection()
            cursor = conn.cursor()

            cursor.execute("""
                SELECT p.code, p.name, p.model, c.name as category_name,
                       l.location_code, p.min_stock, p.unit
                FROM inventory i
                JOIN products p ON i.product_id = p.id
                JOIN locations l ON i.location_id = l.id
                LEFT JOIN categories c ON p.category_id = c.id
                WHERE p.is_active = TRUE AND i.quantity = 0
                ORDER BY p.min_stock DESC
            """)

            results = cursor.fetchall()

            return [
                {
                    'product_code': row[0],
                    'product_name': row[1],
                    'model': row[2],
                    'category_name': row[3] or '未分类',
                    'location_code': row[4],
                    'quantity': 0,
                    'min_stock': row[5],
                    'unit': row[6],
                    'status': '缺货'
                }
                for row in results
            ]

        except Exception as e:
            print(f"获取缺货商品失败: {str(e)}")
            return []
    
    def __del__(self):
        """析构函数，关闭数据库连接"""
        if self.conn:
            self.conn.close() 