#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据备份服务模块
"""

import os
import sys
import subprocess
import json
from datetime import datetime
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

class BackupService:
    """数据备份服务类"""
    
    def __init__(self):
        """初始化备份服务"""
        self.project_root = Path(__file__).parent.parent
        self.backup_dir = self.project_root / "backups"
        self.backup_dir.mkdir(exist_ok=True)
        
        # 数据库连接信息
        self.db_config = {
            "host": "localhost",
            "port": "3306",
            "database": "whzc_db",
            "username": "root",
            "password": "179100215"
        }
        
        # 备份历史文件
        self.backup_history_file = self.backup_dir / "backup_history.json"
        
    def get_backup_filename(self, backup_type="full"):
        """生成备份文件名"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        return f"wms_backup_{backup_type}_{timestamp}.sql"
    
    def full_backup(self):
        """执行完整备份 - 备份所有数据"""
        try:
            backup_filename = self.get_backup_filename("full")
            backup_path = self.backup_dir / backup_filename

            # 构建 mysqldump 命令 - 备份所有数据和结构
            cmd = [
                "mysqldump",
                f"--host={self.db_config['host']}",
                f"--port={self.db_config['port']}",
                f"--user={self.db_config['username']}",
                f"--password={self.db_config['password']}",
                "--single-transaction",
                "--routines",
                "--triggers",
                "--add-drop-database",
                "--create-options",
                "--extended-insert",
                "--set-charset",
                "--default-character-set=utf8mb4",
                f"--result-file={backup_path}",
                self.db_config['database']
            ]
            
            # 执行备份命令
            result = subprocess.run(cmd, capture_output=True, text=True)
            
            if result.returncode == 0:
                # 获取文件大小
                file_size = backup_path.stat().st_size
                
                # 记录备份历史
                self.record_backup_history({
                    "filename": backup_filename,
                    "type": "完整备份",
                    "timestamp": datetime.now().isoformat(),
                    "size": file_size,
                    "status": "成功",
                    "path": str(backup_path)
                })
                
                return {
                    "success": True,
                    "message": f"完整备份成功: {backup_filename}",
                    "filename": backup_filename,
                    "size": file_size
                }
            else:
                return {
                    "success": False,
                    "message": f"备份失败: {result.stderr}",
                    "error": result.stderr
                }
                
        except FileNotFoundError:
            return {
                "success": False,
                "message": "mysqldump 命令未找到，请确保 MySQL 客户端工具已安装",
                "error": "mysqldump not found"
            }
        except Exception as e:
            return {
                "success": False,
                "message": f"备份过程中发生错误: {str(e)}",
                "error": str(e)
            }
    
    def delete_backup(self, backup_filename):
        """删除备份文件"""
        try:
            backup_path = self.backup_dir / backup_filename
            if not backup_path.exists():
                return {
                    "success": False,
                    "message": f"备份文件不存在: {backup_filename}",
                    "error": "File not found"
                }

            # 删除文件
            backup_path.unlink()

            # 记录删除历史
            self.record_backup_history({
                "filename": backup_filename,
                "type": "删除备份",
                "timestamp": datetime.now().isoformat(),
                "size": 0,
                "status": "已删除",
                "path": str(backup_path)
            })

            return {
                "success": True,
                "message": f"备份文件删除成功: {backup_filename}",
                "filename": backup_filename
            }

        except Exception as e:
            return {
                "success": False,
                "message": f"删除备份文件失败: {str(e)}",
                "error": str(e)
            }
    
    def restore_backup(self, backup_filename):
        """恢复备份"""
        try:
            backup_path = self.backup_dir / backup_filename
            
            if not backup_path.exists():
                return {
                    "success": False,
                    "message": f"备份文件不存在: {backup_filename}",
                    "error": "File not found"
                }
            
            # 构建 mysql 恢复命令
            cmd = [
                "mysql",
                f"--host={self.db_config['host']}",
                f"--port={self.db_config['port']}",
                f"--user={self.db_config['username']}",
                f"--password={self.db_config['password']}",
                "--default-character-set=utf8mb4",
                self.db_config['database']
            ]
            
            # 读取备份文件内容并通过管道传输
            with open(backup_path, 'r', encoding='utf-8') as f:
                backup_content = f.read()
            
            # 执行恢复命令
            result = subprocess.run(cmd, input=backup_content, capture_output=True, text=True)
            
            if result.returncode == 0:
                return {
                    "success": True,
                    "message": f"数据恢复成功: {backup_filename}",
                    "filename": backup_filename
                }
            else:
                return {
                    "success": False,
                    "message": f"数据恢复失败: {result.stderr}",
                    "error": result.stderr
                }
                
        except Exception as e:
            return {
                "success": False,
                "message": f"数据恢复过程中发生错误: {str(e)}",
                "error": str(e)
            }
    
    def record_backup_history(self, backup_info):
        """记录备份历史"""
        try:
            # 读取现有历史记录
            history = []
            if self.backup_history_file.exists():
                with open(self.backup_history_file, 'r', encoding='utf-8') as f:
                    history = json.load(f)
            
            # 添加新记录
            history.append(backup_info)
            
            # 保持最近100条记录
            if len(history) > 100:
                history = history[-100:]
            
            # 写入文件
            with open(self.backup_history_file, 'w', encoding='utf-8') as f:
                json.dump(history, f, ensure_ascii=False, indent=2)
                
        except Exception as e:
            print(f"记录备份历史失败: {e}")
    
    def get_backup_history(self):
        """获取备份历史记录"""
        try:
            if not self.backup_history_file.exists():
                return []
            
            with open(self.backup_history_file, 'r', encoding='utf-8') as f:
                history = json.load(f)
            
            # 按时间倒序排列
            return sorted(history, key=lambda x: x.get('timestamp', ''), reverse=True)
            
        except Exception as e:
            print(f"读取备份历史失败: {e}")
            return []
    
    def get_backup_files(self):
        """获取备份文件列表"""
        try:
            backup_files = []
            
            if not self.backup_dir.exists():
                return backup_files
                
            for file_path in self.backup_dir.glob("*.sql"):
                if file_path.is_file():
                    stat = file_path.stat()
                    backup_files.append({
                        "filename": file_path.name,
                        "size": stat.st_size,
                        "size_formatted": self.format_file_size(stat.st_size),
                        "created_time": datetime.fromtimestamp(stat.st_ctime).strftime("%Y-%m-%d %H:%M:%S"),
                        "modified_time": datetime.fromtimestamp(stat.st_mtime).strftime("%Y-%m-%d %H:%M:%S")
                    })
            
            # 按修改时间倒序排列
            return sorted(backup_files, key=lambda x: x['modified_time'], reverse=True)
            
        except Exception as e:
            print(f"获取备份文件列表失败: {e}")
            return []
    
    def format_file_size(self, size_bytes):
        """格式化文件大小"""
        if size_bytes == 0:
            return "0 B"
        
        size_names = ["B", "KB", "MB", "GB", "TB"]
        i = 0
        while size_bytes >= 1024 and i < len(size_names) - 1:
            size_bytes /= 1024.0
            i += 1
        
        return f"{size_bytes:.2f} {size_names[i]}"

# 导出备份服务实例
backup_service = BackupService()

if __name__ == "__main__":
    # 测试备份功能
    print("🗄️ 测试MySQL数据备份")
    print("=" * 40)
    
    result = backup_service.full_backup()
    if result["success"]:
        print(f"✓ {result['message']}")
        print(f"📁 文件大小: {backup_service.format_file_size(result['size'])}")
    else:
        print(f"✗ {result['message']}") 