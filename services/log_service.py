#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
日志服务模块
"""

import os
import sys
import logging
from datetime import datetime
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

class LogService:
    """日志服务类"""
    
    @staticmethod
    def get_logger(name):
        """获取日志记录器"""
        logger = logging.getLogger(name)
        if not logger.handlers:
            # 设置日志级别
            logger.setLevel(logging.INFO)
            
            # 创建控制台处理器
            console_handler = logging.StreamHandler()
            console_handler.setLevel(logging.INFO)
            
            # 创建格式器
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            console_handler.setFormatter(formatter)
            
            # 添加处理器到日志记录器
            logger.addHandler(console_handler)
        
        return logger
    
    def __init__(self, log_dir="logs"):
        """初始化日志服务"""
        self.project_root = Path(__file__).parent.parent
        self.log_dir = self.project_root / log_dir
        self.log_dir.mkdir(exist_ok=True)
        
        # 默认日志文件
        self.default_log_file = self.log_dir / "application.log"
        
    def get_log_content(self, log_file=None, lines=200):
        """获取日志内容"""
        try:
            if log_file is None:
                log_file = self.default_log_file
            else:
                log_file = self.log_dir / log_file
            
            if not log_file.exists():
                return f"日志文件不存在: {log_file}\n\n创建时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"
            
            # 读取最后N行
            with open(log_file, 'r', encoding='utf-8') as f:
                all_lines = f.readlines()
                
            if len(all_lines) <= lines:
                content = ''.join(all_lines)
            else:
                content = ''.join(all_lines[-lines:])
                content = f"... (显示最后 {lines} 行) ...\n\n" + content
            
            return content
            
        except Exception as e:
            return f"读取日志文件失败: {str(e)}\n\n时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"
    
    def write_log(self, message, level="INFO", log_file=None):
        """写入日志"""
        try:
            if log_file is None:
                log_file = self.default_log_file
            else:
                log_file = self.log_dir / log_file
            
            timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            log_entry = f"[{timestamp}] [{level}] {message}\n"
            
            with open(log_file, 'a', encoding='utf-8') as f:
                f.write(log_entry)
                
            return True
            
        except Exception as e:
            print(f"写入日志失败: {e}")
            return False
    
    def get_log_files(self):
        """获取所有日志文件列表"""
        try:
            log_files = []
            for file in self.log_dir.glob("*.log"):
                log_files.append({
                    'name': file.name,
                    'path': str(file),
                    'size': file.stat().st_size,
                    'modified': datetime.fromtimestamp(file.stat().st_mtime)
                })
            
            # 按修改时间排序
            log_files.sort(key=lambda x: x['modified'], reverse=True)
            return log_files
            
        except Exception as e:
            print(f"获取日志文件列表失败: {e}")
            return []

    def info(self, message, log_file=None):
        """写入INFO级别日志"""
        return self.write_log(message, "INFO", log_file)

    def warning(self, message, log_file=None):
        """写入WARNING级别日志"""
        return self.write_log(message, "WARNING", log_file)

    def error(self, message, log_file=None):
        """写入ERROR级别日志"""
        return self.write_log(message, "ERROR", log_file)

    def debug(self, message, log_file=None):
        """写入DEBUG级别日志"""
        return self.write_log(message, "DEBUG", log_file)

    def get_recent_logs(self, count=100, log_file=None):
        """获取最近的日志记录"""
        try:
            if log_file is None:
                log_file = self.default_log_file
            else:
                log_file = self.log_dir / log_file

            if not log_file.exists():
                return []

            logs = []
            with open(log_file, 'r', encoding='utf-8') as f:
                lines = f.readlines()

            # 获取最后count行
            recent_lines = lines[-count:] if len(lines) > count else lines

            for line in recent_lines:
                line = line.strip()
                if line:
                    # 解析日志格式: [时间] [级别] 消息
                    try:
                        if line.startswith('[') and '] [' in line:
                            parts = line.split('] [', 1)
                            if len(parts) == 2:
                                time_part = parts[0][1:]  # 去掉开头的[
                                rest = parts[1]
                                if '] ' in rest:
                                    level_part, message = rest.split('] ', 1)
                                    logs.append({
                                        'time': time_part,
                                        'level': level_part,
                                        'message': message
                                    })
                                else:
                                    logs.append({
                                        'time': time_part,
                                        'level': 'INFO',
                                        'message': rest
                                    })
                            else:
                                logs.append({
                                    'time': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                                    'level': 'INFO',
                                    'message': line
                                })
                        else:
                            logs.append({
                                'time': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                                'level': 'INFO',
                                'message': line
                            })
                    except Exception:
                        logs.append({
                            'time': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                            'level': 'INFO',
                            'message': line
                        })

            return logs

        except Exception as e:
            print(f"获取最近日志失败: {e}")
            return []

    def clear_logs(self, log_file=None):
        """清空日志文件（复数形式，兼容调用）"""
        return self.clear_log(log_file)

    def clear_log(self, log_file=None):
        """清空日志文件"""
        try:
            if log_file is None:
                log_file = self.default_log_file
            else:
                log_file = self.log_dir / log_file

            with open(log_file, 'w', encoding='utf-8') as f:
                f.write(f"日志已清空 - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")

            return True

        except Exception as e:
            print(f"清空日志失败: {e}")
            return False

    def log_password_change(self, username, changed_by_admin=False):
        """记录密码修改日志"""
        try:
            if changed_by_admin:
                message = f"管理员重置用户 {username} 的密码"
            else:
                message = f"用户 {username} 修改了密码"

            return self.write_log(message, "INFO")

        except Exception as e:
            print(f"记录密码修改日志失败: {e}")
            return False

def init_application_log():
    """初始化应用程序日志"""
    log_service = LogService()
    log_service.write_log("应用程序启动", "INFO")
    return log_service

if __name__ == "__main__":
    # 测试日志服务
    log_service = LogService()
    
    # 写入测试日志
    log_service.write_log("系统启动", "INFO")
    log_service.write_log("用户登录: admin", "INFO")
    log_service.write_log("数据库连接成功", "INFO")
    log_service.write_log("测试警告信息", "WARNING")
    log_service.write_log("测试错误信息", "ERROR")
    
    # 读取日志
    content = log_service.get_log_content(lines=10)
    print("日志内容:")
    print(content)
    
    # 获取日志文件列表
    files = log_service.get_log_files()
    print("\n日志文件列表:")
    for file in files:
        print(f"- {file['name']} ({file['size']} bytes, {file['modified']})")