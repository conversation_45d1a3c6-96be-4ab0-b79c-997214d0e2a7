# 重启后启动指南

## 🎯 重启电脑后的完整启动流程

### 方法一：一键启动 (推荐)

**双击运行**: `启动记忆系统.bat`

这个脚本会自动：
1. ✅ 检查并启动Ollama服务
2. ✅ 验证AI模型是否存在
3. ✅ 测试Mem0功能
4. ✅ 提示下一步操作

### 方法二：手动启动

#### 第1步：启动Ollama服务
```bash
ollama serve
```
**重要**: 保持这个窗口运行，不要关闭！

#### 第2步：验证模型
```bash
ollama list
```
确保看到：
- `llama3.1`
- `nomic-embed-text`

如果没有，运行：
```bash
ollama pull llama3.1
ollama pull nomic-embed-text
```

#### 第3步：测试Mem0
```bash
cd E:\WMS\mem0-main
python simple_test.py
```
应该看到：
```
✅ Mem0初始化成功
✅ 记忆已添加
✅ 基本测试完成！
```

#### 第4步：重启Cursor IDE
- 完全关闭Cursor
- 重新打开Cursor
- MCP配置自动生效

#### 第5步：测试记忆功能
在Cursor中对话：
```
用户: "请记住我喜欢Python编程"
AI: "✅ 记忆已添加: 我喜欢Python编程"

用户: "我喜欢什么编程语言？"
AI: "🔍 找到相关记忆: 您喜欢Python编程"
```

## 🛠️ 故障排除

### 问题1：Ollama启动失败
**解决方案**：
```bash
# 检查Ollama是否安装
ollama --version

# 手动启动
ollama serve

# 检查端口占用
netstat -an | findstr :11434
```

### 问题2：模型下载失败
**解决方案**：
```bash
# 重新下载
ollama pull llama3.1
ollama pull nomic-embed-text

# 检查网络连接
ping ollama.ai
```

### 问题3：Mem0测试失败
**解决方案**：
```bash
# 检查Python环境
python --version

# 重新安装依赖
cd E:\WMS\mem0-main
pip install -e .
pip install chromadb

# 重新测试
python simple_test.py
```

### 问题4：Cursor记忆功能不工作
**解决方案**：
```bash
# 重新配置MCP
python setup_cursor_simple.py

# 检查配置文件
type "C:\Users\<USER>\AppData\Roaming\Cursor\User\settings.json"

# 重启Cursor IDE
```

## 📁 重要文件位置

### 启动脚本
- `启动记忆系统.bat` - 一键启动脚本

### 测试脚本
- `simple_test.py` - 基本功能测试
- `crud_demo.py` - 完整CRUD演示

### 配置脚本
- `setup_cursor_simple.py` - Cursor MCP配置

### 数据存储
- `chroma_db/` - 向量数据库
- `local_config_simple.py` - 本地配置

## 🔄 日常使用流程

### 每次开机后
1. **双击**: `启动记忆系统.bat`
2. **等待**: 脚本完成所有检查
3. **重启**: Cursor IDE
4. **开始使用**: 记忆功能立即可用

### 定期维护
```bash
# 每周运行一次完整测试
python crud_demo.py

# 检查数据库大小
dir chroma_db

# 备份记忆数据
xcopy chroma_db backup\chroma_db_%date% /E /I
```

## 💡 使用技巧

### 快速验证系统状态
```bash
# 检查所有服务
curl http://localhost:11434/api/tags
python -c "from mem0 import Memory; print('Mem0 OK')"
```

### 性能优化
```bash
# 限制Ollama内存使用
set OLLAMA_NUM_PARALLEL=1

# 清理临时文件
del /q %temp%\ollama*
```

## 🎯 成功标志

系统正常启动后，您应该看到：

1. ✅ **Ollama服务运行** - `ollama list` 显示模型
2. ✅ **Mem0测试通过** - `simple_test.py` 无错误
3. ✅ **Cursor记忆工作** - AI能记住和回忆信息
4. ✅ **数据持久化** - 重启后记忆仍然存在

## 🚨 紧急恢复

如果系统完全无法工作：

```bash
# 1. 重置Ollama
ollama stop
ollama serve

# 2. 重新安装Mem0
cd E:\WMS\mem0-main
pip uninstall mem0 -y
pip install -e .

# 3. 重新配置Cursor
python setup_cursor_simple.py

# 4. 重启所有服务
```

---

🎉 **按照这个指南，您的记忆系统将在重启后快速恢复正常工作！**
