#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
浏览器监控器 - 检测浏览器关闭并自动清理端口
"""

import time
import psutil
import requests
import threading
import os
import sys
from datetime import datetime

class BrowserMonitor:
    def __init__(self, port=8510, check_interval=5):
        self.port = port
        self.check_interval = check_interval
        self.dashboard_pid = None
        self.monitoring = False
        self.last_access_time = datetime.now()
        
    def is_dashboard_running(self):
        """检查仪表盘是否在运行"""
        try:
            response = requests.get(f"http://localhost:{self.port}", timeout=2)
            return response.status_code == 200
        except:
            return False
    
    def has_browser_connections(self):
        """检查是否有浏览器连接"""
        try:
            # 检查端口连接数
            connections = 0
            for proc in psutil.process_iter(['pid', 'connections']):
                try:
                    if proc.info['connections']:
                        for conn in proc.info['connections']:
                            if hasattr(conn, 'laddr') and conn.laddr.port == self.port:
                                connections += 1
                except (psutil.NoSuchProcess, psutil.AccessDenied):
                    pass
            
            # 如果有连接，更新最后访问时间
            if connections > 1:  # 大于1是因为服务器本身占用一个连接
                self.last_access_time = datetime.now()
                return True
            
            # 检查是否长时间无访问（超过30秒）
            time_since_last_access = (datetime.now() - self.last_access_time).seconds
            return time_since_last_access < 30
            
        except Exception as e:
            print(f"检查连接时出错: {e}")
            return True  # 出错时保守处理，不清理
    
    def find_dashboard_process(self):
        """查找仪表盘进程"""
        for proc in psutil.process_iter(['pid', 'cmdline', 'connections']):
            try:
                cmdline = ' '.join(proc.info['cmdline']) if proc.info['cmdline'] else ''
                
                # 检查是否是仪表盘进程
                if ('streamlit' in cmdline.lower() and 
                    'dashboard_final.py' in cmdline and
                    str(self.port) in cmdline):
                    
                    # 验证进程确实占用了目标端口
                    if proc.info['connections']:
                        for conn in proc.info['connections']:
                            if hasattr(conn, 'laddr') and conn.laddr.port == self.port:
                                return proc.pid
                                
            except (psutil.NoSuchProcess, psutil.AccessDenied):
                pass
        return None
    
    def cleanup_dashboard(self):
        """清理仪表盘进程和端口"""
        print(f"🧹 检测到浏览器已关闭，清理端口 {self.port}...")
        
        # 查找并终止仪表盘进程
        dashboard_pid = self.find_dashboard_process()
        if dashboard_pid:
            try:
                proc = psutil.Process(dashboard_pid)
                proc.terminate()
                print(f"🔄 已终止仪表盘进程 {dashboard_pid}")
                
                # 等待进程终止
                proc.wait(timeout=5)
                print(f"✅ 进程 {dashboard_pid} 已完全终止")
                
            except psutil.TimeoutExpired:
                # 强制终止
                proc.kill()
                print(f"🔥 强制终止进程 {dashboard_pid}")
            except Exception as e:
                print(f"终止进程时出错: {e}")
        
        # 清理端口占用
        self.cleanup_port()
        
        print(f"✅ 端口 {self.port} 清理完成")
        self.monitoring = False
    
    def cleanup_port(self):
        """清理端口占用"""
        for proc in psutil.process_iter(['pid', 'connections']):
            try:
                if proc.info['connections']:
                    for conn in proc.info['connections']:
                        if hasattr(conn, 'laddr') and conn.laddr.port == self.port:
                            proc.terminate()
                            print(f"🔄 清理端口占用进程 {proc.pid}")
                            break
            except (psutil.NoSuchProcess, psutil.AccessDenied):
                pass
    
    def monitor_loop(self):
        """监控循环"""
        print(f"🔍 开始监控端口 {self.port}，检查间隔 {self.check_interval} 秒")
        
        while self.monitoring:
            try:
                # 检查仪表盘是否还在运行
                if not self.is_dashboard_running():
                    print("📱 仪表盘已停止运行")
                    break
                
                # 检查是否有浏览器连接
                if not self.has_browser_connections():
                    print("🌐 检测到浏览器已断开连接")
                    self.cleanup_dashboard()
                    break
                
                time.sleep(self.check_interval)
                
            except KeyboardInterrupt:
                print("\n🛑 监控被用户中断")
                break
            except Exception as e:
                print(f"监控过程中出错: {e}")
                time.sleep(self.check_interval)
        
        print("🔚 监控结束")
    
    def start_monitoring(self):
        """开始监控"""
        if self.monitoring:
            print("⚠️ 监控已在运行中")
            return
        
        # 检查仪表盘是否在运行
        if not self.is_dashboard_running():
            print(f"❌ 端口 {self.port} 上没有运行的仪表盘")
            return
        
        self.monitoring = True
        self.last_access_time = datetime.now()
        
        # 在后台线程中运行监控
        monitor_thread = threading.Thread(target=self.monitor_loop, daemon=True)
        monitor_thread.start()
        
        return monitor_thread
    
    def stop_monitoring(self):
        """停止监控"""
        self.monitoring = False

def main():
    """主函数"""
    print("🖥️ 浏览器监控器启动")
    print("=" * 40)
    
    monitor = BrowserMonitor(port=8510, check_interval=3)
    
    try:
        # 启动监控
        thread = monitor.start_monitoring()
        
        if thread:
            print("✅ 监控已启动，按 Ctrl+C 停止")
            
            # 等待监控线程结束或用户中断
            while thread.is_alive():
                time.sleep(1)
        else:
            print("❌ 监控启动失败")
    
    except KeyboardInterrupt:
        print("\n🛑 用户中断监控")
        monitor.stop_monitoring()
    
    except Exception as e:
        print(f"❌ 监控过程中出错: {e}")
    
    finally:
        print("🔚 监控程序结束")

if __name__ == "__main__":
    main()
