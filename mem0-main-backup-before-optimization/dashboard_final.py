#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Mem0本地仪表盘 完整版 - 集成所有三个功能
包含：文件上传、文档处理、对话上下文记忆
"""

import streamlit as st
import pandas as pd
import plotly.express as px
import plotly.graph_objects as go
from datetime import datetime, timedelta
import sys
from pathlib import Path
import json
import time
import os
import tempfile
import atexit
import signal
import psutil

# 添加项目路径
sys.path.insert(0, str(Path(__file__).parent))

# 导入自定义模块
from document_processor import DocumentProcessor
from conversation_memory import ConversationMemory

# 端口清理函数
def cleanup_on_exit():
    """程序退出时清理端口"""
    try:
        current_pid = os.getpid()
        print(f"🧹 清理进程 {current_pid} 占用的资源...")

        # 查找并终止占用8510端口的进程
        for proc in psutil.process_iter(['pid', 'connections']):
            try:
                if proc.info['connections']:
                    for conn in proc.info['connections']:
                        if hasattr(conn, 'laddr') and conn.laddr.port == 8510:
                            if proc.pid != current_pid:  # 不终止自己
                                proc.terminate()
                                print(f"🔄 终止端口占用进程 {proc.pid}")
            except (psutil.NoSuchProcess, psutil.AccessDenied):
                pass
    except Exception as e:
        print(f"清理过程中出错: {e}")

# 注册退出清理函数
atexit.register(cleanup_on_exit)

# 注意：在Streamlit中不能使用signal处理，因为不在主线程
# 改为使用atexit进行清理

# 页面配置
st.set_page_config(
    page_title="Mem0 Complete Dashboard",
    page_icon="🧠",
    layout="wide",
    initial_sidebar_state="collapsed"
)

# 保持V3的CSS样式
st.markdown("""
<style>
    .stApp {
        background-color: #0a0a0a;
        color: #ffffff;
    }
    
    #MainMenu {visibility: hidden;}
    footer {visibility: hidden;}
    header {visibility: hidden;}
    
    .main-title {
        font-size: 2rem;
        font-weight: 600;
        color: #ffffff;
        margin-bottom: 2rem;
        text-align: center;
    }
    
    .stats-card {
        background: linear-gradient(135deg, #18181b 0%, #27272a 100%);
        border: 1px solid #3f3f46;
        border-radius: 12px;
        padding: 1.5rem;
        margin: 0.5rem 0;
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.3);
        transition: all 0.3s ease;
    }
    
    .stats-card:hover {
        border-color: #6366f1;
        transform: translateY(-2px);
        box-shadow: 0 8px 25px -5px rgba(99, 102, 241, 0.3);
    }
    
    .stats-card-header {
        background: #27272a;
        border-bottom: 1px solid #3f3f46;
        border-radius: 8px 8px 0 0;
        padding: 1rem 1.5rem;
        margin: -1.5rem -1.5rem 1rem -1.5rem;
    }
    
    .stats-card-title {
        color: #ffffff;
        font-size: 1.25rem;
        font-weight: 600;
        margin: 0;
    }
    
    .stats-number {
        font-size: 2rem;
        font-weight: 700;
        color: #6366f1;
        margin: 0.5rem 0;
    }
    
    .stats-label {
        color: #a1a1aa;
        font-size: 0.875rem;
        margin: 0;
    }
    
    .memory-card {
        background: #18181b;
        border: 1px solid #3f3f46;
        border-radius: 8px;
        padding: 1rem;
        margin: 0.5rem 0;
        transition: all 0.2s ease;
    }
    
    .memory-card:hover {
        border-color: #6366f1;
        background: #1f1f23;
    }
    
    .memory-content {
        color: #ffffff;
        font-size: 1rem;
        line-height: 1.5;
        margin-bottom: 0.5rem;
    }
    
    .memory-meta {
        color: #a1a1aa;
        font-size: 0.75rem;
        display: flex;
        justify-content: space-between;
        align-items: center;
    }
    
    .conversation-card {
        background: #1a1a2e;
        border: 1px solid #16213e;
        border-radius: 8px;
        padding: 1rem;
        margin: 0.5rem 0;
    }
    
    .user-message {
        background: #0f3460;
        border-left: 4px solid #6366f1;
    }
    
    .assistant-message {
        background: #16213e;
        border-left: 4px solid #10b981;
    }
    
    .stButton > button {
        background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
        color: white;
        border: none;
        border-radius: 6px;
        padding: 0.5rem 1rem;
        font-weight: 500;
        transition: all 0.2s ease;
    }
    
    .stButton > button:hover {
        background: linear-gradient(135deg, #5855eb 0%, #7c3aed 100%);
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(99, 102, 241, 0.4);
    }
</style>
""", unsafe_allow_html=True)

@st.cache_resource
def load_memory_data():
    """加载记忆数据"""
    try:
        from local_config_simple import get_local_config_simple, setup_local_env
        from mem0 import Memory
        
        setup_local_env()
        config = get_local_config_simple()
        memory = Memory(config=config)
        
        return memory
    except Exception as e:
        st.error(f"无法连接到Mem0: {e}")
        return None

@st.cache_resource
def load_processors(_memory):
    """加载处理器"""
    doc_processor = DocumentProcessor(_memory)
    conv_memory = ConversationMemory(_memory)
    return doc_processor, conv_memory

def get_user_memories(memory, user_id):
    """获取用户记忆"""
    try:
        memories_result = memory.get_all(user_id=user_id)
        if isinstance(memories_result, dict) and 'results' in memories_result:
            # 过滤掉None值和无效记忆
            valid_memories = [mem for mem in memories_result['results']
                            if mem is not None and isinstance(mem, dict) and 'memory' in mem]
            return valid_memories
        elif isinstance(memories_result, list):
            # 过滤掉None值和无效记忆
            valid_memories = [mem for mem in memories_result
                            if mem is not None and isinstance(mem, dict) and 'memory' in mem]
            return valid_memories
        else:
            return []
    except Exception as e:
        st.error(f"获取记忆失败: {e}")
        return []

def search_memories(memory, query, user_id):
    """搜索记忆"""
    try:
        search_result = memory.search(query, user_id=user_id, limit=20)
        if isinstance(search_result, dict) and 'results' in search_result:
            # 过滤掉None值和无效记忆
            valid_results = [mem for mem in search_result['results']
                           if mem is not None and isinstance(mem, dict) and 'memory' in mem]
            return valid_results
        elif isinstance(search_result, list):
            # 过滤掉None值和无效记忆
            valid_results = [mem for mem in search_result
                           if mem is not None and isinstance(mem, dict) and 'memory' in mem]
            return valid_results
        else:
            return []
    except Exception as e:
        st.error(f"搜索失败: {e}")
        return []

def main():
    """主函数"""
    
    # 主标题
    st.markdown('<h1 class="main-title">🧠 Mem0 智能记忆系统 完整版</h1>', unsafe_allow_html=True)
    
    # 版本信息
    st.markdown("""
    <div style="text-align: center; color: #a1a1aa; margin-bottom: 2rem;">
        🎯 完整功能：📁 文件上传 | 📄 文档处理 | 💬 对话记忆 | 🧠 智能分析
    </div>
    """, unsafe_allow_html=True)
    
    # 用户选择
    col1, col2, col3 = st.columns([1, 2, 1])
    with col2:
        user_id = st.text_input("👤 用户ID", value="cursor_user", 
                               placeholder="输入用户ID来管理记忆")
    
    # 加载数据
    memory = load_memory_data()
    if not memory:
        st.error("❌ 无法连接到Mem0系统")
        st.info("请确保Ollama服务正在运行，并且Mem0配置正确")
        return
    
    # 加载处理器
    doc_processor, conv_memory = load_processors(memory)
    
    # 获取记忆数据
    memories = get_user_memories(memory, user_id)
    
    # 使用标签页
    tab1, tab2, tab3, tab4, tab5, tab6, tab7 = st.tabs([
        "📊 概览", 
        "📁 文件上传", 
        "💬 对话记忆",
        "🔍 搜索", 
        "📝 管理", 
        "📈 分析", 
        "⚙️ 设置"
    ])
    
    with tab1:
        show_dashboard(memories, user_id, memory)
    
    with tab2:
        show_file_upload(memory, doc_processor, user_id)
    
    with tab3:
        show_conversation_memory(conv_memory, user_id)
    
    with tab4:
        show_search(memory, user_id)
    
    with tab5:
        show_manage(memory, user_id, memories)
    
    with tab6:
        show_analytics(memories)
    
    with tab7:
        show_settings(memory)

def show_conversation_memory(conv_memory, user_id):
    """显示对话记忆页面"""
    st.markdown("## 💬 智能对话记忆")
    st.markdown("体验智能对话，系统会自动记住重要信息")
    
    # 初始化会话状态
    if 'conversation_history' not in st.session_state:
        st.session_state.conversation_history = []
    
    # 对话界面
    st.markdown("### 🗣️ 智能对话")
    
    # 显示对话历史
    if st.session_state.conversation_history:
        st.markdown("#### 📜 对话历史")
        for i, msg in enumerate(st.session_state.conversation_history[-10:]):  # 显示最近10条
            role_icon = "👤" if msg["role"] == "user" else "🤖"
            role_class = "user-message" if msg["role"] == "user" else "assistant-message"
            
            st.markdown(f"""
            <div class="conversation-card {role_class}">
                <strong>{role_icon} {msg["role"].title()}:</strong><br>
                {msg["content"]}<br>
                <small style="color: #a1a1aa;">{msg["timestamp"]}</small>
            </div>
            """, unsafe_allow_html=True)
    
    # 输入新消息
    with st.form("conversation_form", clear_on_submit=True):
        user_input = st.text_area("💭 输入您的消息", 
                                 placeholder="输入任何内容，系统会智能记住重要信息...",
                                 height=100)
        
        col1, col2, col3 = st.columns([1, 1, 2])
        with col1:
            send_button = st.form_submit_button("📤 发送", type="primary")
        with col2:
            importance = st.selectbox("重要性", [1, 2, 3, 4, 5], index=2)
    
    if send_button and user_input:
        # 添加用户消息
        timestamp = datetime.now().strftime("%H:%M:%S")
        user_msg = {
            "role": "user",
            "content": user_input,
            "timestamp": timestamp
        }
        st.session_state.conversation_history.append(user_msg)
        
        # 添加到对话记忆
        conv_memory.add_message("user", user_input, user_id, importance)
        
        # 生成助手回复（简单模拟）
        assistant_response = generate_assistant_response(user_input, conv_memory, user_id)
        assistant_msg = {
            "role": "assistant", 
            "content": assistant_response,
            "timestamp": datetime.now().strftime("%H:%M:%S")
        }
        st.session_state.conversation_history.append(assistant_msg)
        
        # 添加助手回复到记忆
        conv_memory.add_message("assistant", assistant_response, user_id, 3)
        
        st.rerun()
    
    # 对话管理
    st.markdown("---")
    st.markdown("### 🛠️ 对话管理")
    
    col1, col2, col3 = st.columns(3)
    
    with col1:
        if st.button("📋 获取上下文", use_container_width=True):
            show_conversation_context(conv_memory, user_id)
    
    with col2:
        if st.button("📊 对话总结", use_container_width=True):
            show_conversation_summary(conv_memory, user_id)
    
    with col3:
        if st.button("🗑️ 清除会话", use_container_width=True):
            st.session_state.conversation_history = []
            conv_memory.clear_session()
            st.success("会话已清除")
            st.rerun()

def generate_assistant_response(user_input, conv_memory, user_id):
    """生成助手回复（简单模拟）"""
    # 获取对话上下文
    context = conv_memory.get_conversation_context(user_id, user_input)
    
    # 简单的回复逻辑
    if "你好" in user_input or "hello" in user_input.lower():
        return "你好！我是您的智能记忆助手。我会记住我们对话中的重要信息，帮助您更好地管理知识。"
    
    elif "我是" in user_input or "我叫" in user_input:
        return "很高兴认识您！我已经记住了您的信息，以后的对话中我会记住您的偏好和特点。"
    
    elif "我喜欢" in user_input or "我不喜欢" in user_input:
        return "我已经记住了您的偏好！这些信息会帮助我为您提供更个性化的服务。"
    
    elif "项目" in user_input or "工作" in user_input:
        return "我了解您在讨论工作相关的内容。我会记住这些重要的项目信息，方便您以后查询。"
    
    elif "?" in user_input or "？" in user_input:
        # 尝试从记忆中搜索相关信息
        relevant_memories = context.get("relevant_memories", [])
        if relevant_memories:
            return f"根据我记住的信息，我找到了 {len(relevant_memories)} 条相关记忆。让我为您查找相关内容..."
        else:
            return "我正在思考您的问题。虽然暂时没有找到直接相关的记忆，但我会记住这次对话。"
    
    else:
        return "我理解了您的信息，已经记录下来。如果这是重要信息，我会自动保存到长期记忆中。"

def show_conversation_context(conv_memory, user_id):
    """显示对话上下文"""
    context = conv_memory.get_conversation_context(user_id, "")
    
    with st.expander("📋 对话上下文详情", expanded=True):
        st.write(f"**短期记忆数量**: {len(context['short_term_memory'])}")
        st.write(f"**相关长期记忆**: {len(context['relevant_memories'])}")
        st.write(f"**会话ID**: {context['session_info']['session_id']}")
        
        # 显示用户画像
        if context['user_profile']:
            st.markdown("**用户画像**:")
            profile = context['user_profile']
            if profile['preferences']:
                st.write("- 偏好:", profile['preferences'][:2])
            if profile['personal_info']:
                st.write("- 个人信息:", profile['personal_info'][:2])
            if profile['work_info']:
                st.write("- 工作信息:", profile['work_info'][:2])

def show_conversation_summary(conv_memory, user_id):
    """显示对话总结"""
    summary = conv_memory.summarize_conversation(user_id)
    
    with st.expander("📊 对话总结", expanded=True):
        st.text(summary)

# 从V3复制其他函数
def show_dashboard(memories, user_id, memory):
    """显示仪表盘概览"""
    st.markdown("## 📊 智能记忆概览")
    
    # 统计卡片
    col1, col2, col3, col4 = st.columns(4)
    
    # 确保memories是有效的列表
    if not memories:
        memories = []

    # 计算不同来源的记忆数量
    valid_memories = [mem for mem in memories if mem is not None and isinstance(mem, dict) and 'memory' in mem]

    doc_count = len([mem for mem in valid_memories
                    if (mem.get('metadata') or {}).get('source') == 'document'])

    conv_count = len([mem for mem in valid_memories
                     if (mem.get('metadata') or {}).get('source') == 'conversation'])

    manual_count = len(valid_memories) - doc_count - conv_count
    
    with col1:
        st.markdown(f"""
        <div class="stats-card animate-fade-in">
            <div class="stats-card-header">
                <h3 class="stats-card-title">总记忆数</h3>
            </div>
            <div class="stats-number">{len(valid_memories)}</div>
            <p class="stats-label">已存储记忆</p>
        </div>
        """, unsafe_allow_html=True)
    
    with col2:
        st.markdown(f"""
        <div class="stats-card animate-fade-in">
            <div class="stats-card-header">
                <h3 class="stats-card-title">文档记忆</h3>
            </div>
            <div class="stats-number">{doc_count}</div>
            <p class="stats-label">来自文档</p>
        </div>
        """, unsafe_allow_html=True)
    
    with col3:
        st.markdown(f"""
        <div class="stats-card animate-fade-in">
            <div class="stats-card-header">
                <h3 class="stats-card-title">对话记忆</h3>
            </div>
            <div class="stats-number">{conv_count}</div>
            <p class="stats-label">来自对话</p>
        </div>
        """, unsafe_allow_html=True)
    
    with col4:
        st.markdown(f"""
        <div class="stats-card animate-fade-in">
            <div class="stats-card-header">
                <h3 class="stats-card-title">手动记忆</h3>
            </div>
            <div class="stats-number">{manual_count}</div>
            <p class="stats-label">手动添加</p>
        </div>
        """, unsafe_allow_html=True)
    
    st.markdown("---")
    
    # 最近记忆
    st.markdown("### 📝 最近记忆")
    
    if valid_memories:
        for i, mem in enumerate(valid_memories[:5]):
            if isinstance(mem, dict) and 'memory' in mem:
                memory_text = mem['memory']
                memory_id = mem.get('id', 'unknown')
                created_at = mem.get('created_at', 'unknown')
                metadata = mem.get('metadata') or {}
                source = metadata.get('source', 'manual')
                
                # 根据来源显示不同图标
                if source == "document":
                    source_icon = "📄"
                elif source == "conversation":
                    source_icon = "💬"
                else:
                    source_icon = "💭"
                
                # 截断长文本
                display_text = memory_text[:100] + "..." if len(memory_text) > 100 else memory_text
                
                st.markdown(f"""
                <div class="memory-card animate-fade-in">
                    <div class="memory-content">{source_icon} {display_text}</div>
                    <div class="memory-meta">
                        <span>ID: {memory_id[:8]}... | 来源: {source}</span>
                        <span>{created_at}</span>
                    </div>
                </div>
                """, unsafe_allow_html=True)
    else:
        st.info("暂无记忆数据。开始添加您的第一条记忆、上传文档或进行对话吧！")

# 从V3复制文件上传、搜索、管理、分析、设置功能
def show_file_upload(memory, doc_processor, user_id):
    """显示文件上传页面"""
    st.markdown("## 📁 智能文件上传")
    st.markdown("上传文档，自动提取关键信息并存储为记忆")

    uploaded_files = st.file_uploader(
        "选择文件或拖拽到此处",
        accept_multiple_files=True,
        type=['pdf', 'docx', 'doc', 'txt', 'xlsx', 'xls', 'jpg', 'png', 'jpeg'],
        help="支持的格式：PDF, Word, Excel, 文本文件, 图片"
    )

    if uploaded_files:
        st.markdown("### 📋 上传的文件")

        for uploaded_file in uploaded_files:
            st.markdown(f"""
            <div style="background: #18181b; border: 1px solid #3f3f46; border-radius: 8px; padding: 1rem; margin: 0.5rem 0;">
                <div style="color: #ffffff; font-weight: 600; margin-bottom: 0.5rem;">📄 {uploaded_file.name}</div>
                <div style="color: #a1a1aa; font-size: 0.875rem;">
                    大小: {uploaded_file.size / 1024:.1f} KB | 类型: {uploaded_file.type or '未知'}
                </div>
            </div>
            """, unsafe_allow_html=True)

            col1, col2 = st.columns([1, 3])
            with col1:
                if st.button(f"🔄 处理", key=f"process_{uploaded_file.name}"):
                    with tempfile.NamedTemporaryFile(delete=False, suffix=f"_{uploaded_file.name}") as tmp_file:
                        tmp_file.write(uploaded_file.getbuffer())
                        temp_path = tmp_file.name

                    try:
                        progress_bar = st.progress(0)
                        status_text = st.empty()

                        status_text.text("🔍 正在分析文档...")
                        progress_bar.progress(25)

                        result = doc_processor.process_document(temp_path, user_id, uploaded_file.name)

                        progress_bar.progress(75)
                        status_text.text("💾 正在存储记忆...")

                        progress_bar.progress(100)
                        status_text.text("✅ 处理完成！")

                        st.success(result)

                    except Exception as e:
                        st.error(f"处理文件时出错: {e}")
                    finally:
                        if os.path.exists(temp_path):
                            os.unlink(temp_path)

def show_search(memory, user_id):
    """显示搜索页面"""
    st.markdown("## 🔍 智能搜索")

    col1, col2 = st.columns([4, 1])
    with col1:
        query = st.text_input("", placeholder="使用语义理解搜索记忆内容...", key="search_input")
    with col2:
        search_button = st.button("🔍 搜索", type="primary", use_container_width=True)

    with st.expander("🔧 高级搜索选项"):
        col1, col2 = st.columns(2)
        with col1:
            source_filter = st.selectbox("来源过滤", ["全部", "手动添加", "文档提取", "对话记忆"])
        with col2:
            importance_filter = st.selectbox("重要性过滤", ["全部", "高(4-5)", "中(2-3)", "低(1)"])

    if query and search_button:
        with st.spinner("正在搜索记忆..."):
            results = search_memories(memory, query, user_id)

        # 应用过滤器
        if source_filter != "全部":
            source_map = {"手动添加": "manual", "文档提取": "document", "对话记忆": "conversation"}
            target_source = source_map.get(source_filter)
            results = [r for r in results if (r.get('metadata') or {}).get('source') == target_source]

        if results:
            st.success(f"找到 {len(results)} 条相关记忆")

            for i, mem in enumerate(results):
                if isinstance(mem, dict) and 'memory' in mem:
                    memory_text = mem['memory']
                    memory_id = mem.get('id', 'unknown')
                    metadata = mem.get('metadata') or {}
                    source = metadata.get('source', 'manual')

                    if source == "document":
                        source_icon = "📄"
                    elif source == "conversation":
                        source_icon = "💬"
                    else:
                        source_icon = "💭"

                    highlighted_text = memory_text
                    if query.lower() in memory_text.lower():
                        highlighted_text = memory_text.replace(query, f"**{query}**")

                    with st.expander(f"{source_icon} 结果 {i+1}: {memory_text[:60]}...", expanded=False):
                        st.markdown(f"**内容**: {highlighted_text}")
                        st.markdown(f"**记忆ID**: `{memory_id}`")
                        st.markdown(f"**来源**: {source}")
                        if metadata.get('file_name'):
                            st.markdown(f"**文件**: {metadata['file_name']}")
                        if metadata.get('importance'):
                            st.markdown(f"**重要性**: {metadata['importance']}")

                        col1, col2 = st.columns(2)
                        with col1:
                            if st.button(f"📋 复制", key=f"copy_search_{i}"):
                                st.success("内容已复制！")
                        with col2:
                            if st.button(f"🗑️ 删除", key=f"delete_search_{i}"):
                                try:
                                    memory.delete(memory_id)
                                    st.success("记忆已删除！")
                                    st.rerun()
                                except Exception as e:
                                    st.error(f"删除失败: {e}")
        else:
            st.warning("未找到匹配的记忆内容。")

def show_manage(memory, user_id, memories):
    """显示记忆管理页面"""
    st.markdown("## 📝 记忆管理")

    with st.expander("➕ 添加新记忆", expanded=True):
        with st.form("add_memory_form", clear_on_submit=True):
            new_memory = st.text_area("记忆内容",
                                    placeholder="输入您想要记住的内容...",
                                    height=100)

            importance = st.selectbox("重要性等级", [1, 2, 3, 4, 5], index=2)

            submitted = st.form_submit_button("💾 添加记忆", type="primary", use_container_width=True)

            if submitted and new_memory:
                try:
                    with st.spinner("正在添加记忆..."):
                        result = memory.add(
                            new_memory,
                            user_id=user_id,
                            metadata={
                                "source": "manual",
                                "importance": importance,
                                "created_at": datetime.now().isoformat()
                            }
                        )
                    st.success("✅ 记忆添加成功！")
                    time.sleep(1)
                    st.rerun()
                except Exception as e:
                    st.error(f"添加记忆失败: {e}")

    st.markdown("---")
    st.markdown("### 📋 所有记忆")

    if memories:
        page_size = 5
        total_pages = (len(memories) + page_size - 1) // page_size

        col1, col2, col3 = st.columns([1, 2, 1])
        with col2:
            page_num = st.selectbox("页码", range(1, total_pages + 1), key="page_selector")

        start_idx = (page_num - 1) * page_size
        end_idx = start_idx + page_size
        page_memories = memories[start_idx:end_idx]

        for i, mem in enumerate(page_memories):
            if isinstance(mem, dict) and 'memory' in mem:
                memory_text = mem['memory']
                memory_id = mem.get('id', 'unknown')
                created_at = mem.get('created_at', 'unknown')
                metadata = mem.get('metadata') or {}
                source = metadata.get('source', 'manual')

                if source == "document":
                    source_icon = "📄"
                elif source == "conversation":
                    source_icon = "💬"
                else:
                    source_icon = "💭"

                st.markdown(f"""
                <div class="memory-card">
                    <div class="memory-content">{source_icon} {memory_text}</div>
                    <div class="memory-meta">
                        <span>ID: {memory_id[:8]}... | 来源: {source}</span>
                        <span>{created_at}</span>
                    </div>
                </div>
                """, unsafe_allow_html=True)

                col1, col2, col3, col4 = st.columns([1, 1, 1, 3])
                with col1:
                    if st.button("📋", key=f"copy_manage_{start_idx + i}", help="复制"):
                        st.success("已复制！")
                with col2:
                    if st.button("✏️", key=f"edit_manage_{start_idx + i}", help="编辑"):
                        st.info("编辑功能即将推出！")
                with col3:
                    if st.button("🗑️", key=f"delete_manage_{start_idx + i}", help="删除"):
                        try:
                            memory.delete(memory_id)
                            st.success("记忆已删除！")
                            st.rerun()
                        except Exception as e:
                            st.error(f"删除失败: {e}")

                st.markdown("---")
    else:
        st.info("暂无记忆数据。请添加记忆、上传文档或进行对话！")

def show_analytics(memories):
    """显示统计分析页面"""
    st.markdown("## 📈 智能记忆分析")

    if not memories:
        st.info("暂无数据可供分析。请先添加一些记忆！")
        return

    # 来源分析
    st.markdown("### 📊 记忆来源分析")

    source_counts = {"手动添加": 0, "文档提取": 0, "对话记忆": 0}
    for mem in memories:
        if isinstance(mem, dict):
            metadata = mem.get('metadata') or {}
            source = metadata.get('source', 'manual')
            if source == 'document':
                source_counts["文档提取"] += 1
            elif source == 'conversation':
                source_counts["对话记忆"] += 1
            else:
                source_counts["手动添加"] += 1

    fig_pie = px.pie(
        values=list(source_counts.values()),
        names=list(source_counts.keys()),
        title="记忆来源分布",
        color_discrete_sequence=['#6366f1', '#8b5cf6', '#10b981']
    )
    fig_pie.update_layout(
        plot_bgcolor='rgba(0,0,0,0)',
        paper_bgcolor='rgba(0,0,0,0)',
        font_color='white',
        title_font_color='white'
    )
    st.plotly_chart(fig_pie, use_container_width=True)

    # 记忆长度分布
    st.markdown("### 📏 记忆长度分布")
    lengths = [len(mem.get('memory', '')) for mem in memories if isinstance(mem, dict)]

    if lengths:
        fig = px.histogram(x=lengths, nbins=20,
                          title="记忆长度分布图",
                          color_discrete_sequence=['#6366f1'])
        fig.update_layout(
            plot_bgcolor='rgba(0,0,0,0)',
            paper_bgcolor='rgba(0,0,0,0)',
            font_color='white',
            title_font_color='white',
            xaxis_title="字符数",
            yaxis_title="记忆数量"
        )
        st.plotly_chart(fig, use_container_width=True)

    # 详细统计
    st.markdown("### 📊 详细统计")

    # 分别显示数值和文本统计，避免PyArrow类型转换错误
    col1, col2 = st.columns(2)

    with col1:
        st.markdown("**📊 数量统计**")
        numeric_stats = {
            "指标": ["总记忆数", "文档记忆", "对话记忆", "手动记忆"],
            "数量": [
                len(memories),
                source_counts["文档提取"],
                source_counts["对话记忆"],
                source_counts["手动添加"]
            ]
        }
        df_numeric = pd.DataFrame(numeric_stats)
        st.dataframe(df_numeric, use_container_width=True, hide_index=True)

    with col2:
        st.markdown("**📏 长度统计**")
        if lengths:
            avg_len = sum(lengths) // len(lengths)
            min_len = min(lengths)
            max_len = max(lengths)
        else:
            avg_len = min_len = max_len = 0

        length_stats = {
            "指标": ["平均长度", "最短记忆", "最长记忆"],
            "字符数": [avg_len, min_len, max_len]
        }
        df_length = pd.DataFrame(length_stats)
        st.dataframe(df_length, use_container_width=True, hide_index=True)

def show_settings(memory):
    """显示系统设置页面"""
    st.markdown("## ⚙️ 系统设置")

    col1, col2 = st.columns(2)

    with col1:
        st.markdown("""
        <div class="stats-card">
            <div class="stats-card-header">
                <h3 class="stats-card-title">🔧 系统状态</h3>
            </div>
            <div style="padding: 1rem 0;">
                <p style="color: #10b981; margin: 0.5rem 0;">✅ Mem0核心: 在线</p>
                <p style="color: #10b981; margin: 0.5rem 0;">✅ 向量数据库: 已连接</p>
                <p style="color: #10b981; margin: 0.5rem 0;">✅ 本地LLM: 活跃</p>
                <p style="color: #10b981; margin: 0.5rem 0;">✅ 文档处理: 就绪</p>
                <p style="color: #10b981; margin: 0.5rem 0;">✅ 对话记忆: 就绪</p>
            </div>
        </div>
        """, unsafe_allow_html=True)

    with col2:
        st.markdown("""
        <div class="stats-card">
            <div class="stats-card-header">
                <h3 class="stats-card-title">📊 系统配置</h3>
            </div>
            <div style="padding: 1rem 0;">
                <p style="color: #a1a1aa; margin: 0.5rem 0;"><strong>LLM提供商:</strong> Ollama</p>
                <p style="color: #a1a1aa; margin: 0.5rem 0;"><strong>模型:</strong> llama3.1</p>
                <p style="color: #a1a1aa; margin: 0.5rem 0;"><strong>向量数据库:</strong> ChromaDB</p>
                <p style="color: #a1a1aa; margin: 0.5rem 0;"><strong>存储方式:</strong> 本地</p>
                <p style="color: #a1a1aa; margin: 0.5rem 0;"><strong>版本:</strong> 完整版</p>
            </div>
        </div>
        """, unsafe_allow_html=True)

    st.markdown("---")
    st.markdown("### 🗂️ 数据管理")

    col1, col2, col3 = st.columns(3)

    with col1:
        if st.button("📥 导出数据", type="secondary", use_container_width=True):
            st.info("导出功能即将推出！")

    with col2:
        if st.button("🔄 刷新缓存", type="secondary", use_container_width=True):
            st.cache_resource.clear()
            st.success("缓存已清理！")

    with col3:
        if st.button("🧹 数据清理", type="secondary", use_container_width=True):
            st.warning("清理功能即将推出！")

if __name__ == "__main__":
    main()
