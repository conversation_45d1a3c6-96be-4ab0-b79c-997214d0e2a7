#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
性能优化 - 异步任务管理器
实现后台异步处理，提升用户体验和系统响应速度
"""

import asyncio
import threading
import time
import uuid
from concurrent.futures import ThreadPoolExecutor, Future
from typing import Dict, Any, Callable, Optional, List
from dataclasses import dataclass
from enum import Enum
import logging
from datetime import datetime, timedelta

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class TaskStatus(Enum):
    """任务状态枚举"""
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"

@dataclass
class TaskResult:
    """任务结果"""
    task_id: str
    status: TaskStatus
    result: Any = None
    error: Optional[str] = None
    start_time: Optional[datetime] = None
    end_time: Optional[datetime] = None
    progress: float = 0.0

class ProgressCallback:
    """进度回调管理器"""
    
    def __init__(self, task_id: str, task_manager):
        self.task_id = task_id
        self.task_manager = task_manager
    
    def update_progress(self, progress: float, message: str = ""):
        """更新任务进度"""
        self.task_manager.update_task_progress(self.task_id, progress, message)

class AsyncTaskManager:
    """异步任务管理器"""
    
    def __init__(self, max_workers: int = 4):
        """
        初始化异步任务管理器
        
        Args:
            max_workers: 最大工作线程数
        """
        self.max_workers = max_workers
        self.executor = ThreadPoolExecutor(max_workers=max_workers)
        self.tasks: Dict[str, TaskResult] = {}
        self.futures: Dict[str, Future] = {}
        self.progress_callbacks: Dict[str, List[Callable]] = {}
        self.lock = threading.RLock()
        
        logger.info(f"异步任务管理器初始化完成，最大工作线程数: {max_workers}")
    
    def submit_task(self, func: Callable, *args, task_id: str = None, 
                   progress_callback: Callable = None, **kwargs) -> str:
        """
        提交异步任务
        
        Args:
            func: 要执行的函数
            args: 函数参数
            task_id: 任务ID（可选）
            progress_callback: 进度回调函数
            kwargs: 函数关键字参数
            
        Returns:
            str: 任务ID
        """
        if task_id is None:
            task_id = str(uuid.uuid4())
        
        with self.lock:
            # 创建任务结果对象
            task_result = TaskResult(
                task_id=task_id,
                status=TaskStatus.PENDING,
                start_time=datetime.now()
            )
            self.tasks[task_id] = task_result
            
            # 注册进度回调
            if progress_callback:
                if task_id not in self.progress_callbacks:
                    self.progress_callbacks[task_id] = []
                self.progress_callbacks[task_id].append(progress_callback)
            
            # 创建进度回调对象
            progress_cb = ProgressCallback(task_id, self)
            
            # 提交任务到线程池
            future = self.executor.submit(self._execute_task, task_id, func, progress_cb, *args, **kwargs)
            self.futures[task_id] = future
            
            logger.info(f"任务已提交: {task_id}, 函数: {func.__name__}")
            return task_id
    
    def _execute_task(self, task_id: str, func: Callable, progress_cb: ProgressCallback, *args, **kwargs):
        """执行任务的内部方法"""
        try:
            with self.lock:
                if task_id in self.tasks:
                    self.tasks[task_id].status = TaskStatus.RUNNING
                    self.tasks[task_id].start_time = datetime.now()
            
            logger.info(f"开始执行任务: {task_id}")
            
            # 执行函数，传入进度回调
            if 'progress_callback' in func.__code__.co_varnames:
                result = func(*args, progress_callback=progress_cb, **kwargs)
            else:
                result = func(*args, **kwargs)
            
            # 任务完成
            with self.lock:
                if task_id in self.tasks:
                    self.tasks[task_id].status = TaskStatus.COMPLETED
                    self.tasks[task_id].result = result
                    self.tasks[task_id].end_time = datetime.now()
                    self.tasks[task_id].progress = 100.0
            
            logger.info(f"任务执行完成: {task_id}")
            self._notify_progress(task_id, 100.0, "任务完成")
            
            return result
            
        except Exception as e:
            logger.error(f"任务执行失败: {task_id}, 错误: {str(e)}")
            
            with self.lock:
                if task_id in self.tasks:
                    self.tasks[task_id].status = TaskStatus.FAILED
                    self.tasks[task_id].error = str(e)
                    self.tasks[task_id].end_time = datetime.now()
            
            self._notify_progress(task_id, 0.0, f"任务失败: {str(e)}")
            raise
    
    def get_task_status(self, task_id: str) -> Optional[TaskResult]:
        """获取任务状态"""
        with self.lock:
            return self.tasks.get(task_id)
    
    def get_task_result(self, task_id: str, timeout: float = None) -> Any:
        """
        获取任务结果（阻塞等待）
        
        Args:
            task_id: 任务ID
            timeout: 超时时间（秒）
            
        Returns:
            任务结果
        """
        if task_id not in self.futures:
            raise ValueError(f"任务不存在: {task_id}")
        
        future = self.futures[task_id]
        try:
            result = future.result(timeout=timeout)
            return result
        except Exception as e:
            logger.error(f"获取任务结果失败: {task_id}, 错误: {str(e)}")
            raise
    
    def cancel_task(self, task_id: str) -> bool:
        """取消任务"""
        if task_id not in self.futures:
            return False
        
        future = self.futures[task_id]
        cancelled = future.cancel()
        
        if cancelled:
            with self.lock:
                if task_id in self.tasks:
                    self.tasks[task_id].status = TaskStatus.CANCELLED
                    self.tasks[task_id].end_time = datetime.now()
            
            logger.info(f"任务已取消: {task_id}")
            self._notify_progress(task_id, 0.0, "任务已取消")
        
        return cancelled
    
    def update_task_progress(self, task_id: str, progress: float, message: str = ""):
        """更新任务进度"""
        with self.lock:
            if task_id in self.tasks:
                self.tasks[task_id].progress = progress
        
        self._notify_progress(task_id, progress, message)
    
    def _notify_progress(self, task_id: str, progress: float, message: str):
        """通知进度回调"""
        if task_id in self.progress_callbacks:
            for callback in self.progress_callbacks[task_id]:
                try:
                    callback(task_id, progress, message)
                except Exception as e:
                    logger.error(f"进度回调执行失败: {str(e)}")
    
    def get_all_tasks(self) -> Dict[str, TaskResult]:
        """获取所有任务状态"""
        with self.lock:
            return self.tasks.copy()
    
    def cleanup_completed_tasks(self, max_age_hours: int = 24):
        """清理已完成的旧任务"""
        cutoff_time = datetime.now() - timedelta(hours=max_age_hours)
        
        with self.lock:
            tasks_to_remove = []
            for task_id, task_result in self.tasks.items():
                if (task_result.status in [TaskStatus.COMPLETED, TaskStatus.FAILED, TaskStatus.CANCELLED] and
                    task_result.end_time and task_result.end_time < cutoff_time):
                    tasks_to_remove.append(task_id)
            
            for task_id in tasks_to_remove:
                del self.tasks[task_id]
                if task_id in self.futures:
                    del self.futures[task_id]
                if task_id in self.progress_callbacks:
                    del self.progress_callbacks[task_id]
            
            if tasks_to_remove:
                logger.info(f"清理了 {len(tasks_to_remove)} 个旧任务")
    
    def get_stats(self) -> Dict[str, Any]:
        """获取任务管理器统计信息"""
        with self.lock:
            stats = {
                'total_tasks': len(self.tasks),
                'pending_tasks': 0,
                'running_tasks': 0,
                'completed_tasks': 0,
                'failed_tasks': 0,
                'cancelled_tasks': 0,
                'max_workers': self.max_workers
            }
            
            for task_result in self.tasks.values():
                if task_result.status == TaskStatus.PENDING:
                    stats['pending_tasks'] += 1
                elif task_result.status == TaskStatus.RUNNING:
                    stats['running_tasks'] += 1
                elif task_result.status == TaskStatus.COMPLETED:
                    stats['completed_tasks'] += 1
                elif task_result.status == TaskStatus.FAILED:
                    stats['failed_tasks'] += 1
                elif task_result.status == TaskStatus.CANCELLED:
                    stats['cancelled_tasks'] += 1
            
            return stats
    
    def shutdown(self, wait: bool = True):
        """关闭任务管理器"""
        logger.info("正在关闭异步任务管理器...")
        self.executor.shutdown(wait=wait)

# 全局任务管理器实例
_global_task_manager = None

def get_task_manager() -> AsyncTaskManager:
    """获取全局任务管理器"""
    global _global_task_manager
    if _global_task_manager is None:
        _global_task_manager = AsyncTaskManager(max_workers=4)
    return _global_task_manager

if __name__ == "__main__":
    # 测试异步任务系统
    print("🧪 测试异步任务系统")
    
    def sample_task(duration: int, progress_callback: ProgressCallback = None):
        """示例任务"""
        for i in range(duration):
            time.sleep(1)
            if progress_callback:
                progress = (i + 1) / duration * 100
                progress_callback.update_progress(progress, f"处理中 {i+1}/{duration}")
        return f"任务完成，耗时 {duration} 秒"
    
    def progress_handler(task_id: str, progress: float, message: str):
        """进度处理器"""
        print(f"任务 {task_id}: {progress:.1f}% - {message}")
    
    task_manager = get_task_manager()
    
    # 提交测试任务
    task_id = task_manager.submit_task(
        sample_task, 5, 
        progress_callback=progress_handler
    )
    
    print(f"任务已提交: {task_id}")
    
    # 监控任务状态
    while True:
        status = task_manager.get_task_status(task_id)
        if status and status.status in [TaskStatus.COMPLETED, TaskStatus.FAILED]:
            break
        time.sleep(0.5)
    
    # 获取结果
    try:
        result = task_manager.get_task_result(task_id)
        print(f"任务结果: {result}")
    except Exception as e:
        print(f"任务失败: {e}")
    
    # 显示统计信息
    stats = task_manager.get_stats()
    print(f"任务统计: {stats}")
