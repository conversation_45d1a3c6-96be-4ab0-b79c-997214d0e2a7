#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
智能向量缓存系统
实现高效的向量存储、检索和相似度搜索
"""

import numpy as np
import hashlib
import time
import threading
from typing import List, Dict, Any, Optional, Tuple
from datetime import datetime, timedelta
import logging

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class VectorIndex:
    """向量索引管理器"""
    
    def __init__(self, dimension: int = 384, use_faiss: bool = True):
        """
        初始化向量索引
        
        Args:
            dimension: 向量维度
            use_faiss: 是否使用FAISS加速
        """
        self.dimension = dimension
        self.use_faiss = use_faiss
        self.index = None
        self.id_to_text = {}  # ID到文本的映射
        self.text_to_id = {}  # 文本到ID的映射
        self.vectors = {}     # ID到向量的映射
        self.next_id = 0
        self.lock = threading.RLock()
        
        # 统计信息
        self.stats = {
            'total_vectors': 0,
            'search_count': 0,
            'total_search_time': 0.0,
            'avg_search_time': 0.0,
            'cache_hits': 0
        }
        
        self._initialize_index()
    
    def _initialize_index(self):
        """初始化索引"""
        if self.use_faiss:
            try:
                import faiss
                # 尝试使用GPU索引
                try:
                    if faiss.get_num_gpus() > 0:
                        self.index = faiss.IndexFlatIP(self.dimension)
                        self.index = faiss.index_cpu_to_gpu(faiss.StandardGpuResources(), 0, self.index)
                        logger.info("✅ 使用GPU FAISS索引")
                    else:
                        self.index = faiss.IndexFlatIP(self.dimension)
                        logger.info("✅ 使用CPU FAISS索引")
                except:
                    self.index = faiss.IndexFlatIP(self.dimension)
                    logger.info("✅ 使用CPU FAISS索引")
                    
            except ImportError:
                logger.warning("⚠️ FAISS未安装，使用numpy实现")
                self.use_faiss = False
                self.index = None
        else:
            logger.info("✅ 使用numpy向量搜索")
            self.index = None
    
    def add_vector(self, text: str, vector: np.ndarray) -> int:
        """
        添加向量到索引
        
        Args:
            text: 文本内容
            vector: 向量表示
            
        Returns:
            int: 向量ID
        """
        with self.lock:
            # 检查是否已存在
            text_hash = hashlib.md5(text.encode()).hexdigest()
            if text_hash in self.text_to_id:
                return self.text_to_id[text_hash]
            
            # 分配新ID
            vector_id = self.next_id
            self.next_id += 1
            
            # 存储映射
            self.id_to_text[vector_id] = text
            self.text_to_id[text_hash] = vector_id
            self.vectors[vector_id] = vector
            
            # 添加到索引
            if self.use_faiss and self.index is not None:
                # 确保向量是正确的形状和类型
                vector_normalized = vector / np.linalg.norm(vector)
                self.index.add(vector_normalized.reshape(1, -1).astype(np.float32))
            
            self.stats['total_vectors'] += 1
            
            logger.debug(f"向量已添加: ID={vector_id}, 文本={text[:50]}...")
            return vector_id
    
    def search_similar(self, query_vector: np.ndarray, top_k: int = 10) -> List[Tuple[int, float]]:
        """
        搜索相似向量
        
        Args:
            query_vector: 查询向量
            top_k: 返回前k个结果
            
        Returns:
            List[Tuple[int, float]]: (向量ID, 相似度分数) 列表
        """
        start_time = time.time()
        
        with self.lock:
            if self.stats['total_vectors'] == 0:
                return []
            
            try:
                if self.use_faiss and self.index is not None:
                    # 使用FAISS搜索
                    query_normalized = query_vector / np.linalg.norm(query_vector)
                    scores, indices = self.index.search(
                        query_normalized.reshape(1, -1).astype(np.float32), 
                        min(top_k, self.stats['total_vectors'])
                    )
                    
                    results = []
                    for i, (score, idx) in enumerate(zip(scores[0], indices[0])):
                        if idx != -1:  # FAISS返回-1表示无效结果
                            results.append((idx, float(score)))
                    
                else:
                    # 使用numpy计算相似度
                    similarities = []
                    for vector_id, vector in self.vectors.items():
                        # 计算余弦相似度
                        similarity = np.dot(query_vector, vector) / (
                            np.linalg.norm(query_vector) * np.linalg.norm(vector)
                        )
                        similarities.append((vector_id, similarity))
                    
                    # 排序并返回top_k
                    similarities.sort(key=lambda x: x[1], reverse=True)
                    results = similarities[:top_k]
                
                # 更新统计
                search_time = time.time() - start_time
                self.stats['search_count'] += 1
                self.stats['total_search_time'] += search_time
                self.stats['avg_search_time'] = (
                    self.stats['total_search_time'] / self.stats['search_count']
                )
                
                logger.debug(f"相似度搜索完成: {len(results)}个结果, 耗时: {search_time:.3f}秒")
                return results
                
            except Exception as e:
                logger.error(f"相似度搜索失败: {e}")
                return []
    
    def get_text_by_id(self, vector_id: int) -> Optional[str]:
        """根据ID获取文本"""
        with self.lock:
            return self.id_to_text.get(vector_id)
    
    def get_vector_by_id(self, vector_id: int) -> Optional[np.ndarray]:
        """根据ID获取向量"""
        with self.lock:
            return self.vectors.get(vector_id)
    
    def get_stats(self) -> Dict[str, Any]:
        """获取统计信息"""
        with self.lock:
            return {
                **self.stats,
                'dimension': self.dimension,
                'use_faiss': self.use_faiss,
                'index_type': type(self.index).__name__ if self.index else 'numpy'
            }

class IntelligentVectorCache:
    """智能向量缓存系统"""
    
    def __init__(self, max_cache_size: int = 10000, similarity_threshold: float = 0.95):
        """
        初始化智能向量缓存
        
        Args:
            max_cache_size: 最大缓存大小
            similarity_threshold: 相似度阈值
        """
        self.max_cache_size = max_cache_size
        self.similarity_threshold = similarity_threshold
        self.vector_index = VectorIndex()
        
        # 频率统计
        self.access_frequency = {}
        self.last_access_time = {}
        
        # 预计算的常用向量
        self.frequent_vectors = {}
        
        self.lock = threading.RLock()
        
        logger.info(f"智能向量缓存初始化完成，最大容量: {max_cache_size}")
    
    def add_or_update_vector(self, text: str, vector: np.ndarray) -> int:
        """
        添加或更新向量
        
        Args:
            text: 文本内容
            vector: 向量表示
            
        Returns:
            int: 向量ID
        """
        with self.lock:
            # 检查缓存大小
            if self.vector_index.stats['total_vectors'] >= self.max_cache_size:
                self._evict_least_frequent()
            
            # 添加向量
            vector_id = self.vector_index.add_vector(text, vector)
            
            # 更新访问统计
            self.access_frequency[vector_id] = self.access_frequency.get(vector_id, 0) + 1
            self.last_access_time[vector_id] = time.time()
            
            return vector_id
    
    def search_similar_texts(self, query_text: str, query_vector: np.ndarray, 
                           top_k: int = 10) -> List[Tuple[str, float]]:
        """
        搜索相似文本
        
        Args:
            query_text: 查询文本
            query_vector: 查询向量
            top_k: 返回前k个结果
            
        Returns:
            List[Tuple[str, float]]: (文本, 相似度分数) 列表
        """
        # 搜索相似向量
        similar_vectors = self.vector_index.search_similar(query_vector, top_k)
        
        # 转换为文本结果
        results = []
        for vector_id, score in similar_vectors:
            text = self.vector_index.get_text_by_id(vector_id)
            if text:
                results.append((text, score))
                
                # 更新访问统计
                with self.lock:
                    self.access_frequency[vector_id] = self.access_frequency.get(vector_id, 0) + 1
                    self.last_access_time[vector_id] = time.time()
        
        return results
    
    def _evict_least_frequent(self):
        """淘汰最不常用的向量"""
        with self.lock:
            if not self.access_frequency:
                return
            
            # 找到访问频率最低的向量
            least_frequent_id = min(self.access_frequency.keys(), 
                                  key=lambda x: (self.access_frequency[x], self.last_access_time.get(x, 0)))
            
            # 不淘汰高频向量
            if least_frequent_id in self.frequent_vectors:
                return
            
            # 清理统计信息
            if least_frequent_id in self.access_frequency:
                del self.access_frequency[least_frequent_id]
            if least_frequent_id in self.last_access_time:
                del self.last_access_time[least_frequent_id]
            
            logger.debug(f"淘汰向量: ID={least_frequent_id}")
    
    def get_cache_stats(self) -> Dict[str, Any]:
        """获取缓存统计信息"""
        with self.lock:
            index_stats = self.vector_index.get_stats()
            
            return {
                **index_stats,
                'max_cache_size': self.max_cache_size,
                'similarity_threshold': self.similarity_threshold,
                'frequent_vectors_count': len(self.frequent_vectors),
                'access_frequency_entries': len(self.access_frequency)
            }
    
    def clear_cache(self):
        """清空缓存"""
        with self.lock:
            self.vector_index = VectorIndex()
            self.access_frequency.clear()
            self.last_access_time.clear()
            self.frequent_vectors.clear()
            
            logger.info("向量缓存已清空")

# 全局实例
_global_vector_cache = None

def get_intelligent_vector_cache() -> IntelligentVectorCache:
    """获取全局智能向量缓存"""
    global _global_vector_cache
    if _global_vector_cache is None:
        _global_vector_cache = IntelligentVectorCache()
    return _global_vector_cache

if __name__ == "__main__":
    # 测试智能向量缓存
    print("🧪 测试智能向量缓存")
    
    cache = get_intelligent_vector_cache()
    
    # 测试数据
    test_texts = [
        "这是第一个测试文本",
        "这是第二个测试文本",
        "这是第三个测试文本",
        "这是第四个测试文本",
        "这是第五个测试文本"
    ]
    
    # 生成测试向量
    test_vectors = [np.random.rand(384) for _ in test_texts]
    
    # 添加向量到缓存
    for text, vector in zip(test_texts, test_vectors):
        vector_id = cache.add_or_update_vector(text, vector)
        print(f"添加向量: ID={vector_id}, 文本={text[:20]}...")
    
    # 测试相似度搜索
    query_vector = test_vectors[0]  # 使用第一个向量作为查询
    results = cache.search_similar_texts("查询文本", query_vector, top_k=3)
    
    print(f"相似度搜索结果: {len(results)} 条")
    for text, score in results:
        print(f"  {text[:30]}...: {score:.4f}")
    
    # 显示统计信息
    stats = cache.get_cache_stats()
    print(f"缓存统计: {stats}")
