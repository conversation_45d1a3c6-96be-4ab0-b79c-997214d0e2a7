@echo off
chcp 65001 >nul
title Mem0智能记忆系统 - 一键启动
color 0A

echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                  🧠 Mem0智能记忆系统                        ║
echo ║                    一键启动 + 自动打开                       ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

REM 设置目标端口
set TARGET_PORT=8510
set DASHBOARD_URL=http://localhost:%TARGET_PORT%

echo 🔍 检查系统环境...

REM 检查Ollama服务
echo 📡 检查Ollama服务状态...
curl -s http://localhost:11434/api/tags >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Ollama服务未运行
    echo.
    echo 💡 正在尝试启动Ollama服务...
    start /min ollama serve
    echo ⏳ 等待Ollama服务启动...
    timeout /t 10 /nobreak >nul
    
    REM 再次检查
    curl -s http://localhost:11434/api/tags >nul 2>&1
    if %errorlevel% neq 0 (
        echo ❌ 无法启动Ollama服务
        echo 💡 请手动启动Ollama: ollama serve
        echo.
        pause
        exit /b 1
    )
)
echo ✅ Ollama服务正常

echo.
echo 🧹 清理端口占用...

REM 强力清理所有相关进程
for /f "tokens=2" %%i in ('tasklist /fi "imagename eq python.exe" /fo csv 2^>nul ^| findstr /i "dashboard"') do (
    echo 🔄 终止dashboard进程 %%i
    taskkill /f /pid %%i >nul 2>&1
)

REM 清理端口占用
for /f "tokens=5" %%a in ('netstat -aon 2^>nul ^| findstr :%TARGET_PORT%') do (
    echo 🔄 清理端口占用进程 %%a
    taskkill /f /pid %%a >nul 2>&1
)

REM 等待端口释放
echo ⏳ 等待端口释放...
timeout /t 3 /nobreak >nul

echo.
echo 🚀 启动Mem0仪表盘...

REM 进入项目目录
cd /d "%~dp0"

REM 检查核心文件是否存在
if not exist "dashboard_final.py" (
    echo ❌ 找不到dashboard_final.py文件
    echo 💡 请确保在正确的目录中运行此脚本
    pause
    exit /b 1
)

echo 📱 启动仪表盘服务器...
echo 🌐 访问地址: %DASHBOARD_URL%
echo.

REM 在后台启动仪表盘
start /min cmd /c "streamlit run dashboard_final.py --server.port %TARGET_PORT% --server.headless true"

echo ⏳ 等待服务器启动...
timeout /t 8 /nobreak >nul

REM 检查服务器是否启动成功
echo 🔍 检查服务器状态...
curl -s %DASHBOARD_URL% >nul 2>&1
if %errorlevel% neq 0 (
    echo ⏳ 服务器仍在启动中，再等待5秒...
    timeout /t 5 /nobreak >nul
    
    curl -s %DASHBOARD_URL% >nul 2>&1
    if %errorlevel% neq 0 (
        echo ❌ 服务器启动失败
        echo 💡 请检查错误信息并手动启动
        pause
        exit /b 1
    )
)

echo ✅ 服务器启动成功！

echo.
echo 🌐 正在打开浏览器...
echo 📱 仪表盘地址: %DASHBOARD_URL%

REM 打开默认浏览器
start "" "%DASHBOARD_URL%"

echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                    🎉 启动完成！                            ║
echo ║                                                              ║
echo ║  📱 仪表盘已在浏览器中打开                                   ║
echo ║  🌐 访问地址: %DASHBOARD_URL%                               ║
echo ║                                                              ║
echo ║  🎯 功能特性:                                                ║
echo ║    • 📁 智能文件上传和处理                                   ║
echo ║    • 💬 对话上下文记忆                                       ║
echo ║    • 🔍 语义搜索和分析                                       ║
echo ║    • 📊 数据可视化分析                                       ║
echo ║                                                              ║
echo ║  ⚠️  关闭此窗口将停止仪表盘服务                              ║
echo ║  💡 如需后台运行，请最小化此窗口                             ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

echo 🔄 服务器正在运行中...
echo 💡 按任意键停止服务器并退出
pause >nul

echo.
echo 🛑 正在停止服务器...

REM 清理进程
for /f "tokens=5" %%a in ('netstat -aon 2^>nul ^| findstr :%TARGET_PORT%') do (
    echo 🔄 停止进程 %%a
    taskkill /f /pid %%a >nul 2>&1
)

echo ✅ 服务器已停止
echo 👋 感谢使用Mem0智能记忆系统！
timeout /t 2 /nobreak >nul
