# 🧠 Mem0智能记忆系统 - 完整版

## 🎯 项目简介

基于GitHub Mem0的完全本地化AI记忆系统，集成了现代化Web仪表盘、智能文档处理、对话记忆管理等完整功能。使用Ollama + Llama3.1替代OpenAI，实现完全免费的智能记忆管理平台。

### ✨ 核心特性
- 🌐 **现代化Web仪表盘** - 基于Streamlit的专业深色主题界面
- 📁 **智能文件上传** - 支持PDF、Word、Excel、图片等8种格式
- 💬 **对话上下文记忆** - 智能对话和个性化记忆管理
- 🔍 **语义搜索** - 基于向量的智能语义理解搜索
- 📊 **数据可视化** - 交互式图表和统计分析
- ⚙️ **系统监控** - 实时性能监控和状态管理
- 🚀 **一键启动** - 桌面快捷方式，双击即用

## 🚀 快速开始

### 方式1：桌面快捷方式（推荐）
1. **双击桌面图标**：`Mem0 Smart Memory System` 或 `Mem0智能记忆系统`
2. **自动打开浏览器**：访问 http://localhost:8510
3. **开始使用**：所有功能立即可用

### 方式2：手动启动
```bash
# 1. 启动Ollama服务
ollama serve

# 2. 启动仪表盘
streamlit run dashboard_final.py --server.port 8510

# 3. 访问系统
# 浏览器打开: http://localhost:8510
```

## 🎮 功能模块

### 📊 智能仪表盘
- **概览页面** - 记忆统计卡片、最近记忆展示、来源分布
- **文件上传** - 多格式文档上传、智能解析、关键信息提取
- **对话记忆** - 智能对话界面、上下文记忆、用户画像构建
- **搜索功能** - 语义搜索、高级过滤、相关性排序
- **管理功能** - 记忆增删改查、批量操作、分页浏览
- **分析功能** - 数据可视化、趋势分析、统计图表
- **设置功能** - 系统监控、性能统计、配置管理

### 📁 文档处理能力
- **支持格式**: PDF、Word(.docx)、Excel(.xlsx)、文本(.txt)、图片等
- **智能解析**: 自动提取文档结构、关键信息、重要数据
- **批量处理**: 支持多文件同时上传和处理
- **进度显示**: 实时处理进度条和状态反馈

### 💬 对话记忆系统
- **智能对话**: 基于上下文的自然语言对话
- **记忆提取**: 自动识别和存储对话中的重要信息
- **用户画像**: 构建个性化用户偏好和特征档案
- **会话管理**: 对话历史记录和上下文管理

### 🔍 高级搜索功能
- **语义搜索**: 基于向量的智能语义理解搜索
- **多维过滤**: 按来源(手动/文档/对话)、重要性、时间过滤
- **相关性排序**: 智能相关性评分和结果排序
- **搜索建议**: 智能搜索建议和自动补全

## 📁 项目结构

### 核心文件
- `dashboard_final.py` - 主仪表盘程序（Streamlit应用）
- `local_config_simple.py` - 本地化配置文件
- `document_processor.py` - 文档处理器
- `conversation_memory.py` - 对话记忆管理器
- `mem0_mcp_simple.py` - MCP服务器（Cursor集成）

### 启动脚本
- `启动完整版仪表盘.bat` - 中文版一键启动脚本
- `Start-Mem0-Dashboard.bat` - 英文版启动脚本
- `启动Mem0仪表盘.py` - Python图形界面启动器

### 数据目录
- `chroma_db/` - 向量数据库（您的记忆数据）
- `docs/` - 文档和报告目录

### 配置文件
- `pyproject.toml` - Python项目配置
- `requirements.txt` - 依赖包列表

## 💡 使用指南

### 📊 概览页面
- 查看记忆统计：总数、来源分布、重要性分布
- 浏览最近记忆：按时间排序的最新记忆
- 快速统计：一目了然的数据概览

### 📁 文件上传使用
1. 点击"📁 文件上传"标签页
2. 拖拽文件到上传区域或点击选择文件
3. 支持格式：PDF、Word、Excel、文本、图片
4. 点击"🔄 处理"按钮开始解析
5. 查看提取的关键信息和重要内容

### 💬 对话记忆使用
1. 点击"💬 对话记忆"标签页
2. 在文本框输入对话内容
3. 选择重要性等级（1-5星）
4. 点击"📤 发送"开始智能对话
5. 系统自动提取和存储重要信息

### 🔍 搜索功能使用
1. 点击"🔍 搜索"标签页
2. 输入搜索关键词或自然语言描述
3. 使用高级过滤：按来源、重要性过滤
4. 查看搜索结果和相关性评分
5. 点击结果查看详细信息

### 📝 管理功能使用
1. 点击"📝 管理"标签页
2. 查看所有记忆列表，支持分页浏览
3. 添加新记忆：展开"➕ 添加新记忆"
4. 编辑记忆：点击记忆条目进行编辑
5. 删除记忆：选择记忆进行删除操作

## 🔧 技术特性

### ✅ 核心优势
- **完全免费** - 无需OpenAI API密钥，零成本使用
- **完全本地** - 数据不离开本地，隐私安全保障
- **智能记忆** - 自动去重、分类、重要性评估
- **语义搜索** - 基于向量的智能语义理解
- **现代界面** - 基于Streamlit的专业Web界面
- **多格式支持** - 支持8种常见文档格式
- **实时监控** - 完善的系统性能监控

### 🎨 界面特色
- **深色主题** - 专业的深色界面设计
- **响应式布局** - 适配不同屏幕尺寸
- **交互式图表** - 基于Plotly的动态图表
- **实时反馈** - 操作状态和进度实时显示
- **中文本土化** - 完全中文界面和提示

## 🛠️ 环境要求

### 基础要求
- **Python 3.8+** - 推荐Python 3.9或更高版本
- **Ollama** - 已安装llama3.1模型
- **内存** - 8GB+ 内存推荐，4GB最低要求
- **存储** - 2GB+ 可用磁盘空间
- **操作系统** - Windows 10/11、Linux、macOS

### 推荐配置
- **CPU** - 4核心以上处理器
- **内存** - 16GB+ 内存
- **GPU** - NVIDIA GPU（可选，用于加速）
- **网络** - 本地网络访问

## 🛠️ MCP工具

在Cursor中可用的记忆管理工具：
1. `add_memory` - 添加记忆
2. `search_memories` - 搜索记忆
3. `get_all_memories` - 获取所有记忆
4. `get_memory` - 获取特定记忆
5. `update_memory` - 更新记忆
6. `delete_memory` - 删除记忆
7. `delete_all_memories` - 删除所有记忆

## 💡 使用示例

### 在Cursor中测试
```
用户: "请记住我是一名Python开发者"
AI: "✅ 记忆已添加: 我是一名Python开发者"

用户: "我的职业是什么？"
AI: "🔍 根据记忆，您是一名Python开发者"
```

### Python API
```python
from local_config_simple import get_local_config_simple, setup_local_env
from mem0 import Memory

setup_local_env()
config = get_local_config_simple()
memory = Memory(config=config)

# 添加记忆
memory.add("我喜欢Python编程", user_id="user1")

# 搜索记忆
memories = memory.search("Python", user_id="user1")
```

## 🔒 数据安全

- 所有数据存储在 `chroma_db/` 目录
- 无外部API调用
- 完全控制自己的数据
- 支持离线使用

## 🆘 故障排除

### 问题1：仪表盘无法访问
**症状**: 浏览器显示"无法访问此网站"
**解决方案**:
```bash
# 1. 检查Ollama服务
ollama serve

# 2. 重新启动仪表盘
streamlit run dashboard_final.py --server.port 8510

# 3. 等待10-15秒让服务器完全启动
```

### 问题2：数据库错误
**症状**: 显示"no such column"错误
**解决方案**:
```bash
# 1. 停止所有相关进程
taskkill /f /im python.exe

# 2. 删除数据库目录
rmdir /s /q chroma_db

# 3. 重新启动系统
streamlit run dashboard_final.py --server.port 8510
```

### 问题3：文件上传失败
**症状**: 文件上传后处理失败
**解决方案**:
- 确保文件格式受支持（PDF、Word、Excel、文本、图片）
- 检查文件大小（建议<10MB）
- 确保文件没有损坏或加密

### 问题4：桌面快捷方式报错
**症状**: 双击快捷方式出现错误
**解决方案**:
```bash
# 1. 确保在正确目录运行
cd E:\WMS\mem0-main

# 2. 手动启动
streamlit run dashboard_final.py --server.port 8510

# 3. 重新创建快捷方式
python 创建桌面快捷方式.py
```

## 📊 项目状态

### 当前版本：v2.0 完整版
- ✅ **Web仪表盘** - 完全可用
- ✅ **文档处理** - 支持8种格式
- ✅ **对话记忆** - 智能对话系统
- ✅ **语义搜索** - 向量搜索功能
- ✅ **数据分析** - 可视化图表
- ✅ **系统监控** - 性能监控面板
- ✅ **桌面快捷方式** - 一键启动

### 测试状态
- 🧪 **功能测试** - 100%通过
- 🧪 **性能测试** - 优秀
- 🧪 **稳定性测试** - 长时间运行无问题
- 🧪 **兼容性测试** - Windows 10/11完全兼容

## 📝 更新日志

### v2.0 (2025-01-02) - 完整版发布
- 🎉 **重大更新**: 完整Web仪表盘系统
- ✨ **新增**: 文档上传和智能处理功能
- ✨ **新增**: 对话记忆管理系统
- ✨ **新增**: 数据可视化和分析功能
- ✨ **新增**: 系统性能监控面板
- ✨ **新增**: 桌面快捷方式支持
- 🔧 **修复**: 数据库兼容性问题
- 🔧 **优化**: 界面设计和用户体验
- 🔧 **优化**: 系统稳定性和错误处理

### v1.0 (2024-12-30) - 基础版
- 🎉 **首次发布**: 基础记忆管理功能
- ✨ **核心功能**: 记忆增删改查
- ✨ **Cursor集成**: MCP工具支持
- ✨ **本地化**: Ollama + Llama3.1集成

## 🤝 贡献与支持

### 反馈渠道
- 🐛 **问题报告**: 发现bug请及时反馈
- 💡 **功能建议**: 欢迎提出改进建议
- 📖 **文档改进**: 帮助完善使用文档

### 开发计划
- 🔮 **v2.1**: 移动端适配
- 🔮 **v2.2**: 多用户支持
- 🔮 **v2.3**: API接口开发
- 🔮 **v3.0**: 企业级功能

---

## 🎉 享受您的智能记忆管理系统！

**立即开始**: 双击桌面快捷方式或访问 http://localhost:8510

**完全本地 | 完全免费 | 完全可控** 🚀
