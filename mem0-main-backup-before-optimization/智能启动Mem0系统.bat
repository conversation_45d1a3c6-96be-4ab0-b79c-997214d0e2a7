@echo off
chcp 65001 >nul
title 🧠 Mem0智能记忆系统 - 智能启动器
color 0A

echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                  🧠 Mem0智能记忆系统                        ║
echo ║                    智能启动器 v2.0                          ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

REM 设置目标端口
set TARGET_PORT=8510
set DASHBOARD_URL=http://localhost:%TARGET_PORT%

echo 🔍 正在定位系统文件...

REM 智能查找项目目录
set PROJECT_DIR=
set DASHBOARD_FILE=

REM 方法1: 检查当前目录
if exist "dashboard_final.py" (
    set PROJECT_DIR=%CD%
    set DASHBOARD_FILE=%CD%\dashboard_final.py
    echo ✅ 在当前目录找到系统文件
    goto :found_project
)

REM 方法2: 检查脚本所在目录
cd /d "%~dp0"
if exist "dashboard_final.py" (
    set PROJECT_DIR=%CD%
    set DASHBOARD_FILE=%CD%\dashboard_final.py
    echo ✅ 在脚本目录找到系统文件
    goto :found_project
)

REM 方法3: 检查常见位置
for %%d in ("E:\WMS\mem0-main" "D:\WMS\mem0-main" "C:\WMS\mem0-main" "%USERPROFILE%\mem0-main") do (
    if exist "%%d\dashboard_final.py" (
        cd /d "%%d"
        set PROJECT_DIR=%%d
        set DASHBOARD_FILE=%%d\dashboard_final.py
        echo ✅ 在 %%d 找到系统文件
        goto :found_project
    )
)

REM 方法4: 搜索整个系统
echo 🔍 在系统中搜索 dashboard_final.py...
for /f "delims=" %%i in ('dir /s /b C:\dashboard_final.py 2^>nul') do (
    set DASHBOARD_FILE=%%i
    for %%j in ("%%i") do set PROJECT_DIR=%%~dpj
    cd /d "!PROJECT_DIR!"
    echo ✅ 在 !PROJECT_DIR! 找到系统文件
    goto :found_project
)

echo ❌ 未找到 Mem0 系统文件
echo 💡 请确保以下文件存在:
echo    - dashboard_final.py
echo    - performance_cache.py
echo    - async_task_manager.py
echo.
echo 🔧 解决方案:
echo    1. 将此脚本放在 mem0-main 目录中
echo    2. 或者手动进入 mem0-main 目录运行
echo.
pause
exit /b 1

:found_project
echo 📁 项目目录: %PROJECT_DIR%
echo 📄 主文件: %DASHBOARD_FILE%

echo.
echo 🔍 检查系统环境...

REM 检查Python
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Python未安装或不在PATH中
    echo 💡 请安装Python 3.8+并添加到PATH
    pause
    exit /b 1
)
echo ✅ Python环境正常

REM 检查Streamlit
python -c "import streamlit" >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Streamlit未安装
    echo 💡 正在安装Streamlit...
    pip install streamlit
    if %errorlevel% neq 0 (
        echo ❌ Streamlit安装失败
        pause
        exit /b 1
    )
)
echo ✅ Streamlit环境正常

REM 检查Ollama服务
echo 📡 检查Ollama服务状态...
curl -s http://localhost:11434/api/tags >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Ollama服务未运行
    echo.
    echo 💡 正在尝试启动Ollama服务...
    start /min ollama serve
    echo ⏳ 等待Ollama服务启动...
    timeout /t 10 /nobreak >nul
    
    REM 再次检查
    curl -s http://localhost:11434/api/tags >nul 2>&1
    if %errorlevel% neq 0 (
        echo ❌ 无法启动Ollama服务
        echo 💡 请手动启动Ollama: ollama serve
        echo.
        set /p choice="是否继续启动仪表盘? (y/N): "
        if /i not "!choice!"=="y" (
            exit /b 1
        )
    ) else (
        echo ✅ Ollama服务启动成功
    )
) else (
    echo ✅ Ollama服务正常运行
)

echo.
echo 🧹 清理端口占用...

REM 强力清理端口
for /f "tokens=5" %%a in ('netstat -aon 2^>nul ^| findstr :%TARGET_PORT%') do (
    echo 🔄 清理端口占用进程 %%a
    taskkill /f /pid %%a >nul 2>&1
)

REM 清理所有相关Python进程
for /f "tokens=2" %%i in ('tasklist /fi "imagename eq python.exe" /fo csv 2^>nul ^| findstr /i "dashboard"') do (
    echo 🔄 清理dashboard进程 %%i
    taskkill /f /pid %%i >nul 2>&1
)

echo ⏳ 等待端口释放...
timeout /t 3 /nobreak >nul

echo.
echo 🚀 启动Mem0智能记忆系统...
echo 📱 访问地址: %DASHBOARD_URL%
echo.

REM 启动仪表盘
echo 🔄 正在启动仪表盘服务器...
start /min cmd /c "cd /d "%PROJECT_DIR%" && streamlit run dashboard_final.py --server.port %TARGET_PORT% --server.headless true"

echo ⏳ 等待服务器启动...
timeout /t 10 /nobreak >nul

REM 检查服务器状态
echo 🔍 检查服务器状态...
set SERVER_READY=0
for /l %%i in (1,1,5) do (
    curl -s %DASHBOARD_URL% >nul 2>&1
    if !errorlevel! equ 0 (
        set SERVER_READY=1
        goto :server_ready
    )
    echo ⏳ 等待服务器响应... (%%i/5)
    timeout /t 3 /nobreak >nul
)

:server_ready
if %SERVER_READY% equ 1 (
    echo ✅ 服务器启动成功！
) else (
    echo ⚠️ 服务器可能仍在启动中...
    echo 💡 请稍等片刻，然后手动访问: %DASHBOARD_URL%
)

echo.
echo 🌐 正在打开浏览器...
start "" "%DASHBOARD_URL%"

echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                    🎉 启动完成！                            ║
echo ║                                                              ║
echo ║  📱 仪表盘已在浏览器中打开                                   ║
echo ║  🌐 访问地址: %DASHBOARD_URL%                               ║
echo ║                                                              ║
echo ║  🎯 系统功能:                                                ║
echo ║    • 📁 智能文件上传和处理                                   ║
echo ║    • 💬 对话上下文记忆                                       ║
echo ║    • 🔍 GPU加速语义搜索                                      ║
echo ║    • 📊 智能数据分析                                         ║
echo ║    • ⚡ 异步处理和缓存优化                                    ║
echo ║    • 🎮 GPU加速向量计算                                      ║
echo ║                                                              ║
echo ║  ⚠️  关闭此窗口将停止仪表盘服务                              ║
echo ║  💡 如需后台运行，请最小化此窗口                             ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

echo 🔄 服务器正在运行中...
echo 💡 按任意键停止服务器并退出
pause >nul

echo.
echo 🛑 正在停止服务器...

REM 清理进程
for /f "tokens=5" %%a in ('netstat -aon 2^>nul ^| findstr :%TARGET_PORT%') do (
    echo 🔄 停止进程 %%a
    taskkill /f /pid %%a >nul 2>&1
)

echo ✅ 服务器已停止
echo 👋 感谢使用Mem0智能记忆系统！
timeout /t 2 /nobreak >nul
