================================================================================
代码路径扫描报告
================================================================================

📊 统计信息:
  - 包含问题的文件: 42
  - 发现的问题总数: 184
  - 高风险问题: 1
  - 中风险问题: 128
  - 低风险问题: 55

🔴 高风险问题
----------------------------------------
📁 文件: views\warehouse_management\shelf_3d_viewer.py
📍 行号: 35
🔍 类型: path_pattern
📂 路径: /dev/null
💬 代码: pv.set_error_output_file('nul' if os.name == 'nt' else '/dev/null')...

🟡 中风险问题
----------------------------------------
📁 文件: app.py
📍 行号: 14
🔍 类型: function_call:os.path.dirname
💬 代码: sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))...

📁 文件: app.py
📍 行号: 14
🔍 类型: function_call:os.path.abspath
💬 代码: sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))...

📁 文件: create_database.py
📍 行号: 51
🔍 类型: function_call:open(
💬 代码: with open(sql_file, 'r', encoding='utf-8') as f:...

📁 文件: config\settings.py
📍 行号: 53
🔍 类型: function_call:os.path.join
💬 代码: LOG_FILE_PATH = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'logs', 'app.log') # 项目根目录下...

📁 文件: config\settings.py
📍 行号: 53
🔍 类型: function_call:os.path.dirname
💬 代码: LOG_FILE_PATH = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'logs', 'app.log') # 项目根目录下...

📁 文件: config\settings.py
📍 行号: 60
🔍 类型: function_call:os.path.dirname
💬 代码: if not os.path.exists(os.path.dirname(LOG_FILE_PATH)):...

📁 文件: config\settings.py
📍 行号: 62
🔍 类型: function_call:os.path.dirname
💬 代码: os.makedirs(os.path.dirname(LOG_FILE_PATH))...

📁 文件: config\system_config.py
📍 行号: 16
🔍 类型: function_call:Path(
💬 代码: self.project_root = Path(__file__).parent.parent...

📁 文件: config\system_config.py
📍 行号: 53
🔍 类型: function_call:open(
💬 代码: with open(self.config_file, 'r', encoding='utf-8') as f:...

📁 文件: config\system_config.py
📍 行号: 89
🔍 类型: function_call:open(
💬 代码: with open(self.config_file, 'w', encoding='utf-8') as f:...

📁 文件: controllers\inventory_controller.py
📍 行号: 18
🔍 类型: function_call:os.path.dirname
💬 代码: sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))...

📁 文件: controllers\inventory_controller.py
📍 行号: 18
🔍 类型: function_call:os.path.abspath
💬 代码: sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))...

📁 文件: controllers\product_controller.py
📍 行号: 13
🔍 类型: function_call:os.path.dirname
💬 代码: sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))...

📁 文件: controllers\product_controller.py
📍 行号: 13
🔍 类型: function_call:os.path.abspath
💬 代码: sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))...

📁 文件: controllers\user_controller.py
📍 行号: 15
🔍 类型: function_call:os.path.dirname
💬 代码: sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))...

📁 文件: controllers\user_controller.py
📍 行号: 15
🔍 类型: function_call:os.path.abspath
💬 代码: sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))...

📁 文件: services\backup_service.py
📍 行号: 15
🔍 类型: function_call:os.path.dirname
💬 代码: sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))...

📁 文件: services\backup_service.py
📍 行号: 15
🔍 类型: function_call:os.path.abspath
💬 代码: sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))...

📁 文件: services\backup_service.py
📍 行号: 22
🔍 类型: function_call:Path(
💬 代码: self.project_root = Path(__file__).parent.parent...

📁 文件: services\backup_service.py
📍 行号: 172
🔍 类型: function_call:open(
💬 代码: with open(backup_path, 'r', encoding='utf-8') as f:...

📁 文件: services\backup_service.py
📍 行号: 204
🔍 类型: function_call:open(
💬 代码: with open(self.backup_history_file, 'r', encoding='utf-8') as f:...

📁 文件: services\backup_service.py
📍 行号: 215
🔍 类型: function_call:open(
💬 代码: with open(self.backup_history_file, 'w', encoding='utf-8') as f:...

📁 文件: services\backup_service.py
📍 行号: 227
🔍 类型: function_call:open(
💬 代码: with open(self.backup_history_file, 'r', encoding='utf-8') as f:...

📁 文件: services\excel_service.py
📍 行号: 14
🔍 类型: function_call:os.path.dirname
💬 代码: sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))...

📁 文件: services\excel_service.py
📍 行号: 14
🔍 类型: function_call:os.path.abspath
💬 代码: sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))...

📁 文件: services\inventory_service.py
📍 行号: 14
🔍 类型: function_call:os.path.dirname
💬 代码: sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))...

📁 文件: services\inventory_service.py
📍 行号: 14
🔍 类型: function_call:os.path.abspath
💬 代码: sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))...

📁 文件: services\inventory_transaction_service.py
📍 行号: 17
🔍 类型: function_call:os.path.dirname
💬 代码: sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))...

📁 文件: services\inventory_transaction_service.py
📍 行号: 17
🔍 类型: function_call:os.path.abspath
💬 代码: sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))...

📁 文件: services\log_service.py
📍 行号: 14
🔍 类型: function_call:os.path.dirname
💬 代码: sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))...

📁 文件: services\log_service.py
📍 行号: 14
🔍 类型: function_call:os.path.abspath
💬 代码: sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))...

📁 文件: services\log_service.py
📍 行号: 74
🔍 类型: function_call:open(
💬 代码: with open(log_file, 'r', encoding='utf-8') as f:...

📁 文件: services\log_service.py
📍 行号: 99
🔍 类型: function_call:open(
💬 代码: with open(log_file, 'a', encoding='utf-8') as f:...

📁 文件: services\log_service.py
📍 行号: 156
🔍 类型: function_call:open(
💬 代码: with open(log_file, 'r', encoding='utf-8') as f:...

📁 文件: services\log_service.py
📍 行号: 222
🔍 类型: function_call:open(
💬 代码: with open(log_file, 'w', encoding='utf-8') as f:...

📁 文件: services\product_service.py
📍 行号: 15
🔍 类型: function_call:os.path.dirname
💬 代码: sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))...

📁 文件: services\product_service.py
📍 行号: 15
🔍 类型: function_call:os.path.abspath
💬 代码: sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))...

📁 文件: services\user_service.py
📍 行号: 13
🔍 类型: function_call:os.path.dirname
💬 代码: sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))...

📁 文件: services\user_service.py
📍 行号: 13
🔍 类型: function_call:os.path.abspath
💬 代码: sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))...

📁 文件: utils\code_path_scanner.py
📍 行号: 23
🔍 类型: function_call:Path(
💬 代码: current_file = Path(__file__).resolve()...

📁 文件: utils\code_path_scanner.py
📍 行号: 26
🔍 类型: function_call:Path(
💬 代码: self.project_root = Path(project_root)...

📁 文件: utils\code_path_scanner.py
📍 行号: 40
🔍 类型: path_pattern
📂 路径: ]*(?:resources|images|icons|styles|ui)[^
💬 代码: r'["\']([^"\']*(?:resources|images|icons|styles|ui)[^"\']*)["\']',...

📁 文件: utils\code_path_scanner.py
📍 行号: 60
🔍 类型: function_call:os.path.join
💬 代码: 'os.path.join',...

📁 文件: utils\code_path_scanner.py
📍 行号: 61
🔍 类型: function_call:os.path.dirname
💬 代码: 'os.path.dirname',...

📁 文件: utils\code_path_scanner.py
📍 行号: 62
🔍 类型: function_call:os.path.abspath
💬 代码: 'os.path.abspath',...

📁 文件: utils\code_path_scanner.py
📍 行号: 63
🔍 类型: function_call:Path(
💬 代码: 'Path(',...

📁 文件: utils\code_path_scanner.py
📍 行号: 64
🔍 类型: function_call:open(
💬 代码: 'open(',...

📁 文件: utils\code_path_scanner.py
📍 行号: 65
🔍 类型: function_call:QPixmap(
💬 代码: 'QPixmap(',...

📁 文件: utils\code_path_scanner.py
📍 行号: 66
🔍 类型: function_call:QIcon(
💬 代码: 'QIcon(',...

📁 文件: utils\code_path_scanner.py
📍 行号: 81
🔍 类型: function_call:open(
💬 代码: with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:...

📁 文件: utils\code_path_scanner.py
📍 行号: 140
🔍 类型: path_pattern
📂 路径: resources
💬 代码: path_indicators = ['/', '\\', 'resources', 'images', 'logs', 'backup', 'export', 'temp']...

📁 文件: utils\code_path_scanner.py
📍 行号: 140
🔍 类型: path_pattern
📂 路径: images
💬 代码: path_indicators = ['/', '\\', 'resources', 'images', 'logs', 'backup', 'export', 'temp']...

📁 文件: utils\code_path_scanner.py
📍 行号: 150
🔍 类型: path_pattern
📂 路径: resources
💬 代码: if any(keyword in path.lower() for keyword in ['resources', 'images', 'icons', 'styles']):...

📁 文件: utils\code_path_scanner.py
📍 行号: 150
🔍 类型: path_pattern
📂 路径: images
💬 代码: if any(keyword in path.lower() for keyword in ['resources', 'images', 'icons', 'styles']):...

📁 文件: utils\code_path_scanner.py
📍 行号: 175
🔍 类型: function_call:Path(
💬 代码: file_path = Path(root) / file...

📁 文件: utils\code_path_scanner.py
📍 行号: 260
🔍 类型: function_call:open(
💬 代码: with open(output_file, 'w', encoding='utf-8') as f:...

📁 文件: utils\database_path_updater.py
📍 行号: 15
🔍 类型: function_call:Path(
💬 代码: sys.path.insert(0, str(Path(__file__).parent.parent))...

📁 文件: utils\database_path_updater.py
📍 行号: 268
🔍 类型: function_call:open(
💬 代码: with open(backup_filename, 'w', encoding='utf-8') as backup_file:...

📁 文件: utils\path_manager.py
📍 行号: 53
🔍 类型: function_call:Path(
💬 代码: current_path = Path(__file__).resolve()...

📁 文件: utils\path_manager.py
📍 行号: 74
🔍 类型: function_call:Path(
💬 代码: self._project_root = Path(self._project_root).resolve()...

📁 文件: utils\path_manager.py
📍 行号: 83
🔍 类型: path_pattern
📂 路径: resources
💬 代码: "resources": "resources",...

📁 文件: utils\path_manager.py
📍 行号: 83
🔍 类型: path_pattern
📂 路径: resources
💬 代码: "resources": "resources",...

📁 文件: utils\path_manager.py
📍 行号: 84
🔍 类型: path_pattern
📂 路径: images
💬 代码: "images": "resources/images",...

📁 文件: utils\path_manager.py
📍 行号: 84
🔍 类型: path_pattern
📂 路径: resources/images
💬 代码: "images": "resources/images",...

📁 文件: utils\path_manager.py
📍 行号: 85
🔍 类型: path_pattern
📂 路径: resources/icons
💬 代码: "icons": "resources/icons",...

📁 文件: utils\path_manager.py
📍 行号: 86
🔍 类型: path_pattern
📂 路径: resources/styles
💬 代码: "styles": "resources/styles",...

📁 文件: utils\path_manager.py
📍 行号: 87
🔍 类型: path_pattern
📂 路径: resources/ui
💬 代码: "ui": "resources/ui",...

📁 文件: utils\path_manager.py
📍 行号: 107
🔍 类型: function_call:open(
💬 代码: with open(self._config_file, 'r', encoding='utf-8') as f:...

📁 文件: utils\path_manager.py
📍 行号: 132
🔍 类型: function_call:open(
💬 代码: with open(self._config_file, 'w', encoding='utf-8') as f:...

📁 文件: utils\path_manager.py
📍 行号: 163
🔍 类型: path_pattern
📂 路径: resources
💬 代码: path_key: 路径键名（如'resources', 'images'等）...

📁 文件: utils\path_manager.py
📍 行号: 163
🔍 类型: path_pattern
📂 路径: images
💬 代码: path_key: 路径键名（如'resources', 'images'等）...

📁 文件: utils\path_manager.py
📍 行号: 217
🔍 类型: function_call:Path(
💬 代码: full_path = Path(full_path)...

📁 文件: utils\path_manager.py
📍 行号: 238
🔍 类型: function_call:Path(
💬 代码: stored_path = Path(stored_path)...

📁 文件: utils\path_manager.py
📍 行号: 251
🔍 类型: function_call:Path(
💬 代码: return self._project_root / Path(*relative_parts)...

📁 文件: utils\path_manager.py
📍 行号: 365
🔍 类型: path_pattern
📂 路径: images
💬 代码: return get_resource_path('images', filename)...

📁 文件: utils\path_manager.py
📍 行号: 380
🔍 类型: path_pattern
📂 路径: images
💬 代码: print("图片目录:", pm.get_path('images'))...

📁 文件: views\main_window.py
📍 行号: 18
🔍 类型: function_call:os.path.dirname
💬 代码: sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))...

📁 文件: views\main_window.py
📍 行号: 18
🔍 类型: function_call:os.path.abspath
💬 代码: sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))...

📁 文件: views\auth\forgot_password_window.py
📍 行号: 19
🔍 类型: function_call:os.path.dirname
💬 代码: sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))...

📁 文件: views\auth\forgot_password_window.py
📍 行号: 19
🔍 类型: function_call:os.path.abspath
💬 代码: sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))...

📁 文件: views\auth\login_window.py
📍 行号: 16
🔍 类型: function_call:os.path.dirname
💬 代码: sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))...

📁 文件: views\auth\login_window.py
📍 行号: 16
🔍 类型: function_call:os.path.abspath
💬 代码: sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))...

📁 文件: views\auth\login_window.py
📍 行号: 89
🔍 类型: function_call:QPixmap(
💬 代码: pixmap = QPixmap(str(bg_path))...

📁 文件: views\auth\login_window.py
📍 行号: 126
🔍 类型: function_call:QPixmap(
💬 代码: fallback_pixmap = QPixmap(1000, 700)...

📁 文件: views\auth\register_window.py
📍 行号: 17
🔍 类型: function_call:os.path.dirname
💬 代码: sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))...

📁 文件: views\auth\register_window.py
📍 行号: 17
🔍 类型: function_call:os.path.abspath
💬 代码: sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))...

📁 文件: views\auth\user_edit_dialog.py
📍 行号: 16
🔍 类型: function_call:os.path.dirname
💬 代码: sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))...

📁 文件: views\auth\user_edit_dialog.py
📍 行号: 16
🔍 类型: function_call:os.path.abspath
💬 代码: sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))...

📁 文件: views\dashboard\dashboard_widget.py
📍 行号: 26
🔍 类型: function_call:os.path.dirname
💬 代码: sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))...

📁 文件: views\dashboard\dashboard_widget.py
📍 行号: 26
🔍 类型: function_call:os.path.abspath
💬 代码: sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))...

📁 文件: views\dashboard\data_analysis_widget.py
📍 行号: 25
🔍 类型: function_call:os.path.dirname
💬 代码: sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))...

📁 文件: views\dashboard\data_analysis_widget.py
📍 行号: 25
🔍 类型: function_call:os.path.abspath
💬 代码: sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))...

📁 文件: views\dashboard\data_analysis_widget.py
📍 行号: 490
🔍 类型: function_call:open(
💬 代码: with open(file_path, 'w', encoding='utf-8') as f:...

📁 文件: views\dashboard\report_generator_widget.py
📍 行号: 22
🔍 类型: function_call:os.path.dirname
💬 代码: sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))...

📁 文件: views\dashboard\report_generator_widget.py
📍 行号: 22
🔍 类型: function_call:os.path.abspath
💬 代码: sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))...

📁 文件: views\dashboard\report_generator_widget.py
📍 行号: 368
🔍 类型: function_call:open(
💬 代码: with open(file_path, 'w', encoding='utf-8') as f:...

📁 文件: views\inventory_management\analysis_dialog.py
📍 行号: 24
🔍 类型: function_call:os.path.dirname
💬 代码: sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))...

📁 文件: views\inventory_management\analysis_dialog.py
📍 行号: 24
🔍 类型: function_call:os.path.abspath
💬 代码: sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))...

📁 文件: views\inventory_management\inbound_order_dialog.py
📍 行号: 24
🔍 类型: function_call:os.path.dirname
💬 代码: sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))...

📁 文件: views\inventory_management\inbound_order_dialog.py
📍 行号: 24
🔍 类型: function_call:os.path.abspath
💬 代码: sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))...

📁 文件: views\inventory_management\inventory_management_widget.py
📍 行号: 25
🔍 类型: function_call:os.path.dirname
💬 代码: sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))...

📁 文件: views\inventory_management\inventory_management_widget.py
📍 行号: 25
🔍 类型: function_call:os.path.abspath
💬 代码: sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))...

📁 文件: views\inventory_management\new_product_dialog.py
📍 行号: 14
🔍 类型: function_call:os.path.dirname
💬 代码: sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))...

📁 文件: views\inventory_management\new_product_dialog.py
📍 行号: 14
🔍 类型: function_call:os.path.abspath
💬 代码: sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))...

📁 文件: views\inventory_management\outbound_order_dialog.py
📍 行号: 24
🔍 类型: function_call:os.path.dirname
💬 代码: sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))...

📁 文件: views\inventory_management\outbound_order_dialog.py
📍 行号: 24
🔍 类型: function_call:os.path.abspath
💬 代码: sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))...

📁 文件: views\product_management\category_dialog.py
📍 行号: 17
🔍 类型: function_call:os.path.dirname
💬 代码: sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))...

📁 文件: views\product_management\category_dialog.py
📍 行号: 17
🔍 类型: function_call:os.path.abspath
💬 代码: sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))...

📁 文件: views\product_management\product_dialog.py
📍 行号: 20
🔍 类型: function_call:os.path.dirname
💬 代码: sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))...

📁 文件: views\product_management\product_dialog.py
📍 行号: 20
🔍 类型: function_call:os.path.abspath
💬 代码: sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))...

📁 文件: views\product_management\product_management_widget.py
📍 行号: 23
🔍 类型: function_call:os.path.dirname
💬 代码: sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))...

📁 文件: views\product_management\product_management_widget.py
📍 行号: 23
🔍 类型: function_call:os.path.abspath
💬 代码: sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))...

📁 文件: views\product_management\supplier_dialog.py
📍 行号: 17
🔍 类型: function_call:os.path.dirname
💬 代码: sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))...

📁 文件: views\product_management\supplier_dialog.py
📍 行号: 17
🔍 类型: function_call:os.path.abspath
💬 代码: sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))...

📁 文件: views\system_management\system_management_widget.py
📍 行号: 21
🔍 类型: function_call:os.path.dirname
💬 代码: sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))...

📁 文件: views\system_management\system_management_widget.py
📍 行号: 21
🔍 类型: function_call:os.path.abspath
💬 代码: sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))...

📁 文件: views\warehouse_management\factory_dialog.py
📍 行号: 17
🔍 类型: function_call:os.path.dirname
💬 代码: sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))...

📁 文件: views\warehouse_management\factory_dialog.py
📍 行号: 17
🔍 类型: function_call:os.path.abspath
💬 代码: sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))...

📁 文件: views\warehouse_management\shelf_3d_viewer.py
📍 行号: 22
🔍 类型: function_call:os.path.dirname
💬 代码: sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))...

📁 文件: views\warehouse_management\shelf_3d_viewer.py
📍 行号: 22
🔍 类型: function_call:os.path.abspath
💬 代码: sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))...

📁 文件: views\warehouse_management\shelf_dialog.py
📍 行号: 18
🔍 类型: function_call:os.path.dirname
💬 代码: sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))...

📁 文件: views\warehouse_management\shelf_dialog.py
📍 行号: 18
🔍 类型: function_call:os.path.abspath
💬 代码: sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))...

📁 文件: views\warehouse_management\warehouse_dialog.py
📍 行号: 17
🔍 类型: function_call:os.path.dirname
💬 代码: sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))...

📁 文件: views\warehouse_management\warehouse_dialog.py
📍 行号: 17
🔍 类型: function_call:os.path.abspath
💬 代码: sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))...

📁 文件: views\warehouse_management\warehouse_layout_widget.py
📍 行号: 15
🔍 类型: function_call:os.path.dirname
💬 代码: sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))...

📁 文件: views\warehouse_management\warehouse_layout_widget.py
📍 行号: 15
🔍 类型: function_call:os.path.abspath
💬 代码: sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))...

📁 文件: views\warehouse_management\warehouse_management_widget.py
📍 行号: 21
🔍 类型: function_call:os.path.dirname
💬 代码: sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))...

📁 文件: views\warehouse_management\warehouse_management_widget.py
📍 行号: 21
🔍 类型: function_call:os.path.abspath
💬 代码: sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))...

🟢 低风险问题
----------------------------------------
📁 文件: config\settings.py
📍 行号: 53
🔍 类型: path_pattern
📂 路径: logs
💬 代码: LOG_FILE_PATH = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'logs', 'app.log') # 项目根目录下...

📁 文件: config\system_config.py
📍 行号: 37
🔍 类型: path_pattern
📂 路径: auto_backup
💬 代码: "auto_backup": True,...

📁 文件: config\system_config.py
📍 行号: 38
🔍 类型: path_pattern
📂 路径: backup_interval_hours
💬 代码: "backup_interval_hours": 24...

📁 文件: config\system_settings.json
📍 行号: 15
🔍 类型: path_pattern
📂 路径: auto_backup
💬 代码: "auto_backup": true,...

📁 文件: config\system_settings.json
📍 行号: 16
🔍 类型: path_pattern
📂 路径: backup_interval_hours
💬 代码: "backup_interval_hours": 24...

📁 文件: services\backup_service.py
📍 行号: 23
🔍 类型: path_pattern
📂 路径: backups
💬 代码: self.backup_dir = self.project_root / "backups"...

📁 文件: services\backup_service.py
📍 行号: 36
🔍 类型: path_pattern
📂 路径: backup_history.json
💬 代码: self.backup_history_file = self.backup_dir / "backup_history.json"...

📁 文件: services\backup_service.py
📍 行号: 41
🔍 类型: path_pattern
📂 路径: wms_backup_{backup_type}_{timestamp}.sql
💬 代码: return f"wms_backup_{backup_type}_{timestamp}.sql"...

📁 文件: services\backup_service.py
📍 行号: 41
🔍 类型: path_pattern
📂 路径: wms_backup_{backup_type}_{timestamp}.sql
💬 代码: return f"wms_backup_{backup_type}_{timestamp}.sql"...

📁 文件: services\backup_service.py
📍 行号: 64
🔍 类型: path_pattern
📂 路径: --result-file={backup_path}
💬 代码: f"--result-file={backup_path}",...

📁 文件: services\backup_service.py
📍 行号: 87
🔍 类型: path_pattern
📂 路径: 完整备份成功: {backup_filename}
💬 代码: "message": f"完整备份成功: {backup_filename}",...

📁 文件: services\backup_service.py
📍 行号: 118
🔍 类型: path_pattern
📂 路径: 备份文件不存在: {backup_filename}
💬 代码: "message": f"备份文件不存在: {backup_filename}",...

📁 文件: services\backup_service.py
📍 行号: 137
🔍 类型: path_pattern
📂 路径: 备份文件删除成功: {backup_filename}
💬 代码: "message": f"备份文件删除成功: {backup_filename}",...

📁 文件: services\backup_service.py
📍 行号: 156
🔍 类型: path_pattern
📂 路径: 备份文件不存在: {backup_filename}
💬 代码: "message": f"备份文件不存在: {backup_filename}",...

📁 文件: services\backup_service.py
📍 行号: 181
🔍 类型: path_pattern
📂 路径: 数据恢复成功: {backup_filename}
💬 代码: "message": f"数据恢复成功: {backup_filename}",...

📁 文件: services\backup_service.py
📍 行号: 287
🔍 类型: path_pattern
📂 路径: 📁 文件大小: {backup_service.format_file_size(result[
💬 代码: print(f"📁 文件大小: {backup_service.format_file_size(result['size'])}")...

📁 文件: services\log_service.py
📍 行号: 51
🔍 类型: path_pattern
📂 路径: logs
💬 代码: self.log_dir = self.path_manager.get_path('logs')...

📁 文件: utils\code_path_scanner.py
📍 行号: 34
🔍 类型: path_pattern
📂 路径: mem0-main-backup-before-optimization
💬 代码: 'mem0-m', 'mem0-main', 'mem0-main-backup-before-optimization'...

📁 文件: utils\code_path_scanner.py
📍 行号: 47
🔍 类型: path_pattern
📂 路径: ]*logs[^
💬 代码: r'["\']([^"\']*logs[^"\']*)["\']',...

📁 文件: utils\code_path_scanner.py
📍 行号: 49
🔍 类型: path_pattern
📂 路径: ]*backups?[^
💬 代码: r'["\']([^"\']*backups?[^"\']*)["\']',...

📁 文件: utils\code_path_scanner.py
📍 行号: 51
🔍 类型: path_pattern
📂 路径: ]*\.sql[^
💬 代码: r'["\']([^"\']*\.sql[^"\']*)["\']',...

📁 文件: utils\code_path_scanner.py
📍 行号: 53
🔍 类型: path_pattern
📂 路径: ]*exports?[^
💬 代码: r'["\']([^"\']*exports?[^"\']*)["\']',...

📁 文件: utils\code_path_scanner.py
📍 行号: 55
🔍 类型: path_pattern
📂 路径: ]*temp[^
💬 代码: r'["\']([^"\']*temp[^"\']*)["\']',...

📁 文件: utils\code_path_scanner.py
📍 行号: 140
🔍 类型: path_pattern
📂 路径: logs
💬 代码: path_indicators = ['/', '\\', 'resources', 'images', 'logs', 'backup', 'export', 'temp']...

📁 文件: utils\code_path_scanner.py
📍 行号: 140
🔍 类型: path_pattern
📂 路径: backup
💬 代码: path_indicators = ['/', '\\', 'resources', 'images', 'logs', 'backup', 'export', 'temp']...

📁 文件: utils\code_path_scanner.py
📍 行号: 140
🔍 类型: path_pattern
📂 路径: export
💬 代码: path_indicators = ['/', '\\', 'resources', 'images', 'logs', 'backup', 'export', 'temp']...

📁 文件: utils\code_path_scanner.py
📍 行号: 140
🔍 类型: path_pattern
📂 路径: temp
💬 代码: path_indicators = ['/', '\\', 'resources', 'images', 'logs', 'backup', 'export', 'temp']...

📁 文件: utils\code_path_scanner.py
📍 行号: 154
🔍 类型: path_pattern
📂 路径: ../
💬 代码: if path.startswith('./') or path.startswith('../'):...

📁 文件: utils\database_path_updater.py
📍 行号: 38
🔍 类型: path_pattern
📂 路径: system_logs
💬 代码: 'system_logs': [...

📁 文件: utils\database_path_updater.py
📍 行号: 41
🔍 类型: path_pattern
📂 路径: exports
💬 代码: 'exports': [...

📁 文件: utils\database_path_updater.py
📍 行号: 272
🔍 类型: path_pattern
📂 路径: 数据库备份创建成功: {backup_filename}
💬 代码: self.logger.info(f"数据库备份创建成功: {backup_filename}")...

📁 文件: utils\database_path_updater.py
📍 行号: 294
🔍 类型: path_pattern
📂 路径: backup_file
💬 代码: 'backup_file': None,...

📁 文件: utils\database_path_updater.py
📍 行号: 308
🔍 类型: path_pattern
📂 路径: backup_file
💬 代码: result['backup_file'] = str(backup_file) if backup_file else None...

📁 文件: utils\database_path_updater.py
📍 行号: 356
🔍 类型: path_pattern
📂 路径: --no-backup
💬 代码: parser.add_argument('--no-backup', action='store_true', help='跳过数据库备份')...

📁 文件: utils\database_path_updater.py
📍 行号: 393
🔍 类型: path_pattern
📂 路径: backup_file
💬 代码: if result['backup_file']:...

📁 文件: utils\database_path_updater.py
📍 行号: 394
🔍 类型: path_pattern
📂 路径: backup_file
💬 代码: print(f"\n💾 备份文件: {result['backup_file']}")...

📁 文件: utils\path_manager.py
📍 行号: 88
🔍 类型: path_pattern
📂 路径: logs
💬 代码: "logs": "logs",...

📁 文件: utils\path_manager.py
📍 行号: 88
🔍 类型: path_pattern
📂 路径: logs
💬 代码: "logs": "logs",...

📁 文件: utils\path_manager.py
📍 行号: 89
🔍 类型: path_pattern
📂 路径: backups
💬 代码: "backups": "backups",...

📁 文件: utils\path_manager.py
📍 行号: 89
🔍 类型: path_pattern
📂 路径: backups
💬 代码: "backups": "backups",...

📁 文件: utils\path_manager.py
📍 行号: 90
🔍 类型: path_pattern
📂 路径: exports
💬 代码: "exports": "exports",...

📁 文件: utils\path_manager.py
📍 行号: 90
🔍 类型: path_pattern
📂 路径: exports
💬 代码: "exports": "exports",...

📁 文件: utils\path_manager.py
📍 行号: 91
🔍 类型: path_pattern
📂 路径: temp
💬 代码: "temp": "temp",...

📁 文件: utils\path_manager.py
📍 行号: 91
🔍 类型: path_pattern
📂 路径: temp
💬 代码: "temp": "temp",...

📁 文件: utils\path_manager.py
📍 行号: 95
🔍 类型: path_pattern
📂 路径: backup_dir
💬 代码: "backup_dir": "backups/database",...

📁 文件: utils\path_manager.py
📍 行号: 95
🔍 类型: path_pattern
📂 路径: backups/database
💬 代码: "backup_dir": "backups/database",...

📁 文件: utils\path_manager.py
📍 行号: 96
🔍 类型: path_pattern
📂 路径: export_dir
💬 代码: "export_dir": "exports/database"...

📁 文件: utils\path_manager.py
📍 行号: 96
🔍 类型: path_pattern
📂 路径: exports/database
💬 代码: "export_dir": "exports/database"...

📁 文件: utils\path_manager.py
📍 行号: 196
🔍 类型: path_pattern
📂 路径: backup
💬 代码: db_type: 数据库类型（'backup', 'export'）...

📁 文件: utils\path_manager.py
📍 行号: 196
🔍 类型: path_pattern
📂 路径: export
💬 代码: db_type: 数据库类型（'backup', 'export'）...

📁 文件: utils\path_manager.py
📍 行号: 288
🔍 类型: path_pattern
📂 路径: backups
💬 代码: return self.get_file_path('backups', backup_name)...

📁 文件: utils\path_manager.py
📍 行号: 300
🔍 类型: path_pattern
📂 路径: exports
💬 代码: export_dir = self.get_path('exports') / export_type...

📁 文件: utils\path_manager.py
📍 行号: 313
🔍 类型: path_pattern
📂 路径: temp
💬 代码: temp_dir = self.get_path('temp')...

📁 文件: views\inventory_management\analysis_dialog.py
📍 行号: 74
🔍 类型: path_pattern
📂 路径: exportBtn
💬 代码: export_btn.setObjectName("exportBtn")...

📁 文件: views\system_management\system_management_widget.py
📍 行号: 591
🔍 类型: path_pattern
📂 路径: {backup_time}
💬 代码: f"确定要删除备份文件 '{backup_time}' 吗？\n此操作不可撤销！",...
