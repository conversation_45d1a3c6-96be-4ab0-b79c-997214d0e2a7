#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
武汉总厂仓库管理系统 - 启动入口
"""

import sys
import os
from PyQt6.QtWidgets import QApplication
from PyQt6.QtCore import Qt
from PyQt6.QtGui import QPalette

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# 初始化路径管理器
from utils.path_manager import get_path_manager
path_manager = get_path_manager()

# 设置环境变量禁用Qt警告和深色边框
os.environ['QT_LOGGING_RULES'] = 'qt.qpa.window.debug=false;qt.qpa.window.warning=false'
os.environ['QT_ENABLE_HIGHDPI_SCALING'] = '1'
os.environ['QT_AUTO_SCREEN_SCALE_FACTOR'] = '1'
os.environ['QT_SCALE_FACTOR'] = '1'

def main():
    try:
        print("启动WMS系统...")

        # 创建应用程序实例
        app = QApplication(sys.argv)

        # 设置应用程序属性
        app.setApplicationName("武汉总厂仓库管理系统")
        app.setApplicationVersion("1.0.0")
        app.setOrganizationName("武汉总厂")
        
        # 禁用深色主题检测，强制使用浅色主题
        app.setStyle('Fusion')
        
        # 设置浅色调色板
        palette = QPalette()
        palette.setColor(QPalette.ColorRole.Window, Qt.GlobalColor.white)
        palette.setColor(QPalette.ColorRole.WindowText, Qt.GlobalColor.black)
        palette.setColor(QPalette.ColorRole.Base, Qt.GlobalColor.white)
        palette.setColor(QPalette.ColorRole.AlternateBase, Qt.GlobalColor.lightGray)
        palette.setColor(QPalette.ColorRole.ToolTipBase, Qt.GlobalColor.white)
        palette.setColor(QPalette.ColorRole.ToolTipText, Qt.GlobalColor.black)
        palette.setColor(QPalette.ColorRole.Text, Qt.GlobalColor.black)
        palette.setColor(QPalette.ColorRole.Button, Qt.GlobalColor.lightGray)
        palette.setColor(QPalette.ColorRole.ButtonText, Qt.GlobalColor.black)
        palette.setColor(QPalette.ColorRole.BrightText, Qt.GlobalColor.red)
        palette.setColor(QPalette.ColorRole.Link, Qt.GlobalColor.blue)
        palette.setColor(QPalette.ColorRole.Highlight, Qt.GlobalColor.blue)
        palette.setColor(QPalette.ColorRole.HighlightedText, Qt.GlobalColor.white)
        app.setPalette(palette)

        # 导入窗口类和服务
        from views.auth.login_window import LoginWindow
        from views.main_window import MainWindow
        from services.log_service import init_application_log

        # 初始化日志服务
        log_service = init_application_log()
        log_service.write_log("应用程序启动", "INFO")
    
        # 创建登录窗口
        login_window = LoginWindow()

        # 创建主窗口（初始不显示）
        main_window = None

        # 处理登录成功
        def on_login_success(username):
            nonlocal main_window
            try:
                log_service.write_log(f"用户 {username} 登录成功", "INFO")

                # 隐藏登录窗口
                login_window.hide()

                # 创建并显示主窗口

                main_window = MainWindow(username)
                main_window.show()

                # 处理登出
                def on_logout():
                    log_service.write_log(f"用户 {username} 登出系统", "INFO")
                    main_window.close()
                    # 清空密码并重新显示登录窗口
                    login_window.password_input.clear()
                    login_window.show()

                # 连接登出信号
                main_window.logout_signal.connect(on_logout)

            except Exception as e:
                print(f"创建主窗口时发生错误: {e}")
                import traceback
                traceback.print_exc()

        # 连接登录成功信号
        login_window.login_success.connect(on_login_success)

        # 显示登录窗口
        login_window.show()

        # 运行应用程序
        return app.exec()

    except Exception as e:
        print(f"应用程序启动失败: {e}")
        import traceback
        traceback.print_exc()
        return 1

if __name__ == "__main__":
    sys.exit(main())