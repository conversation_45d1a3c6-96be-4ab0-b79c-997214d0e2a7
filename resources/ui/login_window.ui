<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>LoginWindow</class>
 <widget class="QMainWindow" name="LoginWindow">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>1000</width>
    <height>700</height>
   </rect>
  </property>
  <property name="minimumSize">
   <size>
    <width>1000</width>
    <height>700</height>
   </size>
  </property>
  <property name="maximumSize">
   <size>
    <width>1000</width>
    <height>700</height>
   </size>
  </property>
  <property name="windowTitle">
   <string>武汉总厂仓库管理系统 - 登录</string>
  </property>
  <property name="styleSheet">
   <string>QMainWindow {
    background-color: transparent;
}</string>
  </property>
  <widget class="QWidget" name="centralwidget">
   <layout class="QHBoxLayout" name="main_layout">
    <property name="spacing">
     <number>0</number>
    </property>
    <property name="leftMargin">
     <number>0</number>
    </property>
    <property name="topMargin">
     <number>0</number>
    </property>
    <property name="rightMargin">
     <number>0</number>
    </property>
    <property name="bottomMargin">
     <number>0</number>
    </property>
    <item>
     <widget class="QWidget" name="left_spacer">
      <property name="minimumSize">
       <size>
        <width>600</width>
        <height>0</height>
       </size>
      </property>
      <property name="maximumSize">
       <size>
        <width>600</width>
        <height>16777215</height>
       </size>
      </property>
      <property name="styleSheet">
       <string>background: transparent;</string>
      </property>
     </widget>
    </item>
    <item>
     <widget class="QWidget" name="right_widget">
      <property name="minimumSize">
       <size>
        <width>400</width>
        <height>0</height>
       </size>
      </property>
      <property name="maximumSize">
       <size>
        <width>400</width>
        <height>16777215</height>
       </size>
      </property>
      <layout class="QVBoxLayout" name="final_layout">
       <property name="spacing">
        <number>0</number>
       </property>
       <property name="leftMargin">
        <number>0</number>
       </property>
       <property name="topMargin">
        <number>0</number>
       </property>
       <property name="rightMargin">
        <number>0</number>
       </property>
       <property name="bottomMargin">
        <number>0</number>
       </property>
       <item>
        <widget class="QWidget" name="top_widget">
         <property name="minimumSize">
          <size>
           <width>0</width>
           <height>60</height>
          </size>
         </property>
         <property name="maximumSize">
          <size>
           <width>16777215</width>
           <height>60</height>
          </size>
         </property>
         <layout class="QHBoxLayout" name="top_layout">
          <property name="leftMargin">
           <number>20</number>
          </property>
          <property name="topMargin">
           <number>15</number>
          </property>
          <property name="rightMargin">
           <number>20</number>
          </property>
          <property name="bottomMargin">
           <number>10</number>
          </property>
          <item>
           <widget class="QLabel" name="welcome_label">
            <property name="text">
             <string>欢迎使用武汉总厂仓库管理系统</string>
            </property>
            <property name="alignment">
             <set>Qt::AlignCenter</set>
            </property>
            <property name="styleSheet">
             <string>font-size: 20px;
font-weight: bold;
color: #1976D2;
background: rgba(255, 255, 255, 0.9);
border-radius: 8px;
padding: 10px 20px;
border: 1px solid rgba(33, 150, 243, 0.3);</string>
            </property>
           </widget>
          </item>
         </layout>
        </widget>
       </item>
       <item>
        <widget class="QWidget" name="login_container">
         <property name="minimumSize">
          <size>
           <width>400</width>
           <height>0</height>
          </size>
         </property>
         <property name="maximumSize">
          <size>
           <width>400</width>
           <height>16777215</height>
          </size>
         </property>
         <property name="styleSheet">
          <string>background: transparent;
border-radius: 15px;</string>
         </property>
         <layout class="QVBoxLayout" name="container_layout">
          <property name="leftMargin">
           <number>40</number>
          </property>
          <property name="topMargin">
           <number>60</number>
          </property>
          <property name="rightMargin">
           <number>40</number>
          </property>
          <property name="bottomMargin">
           <number>40</number>
          </property>
          <item>
           <spacer name="top_spacer">
            <property name="orientation">
             <enum>Qt::Vertical</enum>
            </property>
            <property name="sizeHint" stdset="0">
             <size>
              <width>20</width>
              <height>40</height>
             </size>
            </property>
           </spacer>
          </item>
          <item alignment="Qt::AlignHCenter">
           <widget class="QFrame" name="login_panel">
            <property name="minimumSize">
             <size>
              <width>320</width>
              <height>480</height>
             </size>
            </property>
            <property name="maximumSize">
             <size>
              <width>320</width>
              <height>480</height>
             </size>
            </property>
            <property name="frameShape">
             <enum>QFrame::StyledPanel</enum>
            </property>
            <property name="frameShadow">
             <enum>QFrame::Raised</enum>
            </property>
            <property name="styleSheet">
             <string>background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
    stop:0 rgba(255, 255, 255, 0.95),
    stop:1 rgba(240, 248, 255, 0.95));
border-radius: 15px;
border: 1px solid rgba(33, 150, 243, 0.3);</string>
            </property>
            <layout class="QVBoxLayout" name="panel_layout">
             <property name="spacing">
              <number>20</number>
             </property>
             <property name="leftMargin">
              <number>30</number>
             </property>
             <property name="topMargin">
              <number>40</number>
             </property>
             <property name="rightMargin">
              <number>30</number>
             </property>
             <property name="bottomMargin">
              <number>30</number>
             </property>
             <item>
              <widget class="QLabel" name="title_label">
               <property name="text">
                <string>系统登录</string>
               </property>
               <property name="alignment">
                <set>Qt::AlignCenter</set>
               </property>
               <property name="styleSheet">
                <string>font-size: 24px;
font-weight: bold;
color: #1976D2;
margin-bottom: 10px;</string>
               </property>
              </widget>
             </item>
             <item>
              <widget class="QLineEdit" name="username_input">
               <property name="placeholderText">
                <string>请输入用户名</string>
               </property>
               <property name="text">
                <string>admin</string>
               </property>
               <property name="styleSheet">
                <string>padding: 12px 15px;
border: 2px solid #E3F2FD;
border-radius: 8px;
font-size: 14px;
background-color: rgba(255, 255, 255, 0.9);
color: #333;</string>
               </property>
              </widget>
             </item>
             <item>
              <widget class="QLineEdit" name="password_input">
               <property name="placeholderText">
                <string>请输入密码</string>
               </property>
               <property name="text">
                <string>123456</string>
               </property>
               <property name="echoMode">
                <enum>QLineEdit::Password</enum>
               </property>
               <property name="styleSheet">
                <string>padding: 12px 15px;
border: 2px solid #E3F2FD;
border-radius: 8px;
font-size: 14px;
background-color: rgba(255, 255, 255, 0.9);
color: #333;</string>
               </property>
              </widget>
             </item>
             <item>
              <widget class="QCheckBox" name="remember_checkbox">
               <property name="text">
                <string>记住密码</string>
               </property>
               <property name="checked">
                <bool>true</bool>
               </property>
               <property name="styleSheet">
                <string>color: #666;
font-size: 13px;</string>
               </property>
              </widget>
             </item>
             <item>
              <widget class="QPushButton" name="login_button">
               <property name="text">
                <string>登录</string>
               </property>
               <property name="styleSheet">
                <string>background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
    stop:0 #2196F3,
    stop:1 #1976D2);
color: white;
border: none;
border-radius: 8px;
padding: 12px;
font-size: 16px;
font-weight: bold;</string>
               </property>
              </widget>
             </item>
             <item>
              <widget class="QPushButton" name="register_button">
               <property name="text">
                <string>注册新用户</string>
               </property>
               <property name="styleSheet">
                <string>background: transparent;
color: #2196F3;
border: 2px solid #2196F3;
border-radius: 8px;
padding: 10px;
font-size: 14px;</string>
               </property>
              </widget>
             </item>
             <item>
              <widget class="QLabel" name="forgot_label">
               <property name="text">
                <string>&lt;a href=&quot;#&quot; style=&quot;color: #2196F3; text-decoration: none;&quot;&gt;忘记密码？&lt;/a&gt;</string>
               </property>
               <property name="alignment">
                <set>Qt::AlignCenter</set>
               </property>
               <property name="styleSheet">
                <string>color: #2196F3;
font-size: 13px;</string>
               </property>
              </widget>
             </item>
            </layout>
           </widget>
          </item>
          <item>
           <widget class="QLabel" name="db_status_label">
            <property name="text">
             <string>正在检查数据库连接...</string>
            </property>
            <property name="alignment">
             <set>Qt::AlignCenter</set>
            </property>
            <property name="styleSheet">
             <string>color: #666;
font-size: 12px;
padding: 5px;</string>
            </property>
           </widget>
          </item>
          <item>
           <spacer name="bottom_spacer">
            <property name="orientation">
             <enum>Qt::Vertical</enum>
            </property>
            <property name="sizeHint" stdset="0">
             <size>
              <width>20</width>
              <height>40</height>
             </size>
            </property>
           </spacer>
          </item>
         </layout>
        </widget>
       </item>
      </layout>
     </widget>
    </item>
   </layout>
  </widget>
 </widget>
 <resources/>
 <connections/>
</ui> 