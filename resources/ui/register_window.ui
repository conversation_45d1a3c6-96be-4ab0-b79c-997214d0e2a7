<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>RegisterWindow</class>
 <widget class="QMainWindow" name="RegisterWindow">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>450</width>
    <height>600</height>
   </rect>
  </property>
  <property name="sizePolicy">
   <sizepolicy hsizetype="Fixed" vsizetype="Fixed">
    <horstretch>0</horstretch>
    <verstretch>0</verstretch>
   </sizepolicy>
  </property>
  <property name="minimumSize">
   <size>
    <width>450</width>
    <height>600</height>
   </size>
  </property>
  <property name="maximumSize">
   <size>
    <width>450</width>
    <height>900</height>
   </size>
  </property>
  <property name="windowTitle">
   <string>用户注册</string>
  </property>
  <property name="styleSheet">
   <string>QMainWindow {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
        stop:0 #E3F2FD,
        stop:1 #BBDEFB);
}</string>
  </property>
  <widget class="QWidget" name="centralwidget">
   <layout class="QVBoxLayout" name="main_layout">
    <property name="spacing">
     <number>20</number>
    </property>
    <property name="leftMargin">
     <number>30</number>
    </property>
    <property name="topMargin">
     <number>30</number>
    </property>
    <property name="rightMargin">
     <number>30</number>
    </property>
    <property name="bottomMargin">
     <number>30</number>
    </property>
    <item>
     <widget class="QFrame" name="register_panel">
      <property name="styleSheet">
       <string>background: rgba(255, 255, 255, 0.95);
border-radius: 15px;
border: 1px solid rgba(33, 150, 243, 0.3);</string>
      </property>
      <property name="frameShape">
       <enum>QFrame::Shape::StyledPanel</enum>
      </property>
      <property name="frameShadow">
       <enum>QFrame::Shadow::Raised</enum>
      </property>
      <layout class="QGridLayout" name="gridLayout">
       <property name="leftMargin">
        <number>30</number>
       </property>
       <property name="topMargin">
        <number>30</number>
       </property>
       <property name="rightMargin">
        <number>30</number>
       </property>
       <property name="bottomMargin">
        <number>30</number>
       </property>
       <item row="4" column="0">
        <widget class="QLabel" name="realname_label">
         <property name="styleSheet">
          <string notr="true">font-size: 14px;
font-weight: bold;
color: #424242;
margin-bottom: 5px;
border: none;</string>
         </property>
         <property name="text">
          <string>真实姓名:</string>
         </property>
        </widget>
       </item>
       <item row="6" column="1">
        <widget class="QLineEdit" name="password_input">
         <property name="styleSheet">
          <string>padding: 12px 15px;
border: none;
border-radius: 8px;
font-size: 14px;
background-color: #F5F5F5;
color: #333;
margin-bottom: 5px;</string>
         </property>
         <property name="echoMode">
          <enum>QLineEdit::EchoMode::Password</enum>
         </property>
         <property name="placeholderText">
          <string>请输入密码</string>
         </property>
        </widget>
       </item>
       <item row="4" column="1">
        <widget class="QLineEdit" name="realname_input">
         <property name="styleSheet">
          <string>padding: 12px 15px;
border: none;
border-radius: 8px;
font-size: 14px;
background-color: #F5F5F5;
color: #333;
margin-bottom: 5px;</string>
         </property>
         <property name="placeholderText">
          <string>请输入真实姓名</string>
         </property>
        </widget>
       </item>
       <item row="6" column="0">
        <widget class="QLabel" name="password_label">
         <property name="styleSheet">
          <string notr="true">font-size: 14px;
font-weight: bold;
color: #424242;
margin-bottom: 5px;
border: none;</string>
         </property>
         <property name="text">
          <string>密码:</string>
         </property>
        </widget>
       </item>
       <item row="1" column="0">
        <widget class="QLabel" name="username_label">
         <property name="styleSheet">
          <string notr="true">font-size: 14px;
font-weight: bold;
color: #424242;
margin-bottom: 5px;
border: none;</string>
         </property>
         <property name="text">
          <string>用户名:</string>
         </property>
        </widget>
       </item>
       <item row="10" column="1">
        <widget class="QLineEdit" name="phone_input">
         <property name="styleSheet">
          <string>padding: 12px 15px;
border: none;
border-radius: 8px;
font-size: 14px;
background-color: #F5F5F5;
color: #333;
margin-bottom: 5px;</string>
         </property>
         <property name="placeholderText">
          <string>请输入电话号码</string>
         </property>
        </widget>
       </item>
       <item row="8" column="0">
        <widget class="QLabel" name="confirm_label">
         <property name="styleSheet">
          <string notr="true">font-size: 14px;
font-weight: bold;
color: #424242;
margin-bottom: 5px;
border: none;</string>
         </property>
         <property name="text">
          <string>确认密码:</string>
         </property>
        </widget>
       </item>
       <item row="0" column="0" colspan="2">
        <widget class="QLabel" name="title_label">
         <property name="font">
          <font>
           <family>宋体</family>
           <pointsize>-1</pointsize>
           <bold>true</bold>
          </font>
         </property>
         <property name="styleSheet">
          <string>font-size: 24px;
font-weight: bold;
color: #1976D2;
margin-bottom: 10px;
padding: 10px;</string>
         </property>
         <property name="text">
          <string>用户注册</string>
         </property>
         <property name="alignment">
          <set>Qt::AlignmentFlag::AlignCenter</set>
         </property>
        </widget>
       </item>
       <item row="12" column="1">
        <widget class="QComboBox" name="role_combo">
         <property name="styleSheet">
          <string>padding: 12px 15px;
border: none;
border-radius: 8px;
font-size: 14px;
background-color: #F5F5F5;
color: #333;
margin-bottom: 5px;</string>
         </property>
         <item>
          <property name="text">
           <string>查看者</string>
          </property>
         </item>
         <item>
          <property name="text">
           <string>操作员</string>
          </property>
         </item>
         <item>
          <property name="text">
           <string>管理员</string>
          </property>
         </item>
        </widget>
       </item>
       <item row="12" column="0">
        <widget class="QLabel" name="role_label">
         <property name="styleSheet">
          <string notr="true">font-size: 14px;
font-weight: bold;
color: #424242;
margin-bottom: 5px;
border: none;</string>
         </property>
         <property name="lineWidth">
          <number>0</number>
         </property>
         <property name="text">
          <string>用户权限:</string>
         </property>
        </widget>
       </item>
       <item row="10" column="0">
        <widget class="QLabel" name="phone_label">
         <property name="styleSheet">
          <string notr="true">font-size: 14px;
font-weight: bold;
color: #424242;
margin-bottom: 5px;
border: none;</string>
         </property>
         <property name="text">
          <string>电话号码:</string>
         </property>
        </widget>
       </item>
       <item row="1" column="1">
        <widget class="QLineEdit" name="username_input">
         <property name="styleSheet">
          <string>padding: 12px 15px;
border: none;
border-radius: 8px;
font-size: 14px;
background-color: #F5F5F5;
color: #333;
margin-bottom: 5px;</string>
         </property>
         <property name="placeholderText">
          <string>请输入用户名</string>
         </property>
        </widget>
       </item>
       <item row="13" column="0" colspan="2">
        <layout class="QHBoxLayout" name="button_layout">
         <property name="spacing">
          <number>15</number>
         </property>
         <item>
          <widget class="QPushButton" name="register_button">
           <property name="styleSheet">
            <string>background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
    stop:0 #2196F3,
    stop:1 #1976D2);
color: white;
border: none;
border-radius: 8px;
padding: 12px 20px;
font-size: 16px;
font-weight: bold;
min-width: 100px;</string>
           </property>
           <property name="text">
            <string>注册</string>
           </property>
          </widget>
         </item>
         <item>
          <widget class="QPushButton" name="back_button">
           <property name="styleSheet">
            <string>background: transparent;
color: #666;
border: 2px solid #CCC;
border-radius: 8px;
padding: 12px 20px;
font-size: 14px;
min-width: 100px;</string>
           </property>
           <property name="text">
            <string>返回登录</string>
           </property>
          </widget>
         </item>
        </layout>
       </item>
       <item row="8" column="1">
        <widget class="QLineEdit" name="confirm_password_input">
         <property name="styleSheet">
          <string>padding: 12px 15px;
border: none;
border-radius: 8px;
font-size: 14px;
background-color: #F5F5F5;
color: #333;
margin-bottom: 5px;</string>
         </property>
         <property name="echoMode">
          <enum>QLineEdit::EchoMode::Password</enum>
         </property>
         <property name="placeholderText">
          <string>请确认密码</string>
         </property>
        </widget>
       </item>
      </layout>
     </widget>
    </item>
   </layout>
  </widget>
 </widget>
 <resources/>
 <connections/>
</ui>
