#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
对话上下文记忆功能
实现智能对话记忆管理，支持短期和长期记忆
"""

import json
from datetime import datetime, timedelta
from typing import List, Dict, Any
import uuid

class ConversationMemory:
    def __init__(self, memory_client, max_short_term=20):
        """
        初始化对话记忆管理器
        
        Args:
            memory_client: Mem0记忆客户端
            max_short_term: 短期记忆最大条数
        """
        self.memory = memory_client
        self.max_short_term = max_short_term
        self.conversation_buffer = []  # 短期对话缓存
        self.session_id = str(uuid.uuid4())
        
    def add_message(self, role: str, content: str, user_id: str, 
                   importance: int = 2, auto_extract: bool = True):
        """
        添加对话消息
        
        Args:
            role: 角色 (user/assistant/system)
            content: 消息内容
            user_id: 用户ID
            importance: 重要性 (1-5)
            auto_extract: 是否自动提取重要信息
        """
        message = {
            "id": str(uuid.uuid4()),
            "role": role,
            "content": content,
            "timestamp": datetime.now().isoformat(),
            "session_id": self.session_id,
            "importance": importance
        }
        
        # 添加到短期缓存
        self.conversation_buffer.append(message)
        
        # 保持短期记忆在限制范围内
        if len(self.conversation_buffer) > self.max_short_term:
            self.conversation_buffer.pop(0)
        
        # 自动提取重要信息到长期记忆
        if auto_extract and self.is_important_message(content, role):
            self.extract_to_long_term(message, user_id)
    
    def is_important_message(self, content: str, role: str) -> bool:
        """
        判断消息是否重要，需要存储到长期记忆
        
        Args:
            content: 消息内容
            role: 角色
            
        Returns:
            bool: 是否重要
        """
        # 用户消息中的重要关键词
        important_keywords = [
            "我是", "我叫", "我的名字", "我喜欢", "我不喜欢", "我的偏好",
            "记住", "重要", "关键", "必须", "一定要", "千万不要",
            "我的工作", "我在", "我住在", "我的地址", "我的电话",
            "项目", "任务", "截止时间", "会议", "约定", "计划"
        ]
        
        # 助手消息中的重要信息
        assistant_keywords = [
            "建议您", "推荐", "重要提醒", "注意事项", "关键信息",
            "总结", "结论", "要点", "核心"
        ]
        
        if role == "user":
            return any(keyword in content for keyword in important_keywords)
        elif role == "assistant":
            return any(keyword in content for keyword in assistant_keywords) or len(content) > 200
        
        return False
    
    def extract_to_long_term(self, message: Dict[str, Any], user_id: str):
        """
        提取重要信息到长期记忆
        
        Args:
            message: 消息对象
            user_id: 用户ID
        """
        try:
            # 构建记忆内容
            memory_content = f"[{message['role']}] {message['content']}"
            
            # 存储到长期记忆
            self.memory.add(
                memory_content,
                user_id=user_id,
                metadata={
                    "source": "conversation",
                    "role": message['role'],
                    "session_id": message['session_id'],
                    "importance": message['importance'],
                    "extracted_at": datetime.now().isoformat(),
                    "original_timestamp": message['timestamp']
                }
            )
            
        except Exception as e:
            print(f"提取到长期记忆失败: {e}")
    
    def get_conversation_context(self, user_id: str, query: str = "", 
                               include_history: bool = True) -> Dict[str, Any]:
        """
        获取对话上下文
        
        Args:
            user_id: 用户ID
            query: 当前查询（用于搜索相关记忆）
            include_history: 是否包含历史记忆
            
        Returns:
            Dict: 上下文信息
        """
        context = {
            "short_term_memory": self.conversation_buffer,
            "relevant_memories": [],
            "user_profile": {},
            "session_info": {
                "session_id": self.session_id,
                "message_count": len(self.conversation_buffer),
                "start_time": self.conversation_buffer[0]["timestamp"] if self.conversation_buffer else None
            }
        }
        
        if include_history and query:
            # 搜索相关的长期记忆
            try:
                search_result = self.memory.search(query, user_id=user_id, limit=5)
                if isinstance(search_result, dict) and 'results' in search_result:
                    context["relevant_memories"] = search_result['results']
                elif isinstance(search_result, list):
                    context["relevant_memories"] = search_result
            except Exception as e:
                print(f"搜索相关记忆失败: {e}")
        
        # 获取用户画像
        context["user_profile"] = self.get_user_profile(user_id)
        
        return context
    
    def get_user_profile(self, user_id: str) -> Dict[str, Any]:
        """
        获取用户画像
        
        Args:
            user_id: 用户ID
            
        Returns:
            Dict: 用户画像信息
        """
        profile = {
            "preferences": [],
            "personal_info": [],
            "work_info": [],
            "contact_info": []
        }
        
        try:
            # 搜索用户相关的个人信息
            personal_queries = ["我是", "我叫", "我的", "我喜欢", "我不喜欢"]
            
            for query in personal_queries:
                search_result = self.memory.search(query, user_id=user_id, limit=3)
                if isinstance(search_result, dict) and 'results' in search_result:
                    memories = search_result['results']
                elif isinstance(search_result, list):
                    memories = search_result
                else:
                    continue
                
                for mem in memories:
                    if isinstance(mem, dict) and 'memory' in mem:
                        content = mem['memory']
                        
                        # 分类信息
                        if any(word in content for word in ["喜欢", "偏好", "爱好"]):
                            profile["preferences"].append(content)
                        elif any(word in content for word in ["我是", "我叫", "年龄", "性别"]):
                            profile["personal_info"].append(content)
                        elif any(word in content for word in ["工作", "职业", "公司", "项目"]):
                            profile["work_info"].append(content)
                        elif any(word in content for word in ["电话", "邮箱", "地址", "联系"]):
                            profile["contact_info"].append(content)
            
        except Exception as e:
            print(f"获取用户画像失败: {e}")
        
        return profile
    
    def summarize_conversation(self, user_id: str) -> str:
        """
        总结当前对话
        
        Args:
            user_id: 用户ID
            
        Returns:
            str: 对话总结
        """
        if not self.conversation_buffer:
            return "暂无对话内容"
        
        # 简单的对话总结
        user_messages = [msg for msg in self.conversation_buffer if msg["role"] == "user"]
        assistant_messages = [msg for msg in self.conversation_buffer if msg["role"] == "assistant"]
        
        summary = f"""
对话总结：
- 会话ID: {self.session_id}
- 消息总数: {len(self.conversation_buffer)}
- 用户消息: {len(user_messages)}条
- 助手回复: {len(assistant_messages)}条
- 开始时间: {self.conversation_buffer[0]['timestamp']}
- 最新时间: {self.conversation_buffer[-1]['timestamp']}

主要话题: {self.extract_main_topics()}
        """
        
        return summary.strip()
    
    def extract_main_topics(self) -> str:
        """
        提取对话主要话题
        
        Returns:
            str: 主要话题
        """
        if not self.conversation_buffer:
            return "无"
        
        # 简单的关键词提取
        all_content = " ".join([msg["content"] for msg in self.conversation_buffer])
        
        # 常见话题关键词
        topic_keywords = {
            "工作": ["工作", "项目", "任务", "开发", "设计", "测试"],
            "技术": ["技术", "编程", "代码", "系统", "数据库", "API"],
            "生活": ["生活", "家庭", "朋友", "爱好", "旅行", "美食"],
            "学习": ["学习", "教程", "课程", "知识", "技能", "培训"],
            "购物": ["购买", "价格", "产品", "推荐", "选择", "比较"]
        }
        
        detected_topics = []
        for topic, keywords in topic_keywords.items():
            if any(keyword in all_content for keyword in keywords):
                detected_topics.append(topic)
        
        return ", ".join(detected_topics) if detected_topics else "日常对话"
    
    def clear_session(self):
        """清除当前会话"""
        self.conversation_buffer.clear()
        self.session_id = str(uuid.uuid4())
    
    def export_conversation(self) -> Dict[str, Any]:
        """
        导出对话记录
        
        Returns:
            Dict: 对话记录
        """
        return {
            "session_id": self.session_id,
            "messages": self.conversation_buffer,
            "summary": self.summarize_conversation("export"),
            "exported_at": datetime.now().isoformat()
        }

# 测试函数
def test_conversation_memory():
    """测试对话记忆功能"""
    print("🧪 测试对话记忆功能")
    
    # 模拟memory对象
    class MockMemory:
        def __init__(self):
            self.memories = []
        
        def add(self, content, user_id, metadata=None):
            memory = {
                'id': str(uuid.uuid4()),
                'memory': content,
                'user_id': user_id,
                'metadata': metadata,
                'created_at': datetime.now().isoformat()
            }
            self.memories.append(memory)
            print(f"存储长期记忆: {content[:50]}...")
        
        def search(self, query, user_id, limit=5):
            # 简单的模拟搜索
            results = [mem for mem in self.memories if query.lower() in mem['memory'].lower()]
            return {'results': results[:limit]}
    
    mock_memory = MockMemory()
    conv_memory = ConversationMemory(mock_memory)
    
    # 模拟对话
    print("\n🗣️ 模拟对话场景:")
    
    # 用户介绍自己
    conv_memory.add_message("user", "你好，我是张三，我在一家科技公司工作", "test_user")
    conv_memory.add_message("assistant", "你好张三！很高兴认识你。能告诉我更多关于你工作的信息吗？", "test_user")
    
    # 用户分享偏好
    conv_memory.add_message("user", "我喜欢编程，特别是Python开发，我不喜欢加班", "test_user")
    conv_memory.add_message("assistant", "了解了，Python是很棒的语言！工作生活平衡确实很重要。", "test_user")
    
    # 用户询问技术问题
    conv_memory.add_message("user", "你能帮我推荐一些Python学习资源吗？", "test_user")
    conv_memory.add_message("assistant", "当然可以！基于你的编程背景，我推荐以下资源...", "test_user")
    
    # 获取对话上下文
    print("\n📋 获取对话上下文:")
    context = conv_memory.get_conversation_context("test_user", "Python")
    
    print(f"短期记忆数量: {len(context['short_term_memory'])}")
    print(f"相关长期记忆: {len(context['relevant_memories'])}")
    print(f"用户画像: {context['user_profile']}")
    
    # 对话总结
    print("\n📊 对话总结:")
    summary = conv_memory.summarize_conversation("test_user")
    print(summary)
    
    # 显示存储的长期记忆
    print(f"\n💾 存储的长期记忆数量: {len(mock_memory.memories)}")
    for i, mem in enumerate(mock_memory.memories, 1):
        print(f"{i}. {mem['memory']}")
        print(f"   来源: {mem['metadata']['source']}")
        print(f"   角色: {mem['metadata']['role']}")
        print()

if __name__ == "__main__":
    test_conversation_memory()
