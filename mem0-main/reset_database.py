#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
重置ChromaDB数据库 - 修复版本兼容性问题
"""

import os
import sys
import shutil
import time
import psutil
from pathlib import Path

def stop_dashboard_service():
    """停止仪表盘服务"""
    print("🛑 正在停止仪表盘服务...")

    # 查找占用8510端口的进程
    for proc in psutil.process_iter(['pid', 'name']):
        try:
            connections = proc.connections()
            if connections:
                for conn in connections:
                    if hasattr(conn, 'laddr') and conn.laddr.port == 8510:
                        print(f"🔄 终止进程 {proc.pid} ({proc.info['name']})")
                        proc.terminate()
                        proc.wait(timeout=5)
                        print(f"✅ 进程 {proc.pid} 已终止")
                        return True
        except (psutil.NoSuchProcess, psutil.AccessDenied, psutil.TimeoutExpired):
            pass

    print("ℹ️ 未找到占用8510端口的进程")
    return False

def backup_database():
    """备份当前数据库"""
    chroma_db_path = Path("chroma_db")
    
    if chroma_db_path.exists():
        timestamp = int(time.time())
        backup_path = Path(f"chroma_db_backup_{timestamp}")
        
        print(f"💾 备份数据库到 {backup_path}")
        try:
            shutil.copytree(chroma_db_path, backup_path)
            print("✅ 数据库备份完成")
            return backup_path
        except Exception as e:
            print(f"❌ 备份失败: {e}")
            return None
    else:
        print("ℹ️ 数据库目录不存在，无需备份")
        return None

def remove_database():
    """删除旧数据库"""
    chroma_db_path = Path("chroma_db")
    
    if chroma_db_path.exists():
        print("🗑️ 删除旧数据库...")
        try:
            # 等待一段时间确保进程完全释放文件
            time.sleep(3)
            shutil.rmtree(chroma_db_path)
            print("✅ 旧数据库已删除")
            return True
        except Exception as e:
            print(f"❌ 删除失败: {e}")
            print("请手动删除 chroma_db 目录后重新运行")
            return False
    else:
        print("ℹ️ 数据库目录不存在")
        return True

def test_new_database():
    """测试新数据库"""
    print("🧪 测试新数据库...")
    
    try:
        from local_config_simple import get_memory_client
        
        # 创建新的记忆客户端
        memory = get_memory_client()
        print("✅ 记忆客户端创建成功")
        
        # 添加测试记忆
        test_content = "这是重置数据库后的第一条测试记忆"
        result = memory.add(test_content, user_id="cursor_user")
        print(f"✅ 测试记忆添加成功: {result}")
        
        # 获取所有记忆
        memories = memory.get_all(user_id="cursor_user")
        print(f"✅ 获取记忆成功，共 {len(memories) if memories else 0} 条")
        
        if memories:
            first_memory = memories[0]
            print(f"📄 第一条记忆: {first_memory}")
            
            # 测试记忆ID格式
            if isinstance(first_memory, dict) and 'id' in first_memory:
                memory_id = first_memory['id']
                print(f"🆔 记忆ID: {memory_id}")
                
                # 测试获取单条记忆
                detail = memory.get(memory_id)
                print(f"✅ 获取记忆详情成功: {detail}")
                
                # 测试更新记忆
                update_result = memory.update(memory_id, "这是更新后的测试记忆内容")
                print(f"✅ 更新记忆成功: {update_result}")
                
                # 测试删除记忆
                delete_result = memory.delete(memory_id)
                print(f"✅ 删除记忆成功: {delete_result}")
                
                print("🎉 所有操作测试通过！")
                return True
            else:
                print("⚠️ 记忆格式异常，可能影响删除和编辑功能")
                return False
        else:
            print("⚠️ 未找到测试记忆")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🔧 ChromaDB数据库重置工具")
    print("=" * 50)
    
    # 切换到正确的目录
    script_dir = Path(__file__).parent
    os.chdir(script_dir)
    print(f"📁 工作目录: {os.getcwd()}")
    
    # 步骤1: 停止仪表盘服务
    stop_dashboard_service()
    
    # 步骤2: 备份数据库
    backup_path = backup_database()
    
    # 步骤3: 删除旧数据库
    if not remove_database():
        print("❌ 无法删除旧数据库，请手动删除后重新运行")
        return False
    
    # 步骤4: 测试新数据库
    if test_new_database():
        print("\n✅ 数据库重置成功！")
        print("🎯 删除和编辑功能现在应该可以正常工作了")
        
        if backup_path:
            print(f"💾 旧数据备份在: {backup_path}")
            
        return True
    else:
        print("\n❌ 数据库重置失败")
        
        # 如果有备份，询问是否恢复
        if backup_path:
            print(f"💾 可以从备份恢复: {backup_path}")
            
        return False

if __name__ == "__main__":
    success = main()
    
    if success:
        print("\n🚀 建议重新启动仪表盘服务:")
        print("   streamlit run dashboard_final.py --server.port 8510")
    else:
        print("\n❌ 重置失败，请检查错误信息")
