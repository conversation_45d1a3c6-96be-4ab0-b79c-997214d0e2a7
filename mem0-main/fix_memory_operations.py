#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复记忆系统的删除和编辑功能
"""

import sys
import os
from pathlib import Path

# 添加项目路径
sys.path.insert(0, str(Path(__file__).parent))

def fix_memory_operations():
    """修复记忆操作功能"""
    try:
        from local_config_simple import get_memory_client
        
        print("🔧 正在初始化记忆客户端...")
        memory = get_memory_client()
        
        print("📋 获取所有记忆...")
        memories = memory.get_all(user_id="cursor_user")
        
        if not memories:
            print("📭 没有找到记忆")
            return
            
        print(f"📊 找到 {len(memories)} 条记忆")
        
        for i, mem in enumerate(memories):
            print(f"\n记忆 {i+1}:")
            print(f"  类型: {type(mem)}")
            print(f"  内容: {mem}")
            
            if isinstance(mem, dict):
                print(f"  字典键: {list(mem.keys())}")
                if 'id' in mem:
                    print(f"  ID: {mem['id']}")
                if 'memory' in mem:
                    print(f"  记忆内容: {mem['memory']}")
                    
        # 测试搜索功能
        print("\n🔍 测试搜索功能...")
        search_results = memory.search("测试", user_id="cursor_user")
        print(f"搜索结果: {search_results}")
        
        # 如果有记忆，尝试获取第一条的详细信息
        if memories and isinstance(memories[0], dict) and 'id' in memories[0]:
            memory_id = memories[0]['id']
            print(f"\n📄 获取记忆详情 (ID: {memory_id})...")
            
            try:
                detail = memory.get(memory_id)
                print(f"详情: {detail}")
            except Exception as e:
                print(f"❌ 获取详情失败: {e}")
                
            # 测试更新功能
            print(f"\n✏️ 测试更新功能 (ID: {memory_id})...")
            try:
                update_result = memory.update(memory_id, "更新测试内容")
                print(f"更新结果: {update_result}")
            except Exception as e:
                print(f"❌ 更新失败: {e}")
                
            # 测试删除功能
            print(f"\n🗑️ 测试删除功能 (ID: {memory_id})...")
            try:
                delete_result = memory.delete(memory_id)
                print(f"删除结果: {delete_result}")
            except Exception as e:
                print(f"❌ 删除失败: {e}")
        
    except Exception as e:
        print(f"❌ 修复过程中出错: {e}")
        import traceback
        traceback.print_exc()

def test_basic_operations():
    """测试基本操作"""
    try:
        from local_config_simple import get_memory_client
        
        print("🧪 开始基本操作测试...")
        memory = get_memory_client()
        
        # 添加测试记忆
        print("➕ 添加测试记忆...")
        add_result = memory.add("这是一个测试记忆，用于验证删除和编辑功能", user_id="cursor_user")
        print(f"添加结果: {add_result}")
        
        # 获取所有记忆
        print("📋 获取所有记忆...")
        memories = memory.get_all(user_id="cursor_user")
        print(f"记忆总数: {len(memories) if memories else 0}")
        
        if memories:
            for i, mem in enumerate(memories):
                print(f"记忆 {i+1}: {mem}")
                
        return memories
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return None

if __name__ == "__main__":
    print("🔧 记忆系统修复工具")
    print("=" * 50)
    
    # 首先测试基本操作
    memories = test_basic_operations()
    
    print("\n" + "=" * 50)
    
    # 然后修复操作
    fix_memory_operations()
    
    print("\n✅ 修复完成")
