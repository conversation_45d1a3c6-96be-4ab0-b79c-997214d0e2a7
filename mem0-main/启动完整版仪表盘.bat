@echo off
chcp 65001 >nul
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                  Mem0完整版仪表盘启动                        ║
echo ║              智能端口管理 + 自动清理                         ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

echo 🔍 检查Ollama服务状态...
curl -s http://localhost:11434/api/tags >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Ollama服务未运行
    echo 💡 请先启动Ollama服务: ollama serve
    echo.
    pause
    exit /b 1
)
echo ✅ Ollama服务正常

echo.
echo 🧹 清理占用的端口...

REM 定义目标端口
set TARGET_PORT=8510

REM 查找并终止占用目标端口的进程
echo 🔍 检查端口 %TARGET_PORT% 是否被占用...
for /f "tokens=5" %%a in ('netstat -aon ^| findstr :%TARGET_PORT%') do (
    echo 🔄 发现进程 %%a 占用端口 %TARGET_PORT%，正在终止...
    taskkill /f /pid %%a >nul 2>&1
)

REM 更强力的进程清理
echo 🧹 清理所有相关进程...
taskkill /f /im "python.exe" /fi "WINDOWTITLE eq *streamlit*" >nul 2>&1
taskkill /f /im "streamlit.exe" >nul 2>&1

REM 清理所有Python进程中包含dashboard的
for /f "tokens=2" %%i in ('tasklist /fi "imagename eq python.exe" /fo csv ^| findstr /i "dashboard"') do (
    echo 🔄 终止dashboard进程 %%i
    taskkill /f /pid %%i >nul 2>&1
)

REM 强制清理端口占用
echo 🔧 强制清理端口占用...
for /f "tokens=5" %%a in ('netstat -aon ^| findstr :%TARGET_PORT%') do (
    echo 🔄 强制终止进程 %%a
    taskkill /f /pid %%a >nul 2>&1
)

REM 等待端口完全释放
echo ⏳ 等待端口完全释放...
timeout /t 5 /nobreak >nul

REM 验证端口是否释放
netstat -an | findstr :%TARGET_PORT% >nul
if %errorlevel% equ 0 (
    echo ⚠️ 端口仍被占用，尝试额外清理...
    timeout /t 3 /nobreak >nul
) else (
    echo ✅ 端口 %TARGET_PORT% 已完全释放
)

echo.
echo 🚀 启动Mem0完整版仪表盘...
echo 📱 仪表盘将在浏览器中打开: http://localhost:%TARGET_PORT%
echo.
echo 🎯 完整功能:
echo   • 📁 文件上传 - 支持多种格式文档
echo   • 📄 文档处理 - 智能提取关键信息  
echo   • 💬 对话记忆 - 智能上下文记忆
echo   • 🔍 智能搜索 - 语义搜索 + 高级过滤
echo   • 📝 记忆管理 - 完整的增删改查
echo   • 📈 数据分析 - 可视化图表分析
echo   • ⚙️ 系统设置 - 状态监控和配置
echo.
echo 🔄 启动中...

cd /d "%~dp0"
streamlit run dashboard_final.py --server.port %TARGET_PORT% --server.headless true

echo.
echo 🔄 如果启动失败，请检查:
echo   1. Ollama服务是否正常运行
echo   2. Python环境是否正确
echo   3. 依赖包是否完整安装
echo.
pause
