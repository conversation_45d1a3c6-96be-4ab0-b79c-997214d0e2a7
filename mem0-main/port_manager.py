#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
端口管理器 - 自动清理端口占用并启动仪表盘
"""

import subprocess
import time
import sys
import psutil
import socket
from pathlib import Path

class PortManager:
    def __init__(self, target_port=8510):
        self.target_port = target_port
        
    def is_port_in_use(self, port):
        """检查端口是否被占用"""
        with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
            try:
                s.bind(('localhost', port))
                return False
            except OSError:
                return True
    
    def kill_processes_on_port(self, port):
        """终止占用指定端口的进程"""
        killed_count = 0
        
        for proc in psutil.process_iter(['pid', 'name', 'connections']):
            try:
                connections = proc.info['connections']
                if connections:
                    for conn in connections:
                        if conn.laddr.port == port:
                            print(f"🔄 终止进程 {proc.info['name']} (PID: {proc.info['pid']})")
                            proc.terminate()
                            killed_count += 1
                            break
            except (psutil.NoSuchProcess, psutil.AccessDenied, psutil.ZombieProcess):
                pass
        
        return killed_count
    
    def kill_streamlit_processes(self):
        """终止所有Streamlit相关进程"""
        killed_count = 0
        
        for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
            try:
                cmdline = ' '.join(proc.info['cmdline']) if proc.info['cmdline'] else ''
                
                # 检查是否是Streamlit进程
                if ('streamlit' in proc.info['name'].lower() or 
                    'streamlit' in cmdline.lower() or
                    'dashboard' in cmdline.lower()):
                    
                    print(f"🧹 清理Streamlit进程 {proc.info['name']} (PID: {proc.info['pid']})")
                    proc.terminate()
                    killed_count += 1
                    
            except (psutil.NoSuchProcess, psutil.AccessDenied, psutil.ZombieProcess):
                pass
        
        return killed_count
    
    def wait_for_port_release(self, port, timeout=10):
        """等待端口释放"""
        start_time = time.time()
        
        while time.time() - start_time < timeout:
            if not self.is_port_in_use(port):
                return True
            time.sleep(0.5)
        
        return False
    
    def clean_and_prepare_port(self):
        """清理并准备端口"""
        print(f"🔍 检查端口 {self.target_port} 状态...")
        
        if self.is_port_in_use(self.target_port):
            print(f"⚠️ 端口 {self.target_port} 被占用，正在清理...")
            
            # 终止占用端口的进程
            killed = self.kill_processes_on_port(self.target_port)
            
            # 清理所有Streamlit进程
            streamlit_killed = self.kill_streamlit_processes()
            
            if killed > 0 or streamlit_killed > 0:
                print(f"🧹 已终止 {killed + streamlit_killed} 个进程")
                
                # 等待端口释放
                print("⏳ 等待端口释放...")
                if self.wait_for_port_release(self.target_port):
                    print(f"✅ 端口 {self.target_port} 已释放")
                    return True
                else:
                    print(f"❌ 端口 {self.target_port} 释放超时")
                    return False
            else:
                print("🔍 未找到占用进程，可能是系统延迟")
                time.sleep(2)
                return not self.is_port_in_use(self.target_port)
        else:
            print(f"✅ 端口 {self.target_port} 可用")
            return True
    
    def start_dashboard(self):
        """启动仪表盘"""
        dashboard_file = Path(__file__).parent / "dashboard_final.py"
        
        if not dashboard_file.exists():
            print(f"❌ 找不到仪表盘文件: {dashboard_file}")
            return False
        
        print(f"🚀 启动仪表盘在端口 {self.target_port}...")
        print(f"📱 访问地址: http://localhost:{self.target_port}")
        
        try:
            # 启动Streamlit
            cmd = [
                sys.executable, "-m", "streamlit", "run", 
                str(dashboard_file),
                "--server.port", str(self.target_port),
                "--server.headless", "true"
            ]
            
            subprocess.run(cmd, cwd=dashboard_file.parent)
            return True
            
        except KeyboardInterrupt:
            print("\n🛑 用户中断启动")
            return False
        except Exception as e:
            print(f"❌ 启动失败: {e}")
            return False

def main():
    """主函数"""
    print("🧠 Mem0完整版仪表盘启动器")
    print("=" * 50)
    
    # 检查Ollama服务
    print("🔍 检查Ollama服务...")
    try:
        import requests
        response = requests.get("http://localhost:11434/api/tags", timeout=5)
        if response.status_code == 200:
            print("✅ Ollama服务正常")
        else:
            print("❌ Ollama服务异常")
            return False
    except Exception:
        print("❌ Ollama服务未运行")
        print("💡 请先启动Ollama服务: ollama serve")
        return False
    
    # 端口管理
    port_manager = PortManager(8510)
    
    if port_manager.clean_and_prepare_port():
        print("\n🎯 端口准备完成，启动仪表盘...")
        return port_manager.start_dashboard()
    else:
        print("\n❌ 端口准备失败")
        return False

if __name__ == "__main__":
    success = main()
    if not success:
        print("\n🔧 启动失败，请检查系统状态")
        input("按回车键退出...")
        sys.exit(1)
