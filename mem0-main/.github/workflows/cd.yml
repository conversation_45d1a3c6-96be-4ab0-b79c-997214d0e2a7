name: Publish Python 🐍 distributions 📦 to PyPI and TestPyPI

on:
  release:
    types: [published]

jobs:
  build-n-publish:
    name: Build and publish Python 🐍 distributions 📦 to PyPI and TestPyPI
    runs-on: ubuntu-latest
    permissions:
      id-token: write
    steps:
      - uses: actions/checkout@v2

      - name: Set up Python
        uses: actions/setup-python@v2
        with:
          python-version: '3.11'

      - name: Install Hatch
        run: |
          pip install hatch

      - name: Install dependencies
        run: |
          hatch env create

      - name: Build a binary wheel and a source tarball
        run: |
          hatch build --clean

      # TODO: Needs to setup mem0 repo on Test PyPI
      # - name: Publish distribution 📦 to Test PyPI
      #   uses: pypa/gh-action-pypi-publish@release/v1
      #   with:
      #     repository_url: https://test.pypi.org/legacy/
      #     packages_dir: dist/

      - name: Publish distribution 📦 to PyPI
        if: startsWith(github.ref, 'refs/tags')
        uses: pypa/gh-action-pypi-publish@release/v1
        with:
          packages_dir: dist/
