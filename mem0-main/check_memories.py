#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查当前记忆系统状态
"""

from local_config_simple import get_memory_client

def check_memories():
    """检查记忆状态"""
    try:
        memory = get_memory_client()
        
        # 检查test_user的记忆
        memories = memory.get_all(user_id='test_user')
        results = memories.get('results', [])
        
        print(f"用户 test_user 的记忆数量: {len(results)}")
        print("\n最近的记忆:")
        
        for i, mem in enumerate(results[:5]):
            memory_text = mem.get('memory', 'N/A')
            metadata = mem.get('metadata', {})
            source = metadata.get('source', 'unknown')
            print(f"{i+1}. [{source}] {memory_text[:100]}...")
        
        # 检查其他用户
        print(f"\n检查其他可能的用户...")
        for user_id in ['user1', 'default', 'admin']:
            try:
                user_memories = memory.get_all(user_id=user_id)
                user_results = user_memories.get('results', [])
                if user_results:
                    print(f"用户 {user_id} 的记忆数量: {len(user_results)}")
            except:
                pass
        
        return len(results)
        
    except Exception as e:
        print(f"检查记忆失败: {e}")
        return 0

if __name__ == "__main__":
    check_memories()
