#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试记忆系统的所有操作功能
"""

import sys
from pathlib import Path

# 添加项目路径
sys.path.insert(0, str(Path(__file__).parent))

def test_memory_operations():
    """测试记忆操作功能"""
    try:
        from local_config_simple import get_memory_client
        
        print("🧪 开始记忆操作测试...")
        memory = get_memory_client()
        print("✅ 记忆客户端创建成功")
        
        # 添加测试记忆
        print("\n➕ 添加测试记忆...")
        test_content = "这是一个测试记忆，用于验证删除和编辑功能"
        result = memory.add(test_content, user_id="cursor_user")
        print(f"✅ 添加记忆成功: {result}")
        
        # 获取所有记忆
        print("\n📋 获取所有记忆...")
        memories = memory.get_all(user_id="cursor_user")
        print(f"✅ 获取记忆成功，共 {len(memories)} 条")
        
        if not memories:
            print("❌ 没有找到记忆")
            return False
            
        # 分析记忆数据结构
        print(f"\n📊 记忆数据分析:")
        print(f"  memories类型: {type(memories)}")
        print(f"  memories内容: {memories}")

        if isinstance(memories, dict):
            print(f"  字典键: {list(memories.keys())}")
            for key, value in memories.items():
                print(f"    {key}: {value} (类型: {type(value)})")
        elif isinstance(memories, list):
            for i, mem in enumerate(memories):
                print(f"  记忆 {i+1}:")
                print(f"    类型: {type(mem)}")
                print(f"    内容: {mem}")

        # 获取第一条记忆进行测试
        if isinstance(memories, list) and len(memories) > 0:
            first_memory = memories[0]
        elif isinstance(memories, dict) and 'results' in memories:
            # 如果memories是字典且包含results键
            results = memories['results']
            if isinstance(results, list) and len(results) > 0:
                first_memory = results[0]
            else:
                print("❌ results为空或不是列表")
                return False
        else:
            print("❌ 无法获取第一条记忆")
            return False
        
        if isinstance(first_memory, dict) and 'id' in first_memory:
            memory_id = first_memory['id']
            print(f"\n🆔 使用记忆ID进行测试: {memory_id}")
            
            # 测试获取单条记忆
            print("\n📄 测试获取记忆详情...")
            try:
                detail = memory.get(memory_id)
                print(f"✅ 获取记忆详情成功:")
                print(f"    类型: {type(detail)}")
                print(f"    内容: {detail}")
            except Exception as e:
                print(f"❌ 获取记忆详情失败: {e}")
            
            # 测试更新记忆
            print("\n✏️ 测试更新记忆...")
            try:
                new_content = "这是更新后的记忆内容 - 测试更新功能"
                update_result = memory.update(memory_id, new_content)
                print(f"✅ 更新记忆成功:")
                print(f"    类型: {type(update_result)}")
                print(f"    结果: {update_result}")
                
                # 验证更新是否成功
                updated_detail = memory.get(memory_id)
                print(f"✅ 验证更新成功: {updated_detail}")
                
            except Exception as e:
                print(f"❌ 更新记忆失败: {e}")
                import traceback
                traceback.print_exc()
            
            # 测试删除记忆
            print("\n🗑️ 测试删除记忆...")
            try:
                delete_result = memory.delete(memory_id)
                print(f"✅ 删除记忆成功:")
                print(f"    类型: {type(delete_result)}")
                print(f"    结果: {delete_result}")
                
                # 验证删除是否成功
                remaining_memories = memory.get_all(user_id="cursor_user")
                print(f"✅ 验证删除成功，剩余记忆: {len(remaining_memories)} 条")
                
            except Exception as e:
                print(f"❌ 删除记忆失败: {e}")
                import traceback
                traceback.print_exc()
                
        else:
            print("⚠️ 记忆格式异常，无法获取ID")
            print(f"记忆类型: {type(first_memory)}")
            print(f"记忆内容: {first_memory}")
            
            if isinstance(first_memory, dict):
                print(f"字典键: {list(first_memory.keys())}")
            
            return False
        
        print("\n🎉 记忆操作测试完成！")
        return True
        
    except Exception as e:
        print(f"❌ 测试过程中出错: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🔧 记忆系统操作测试")
    print("=" * 50)
    
    success = test_memory_operations()
    
    if success:
        print("\n✅ 所有测试通过！删除和编辑功能应该可以正常工作了。")
    else:
        print("\n❌ 测试失败，需要进一步调试。")
