#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Mem0 MCP服务器 - 简化版本，用于Cursor等IDE集成
"""

import sys
import json
import asyncio
import os
from pathlib import Path

# 添加项目路径
current_dir = Path(__file__).parent.absolute()
sys.path.insert(0, str(current_dir))

# 设置工作目录
os.chdir(current_dir)

class Mem0MCPServer:
    """Mem0 MCP服务器"""
    
    def __init__(self):
        try:
            from local_config_simple import get_local_config_simple, setup_local_env
            from mem0 import Memory

            setup_local_env()
            config = get_local_config_simple()
            self.memory = Memory(config=config)
            print("✅ Mem0 MCP服务器初始化成功", file=sys.stderr)
        except Exception as e:
            print(f"❌ Mem0 MCP服务器初始化失败: {e}", file=sys.stderr)
            import traceback
            traceback.print_exc(file=sys.stderr)
            raise
        
        self.tools = [
            {
                "name": "add_memory",
                "description": "添加新记忆到Mem0系统",
                "inputSchema": {
                    "type": "object",
                    "properties": {
                        "content": {"type": "string", "description": "记忆内容"},
                        "user_id": {"type": "string", "description": "用户ID", "default": "cursor_user"}
                    },
                    "required": ["content"]
                }
            },
            {
                "name": "search_memories",
                "description": "搜索相关记忆",
                "inputSchema": {
                    "type": "object",
                    "properties": {
                        "query": {"type": "string", "description": "搜索查询"},
                        "user_id": {"type": "string", "description": "用户ID", "default": "cursor_user"},
                        "limit": {"type": "integer", "description": "返回数量限制", "default": 10}
                    },
                    "required": ["query"]
                }
            },
            {
                "name": "get_all_memories",
                "description": "获取所有记忆",
                "inputSchema": {
                    "type": "object",
                    "properties": {
                        "user_id": {"type": "string", "description": "用户ID", "default": "cursor_user"},
                        "limit": {"type": "integer", "description": "返回数量限制", "default": 100}
                    }
                }
            },
            {
                "name": "get_memory",
                "description": "根据ID获取特定记忆",
                "inputSchema": {
                    "type": "object",
                    "properties": {
                        "memory_id": {"type": "string", "description": "记忆ID"}
                    },
                    "required": ["memory_id"]
                }
            },
            {
                "name": "update_memory",
                "description": "更新指定记忆的内容",
                "inputSchema": {
                    "type": "object",
                    "properties": {
                        "memory_id": {"type": "string", "description": "记忆ID"},
                        "new_content": {"type": "string", "description": "新的记忆内容"}
                    },
                    "required": ["memory_id", "new_content"]
                }
            },
            {
                "name": "delete_memory",
                "description": "删除指定的记忆",
                "inputSchema": {
                    "type": "object",
                    "properties": {
                        "memory_id": {"type": "string", "description": "要删除的记忆ID"}
                    },
                    "required": ["memory_id"]
                }
            },
            {
                "name": "delete_all_memories",
                "description": "删除用户的所有记忆",
                "inputSchema": {
                    "type": "object",
                    "properties": {
                        "user_id": {"type": "string", "description": "用户ID", "default": "cursor_user"},
                        "confirm": {"type": "boolean", "description": "确认删除", "default": False}
                    },
                    "required": ["confirm"]
                }
            }
        ]
    
    async def handle_message(self, message):
        """处理MCP消息"""
        method = message.get("method")
        params = message.get("params", {})
        msg_id = message.get("id")
        
        try:
            if method == "initialize":
                return {
                    "jsonrpc": "2.0",
                    "id": msg_id,
                    "result": {
                        "protocolVersion": "2024-11-05",
                        "capabilities": {"tools": {"listChanged": True}},
                        "serverInfo": {"name": "mem0-local", "version": "1.0.0"}
                    }
                }
            
            elif method == "tools/list":
                return {
                    "jsonrpc": "2.0",
                    "id": msg_id,
                    "result": {"tools": self.tools}
                }
            
            elif method == "tools/call":
                return await self.handle_tool_call(msg_id, params)
            
            else:
                return {
                    "jsonrpc": "2.0",
                    "id": msg_id,
                    "error": {"code": -32601, "message": f"Unknown method: {method}"}
                }
                
        except Exception as e:
            return {
                "jsonrpc": "2.0",
                "id": msg_id,
                "error": {"code": -32603, "message": f"Internal error: {str(e)}"}
            }
    
    async def handle_tool_call(self, msg_id, params):
        """处理工具调用"""
        tool_name = params.get("name")
        arguments = params.get("arguments", {})
        user_id = arguments.get("user_id", "cursor_user")
        
        try:
            if tool_name == "add_memory":
                content = arguments["content"]
                result = self.memory.add(content, user_id=user_id)
                
                # 解析结果
                if isinstance(result, dict) and 'results' in result:
                    results = result['results']
                    if results and len(results) > 0:
                        memory_info = results[0]
                        memory_text = memory_info.get('memory', content)
                        event = memory_info.get('event', 'ADD')
                        
                        return {
                            "jsonrpc": "2.0",
                            "id": msg_id,
                            "result": {
                                "content": [{
                                    "type": "text",
                                    "text": f"✅ 记忆已{event}: {memory_text}"
                                }]
                            }
                        }
                
                return {
                    "jsonrpc": "2.0",
                    "id": msg_id,
                    "result": {
                        "content": [{
                            "type": "text",
                            "text": f"✅ 记忆已添加: {content}"
                        }]
                    }
                }
            
            elif tool_name == "search_memories":
                query = arguments["query"]
                limit = arguments.get("limit", 10)
                memories = self.memory.search(query, user_id=user_id, limit=limit)

                if not memories:
                    text = f"🔍 未找到与 '{query}' 相关的记忆"
                else:
                    text = f"🔍 找到 {len(memories)} 条相关记忆:\\n\\n"
                    for i, mem in enumerate(memories, 1):
                        # 处理不同的返回格式
                        if isinstance(mem, dict):
                            if 'memory' in mem:
                                memory_text = mem['memory']
                                memory_id = mem.get('id', 'unknown')
                                text += f"{i}. [{memory_id[:8]}...] {memory_text}\\n"
                            else:
                                text += f"{i}. {str(mem)}\\n"
                        else:
                            text += f"{i}. {str(mem)}\\n"

                return {
                    "jsonrpc": "2.0",
                    "id": msg_id,
                    "result": {
                        "content": [{"type": "text", "text": text}]
                    }
                }
            
            elif tool_name == "get_all_memories":
                limit = arguments.get("limit", 100)
                memories = self.memory.get_all(user_id=user_id, limit=limit)

                if not memories:
                    text = "📭 暂无记忆内容"
                else:
                    text = f"📋 所有记忆 (共 {len(memories)} 条):\\n\\n"
                    for i, mem in enumerate(memories, 1):
                        # 处理不同的返回格式
                        if isinstance(mem, dict):
                            if 'memory' in mem:
                                memory_text = mem['memory']
                                memory_id = mem.get('id', 'unknown')
                                created_at = mem.get('created_at', 'unknown')
                                text += f"{i}. [{memory_id[:8]}...] {memory_text}\\n"
                                text += f"    📅 创建时间: {created_at}\\n\\n"
                            else:
                                text += f"{i}. {str(mem)}\\n"
                        else:
                            text += f"{i}. {str(mem)}\\n"

                return {
                    "jsonrpc": "2.0",
                    "id": msg_id,
                    "result": {
                        "content": [{"type": "text", "text": text}]
                    }
                }

            elif tool_name == "get_memory":
                memory_id = arguments["memory_id"]
                memory = self.memory.get(memory_id)

                if not memory:
                    text = f"❌ 未找到ID为 {memory_id} 的记忆"
                else:
                    text = f"📄 记忆详情:\\n\\n"
                    text += f"🆔 ID: {memory.get('id', 'unknown')}\\n"
                    text += f"📝 内容: {memory.get('memory', 'unknown')}\\n"
                    text += f"📅 创建时间: {memory.get('created_at', 'unknown')}\\n"
                    text += f"🔄 更新时间: {memory.get('updated_at', 'unknown')}\\n"
                    if 'metadata' in memory:
                        text += f"📋 元数据: {memory['metadata']}\\n"

                return {
                    "jsonrpc": "2.0",
                    "id": msg_id,
                    "result": {
                        "content": [{"type": "text", "text": text}]
                    }
                }

            elif tool_name == "update_memory":
                memory_id = arguments["memory_id"]
                new_content = arguments["new_content"]

                try:
                    result = self.memory.update(memory_id, new_content)

                    if result:
                        text = f"✅ 记忆已更新\\n"
                        text += f"🆔 ID: {memory_id}\\n"
                        text += f"📝 新内容: {new_content}"
                    else:
                        text = f"❌ 更新失败，未找到ID为 {memory_id} 的记忆"

                except Exception as e:
                    text = f"❌ 更新失败: {str(e)}"

                return {
                    "jsonrpc": "2.0",
                    "id": msg_id,
                    "result": {
                        "content": [{"type": "text", "text": text}]
                    }
                }

            elif tool_name == "delete_memory":
                memory_id = arguments["memory_id"]

                try:
                    result = self.memory.delete(memory_id)

                    if result:
                        text = f"🗑️ 记忆已删除\\n🆔 ID: {memory_id}"
                    else:
                        text = f"❌ 删除失败，未找到ID为 {memory_id} 的记忆"

                except Exception as e:
                    text = f"❌ 删除失败: {str(e)}"

                return {
                    "jsonrpc": "2.0",
                    "id": msg_id,
                    "result": {
                        "content": [{"type": "text", "text": text}]
                    }
                }

            elif tool_name == "delete_all_memories":
                confirm = arguments.get("confirm", False)

                if not confirm:
                    text = "⚠️ 危险操作！删除所有记忆需要确认。\\n请设置 confirm=true 来确认删除。"
                else:
                    try:
                        result = self.memory.delete_all(user_id=user_id)

                        if result:
                            text = f"🗑️ 用户 {user_id} 的所有记忆已删除"
                        else:
                            text = "❌ 删除失败"

                    except Exception as e:
                        text = f"❌ 删除失败: {str(e)}"

                return {
                    "jsonrpc": "2.0",
                    "id": msg_id,
                    "result": {
                        "content": [{"type": "text", "text": text}]
                    }
                }

            else:
                raise ValueError(f"Unknown tool: {tool_name}")
                
        except Exception as e:
            return {
                "jsonrpc": "2.0",
                "id": msg_id,
                "error": {"code": -32603, "message": f"Tool call failed: {str(e)}"}
            }

async def main():
    """主函数"""
    try:
        server = Mem0MCPServer()
        
        # 发送初始化消息
        init_msg = {"jsonrpc": "2.0", "method": "initialized", "params": {}}
        print(json.dumps(init_msg))
        sys.stdout.flush()
        
        # 处理stdin消息
        while True:
            try:
                line = await asyncio.get_event_loop().run_in_executor(None, sys.stdin.readline)
                if not line:
                    break
                
                try:
                    message = json.loads(line.strip())
                    response = await server.handle_message(message)
                    print(json.dumps(response))
                    sys.stdout.flush()
                except json.JSONDecodeError:
                    continue
                    
            except Exception as e:
                error_response = {
                    "jsonrpc": "2.0",
                    "error": {"code": -32603, "message": f"Error: {str(e)}"}
                }
                print(json.dumps(error_response))
                sys.stdout.flush()
                
    except Exception as e:
        print(f"MCP服务器启动失败: {e}", file=sys.stderr)

if __name__ == "__main__":
    asyncio.run(main())
