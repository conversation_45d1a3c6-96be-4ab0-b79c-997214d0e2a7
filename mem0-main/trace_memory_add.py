#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
追踪记忆添加过程
"""

import sys
from pathlib import Path

# 添加项目路径
sys.path.insert(0, str(Path(__file__).parent))

def trace_memory_add():
    """追踪记忆添加过程"""
    try:
        from local_config_simple import get_memory_client
        import chromadb
        
        print("🔍 开始追踪记忆添加过程...")
        
        # 1. 检查初始状态
        print("\n📊 1. 检查初始状态...")
        client = chromadb.PersistentClient(path="./chroma_db_v2")
        collection = client.get_collection("mem0_local_v2")
        initial_count = collection.count()
        print(f"   初始记录数: {initial_count}")
        
        # 2. 创建记忆客户端
        print("\n🧠 2. 创建记忆客户端...")
        memory = get_memory_client()
        print("   ✅ 记忆客户端创建成功")
        
        # 3. 添加记忆
        print("\n➕ 3. 添加记忆...")
        test_content = "这是一个用于追踪的测试记忆"
        print(f"   内容: {test_content}")
        
        result = memory.add(test_content, user_id="cursor_user")
        print(f"   ✅ 添加结果: {result}")
        print(f"   结果类型: {type(result)}")
        
        if isinstance(result, dict):
            print(f"   结果键: {list(result.keys())}")
            for key, value in result.items():
                print(f"     {key}: {value} (类型: {type(value)})")
        
        # 4. 检查数据库状态
        print("\n📊 4. 检查数据库状态...")
        after_count = collection.count()
        print(f"   添加后记录数: {after_count}")
        print(f"   新增记录数: {after_count - initial_count}")
        
        if after_count > initial_count:
            # 获取最新数据
            all_data = collection.get()
            print(f"   最新数据:")
            print(f"     IDs: {all_data.get('ids', [])}")
            print(f"     Documents: {all_data.get('documents', [])}")
            print(f"     Metadatas: {all_data.get('metadatas', [])}")
        
        # 5. 使用mem0 API获取记忆
        print("\n📋 5. 使用mem0 API获取记忆...")
        memories = memory.get_all(user_id="cursor_user")
        print(f"   获取结果: {memories}")
        print(f"   结果类型: {type(memories)}")
        
        if isinstance(memories, dict) and 'results' in memories:
            results = memories['results']
            print(f"   results长度: {len(results)}")
            for i, mem in enumerate(results):
                print(f"     记忆 {i}: {mem} (类型: {type(mem)})")
        
        # 6. 测试搜索功能
        print("\n🔍 6. 测试搜索功能...")
        search_results = memory.search("追踪", user_id="cursor_user")
        print(f"   搜索结果: {search_results}")
        
        return True
        
    except Exception as e:
        print(f"❌ 追踪过程中出错: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🔧 记忆添加过程追踪工具")
    print("=" * 50)
    
    success = trace_memory_add()
    
    if success:
        print("\n✅ 追踪完成")
    else:
        print("\n❌ 追踪失败")
