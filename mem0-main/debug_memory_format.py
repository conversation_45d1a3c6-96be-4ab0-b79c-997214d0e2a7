#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试记忆数据格式
"""

import sys
from pathlib import Path

# 添加项目路径
sys.path.insert(0, str(Path(__file__).parent))

def debug_memory_format():
    """调试记忆数据格式"""
    try:
        from local_config_simple import get_memory_client
        
        print("🔍 调试记忆数据格式...")
        memory = get_memory_client()
        print("✅ 记忆客户端创建成功")
        
        # 获取所有记忆
        print("\n📋 获取所有记忆...")
        memories = memory.get_all(user_id="cursor_user")
        
        print(f"📊 原始数据:")
        print(f"  类型: {type(memories)}")
        print(f"  内容: {memories}")
        
        if isinstance(memories, dict):
            print(f"  字典键: {list(memories.keys())}")
            for key, value in memories.items():
                print(f"    {key}: {value} (类型: {type(value)})")
                
                if isinstance(value, list):
                    print(f"      列表长度: {len(value)}")
                    for i, item in enumerate(value):
                        print(f"        项目 {i}: {item} (类型: {type(item)})")
                        if isinstance(item, dict):
                            print(f"          字典键: {list(item.keys())}")
                            for k, v in item.items():
                                print(f"            {k}: {v}")
        
        elif isinstance(memories, list):
            print(f"  列表长度: {len(memories)}")
            for i, mem in enumerate(memories):
                print(f"    项目 {i}: {mem} (类型: {type(mem)})")
                if isinstance(mem, dict):
                    print(f"      字典键: {list(mem.keys())}")
                    for k, v in mem.items():
                        print(f"        {k}: {v}")
        
        # 测试搜索功能
        print("\n🔍 测试搜索功能...")
        search_results = memory.search("测试", user_id="cursor_user")
        print(f"搜索结果类型: {type(search_results)}")
        print(f"搜索结果内容: {search_results}")
        
        if isinstance(search_results, list):
            for i, result in enumerate(search_results):
                print(f"  搜索结果 {i}: {result} (类型: {type(result)})")
                if isinstance(result, dict):
                    print(f"    字典键: {list(result.keys())}")
                    for k, v in result.items():
                        print(f"      {k}: {v}")
        
        return memories, search_results
        
    except Exception as e:
        print(f"❌ 调试过程中出错: {e}")
        import traceback
        traceback.print_exc()
        return None, None

if __name__ == "__main__":
    print("🔧 记忆数据格式调试工具")
    print("=" * 50)
    
    memories, search_results = debug_memory_format()
    
    print("\n" + "=" * 50)
    print("🎯 调试完成")
