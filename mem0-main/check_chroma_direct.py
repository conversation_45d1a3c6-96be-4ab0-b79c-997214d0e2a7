#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
直接检查ChromaDB数据库内容
"""

import chromadb
from pathlib import Path

def check_chroma_direct():
    """直接检查ChromaDB数据库"""
    try:
        print("🔍 直接连接ChromaDB...")
        
        # 连接到数据库
        client = chromadb.PersistentClient(path="./chroma_db_v2")
        print("✅ 连接成功")
        
        # 列出所有集合
        collections = client.list_collections()
        print(f"📋 找到 {len(collections)} 个集合:")
        for col in collections:
            print(f"  - {col.name} (ID: {col.id})")
        
        if collections:
            # 检查第一个集合的内容
            collection = collections[0]
            print(f"\n🔍 检查集合 '{collection.name}' 的内容:")
            
            # 获取集合中的所有数据
            results = collection.get()
            print(f"📊 集合数据:")
            print(f"  IDs: {results.get('ids', [])}")
            print(f"  Documents: {results.get('documents', [])}")
            print(f"  Metadatas: {results.get('metadatas', [])}")
            print(f"  Embeddings: {len(results.get('embeddings', []))} 个向量")
            
            # 统计信息
            count = collection.count()
            print(f"📈 集合统计: {count} 条记录")
            
        else:
            print("❌ 没有找到任何集合")
            
        # 尝试创建测试集合
        print(f"\n🧪 创建测试集合...")
        try:
            test_collection = client.get_or_create_collection("test_collection")
            print("✅ 测试集合创建成功")
            
            # 添加测试数据
            test_collection.add(
                documents=["这是一个测试文档"],
                metadatas=[{"user_id": "cursor_user", "test": True}],
                ids=["test_id_1"]
            )
            print("✅ 测试数据添加成功")
            
            # 查询测试数据
            test_results = test_collection.get()
            print(f"📊 测试数据查询结果:")
            print(f"  IDs: {test_results.get('ids', [])}")
            print(f"  Documents: {test_results.get('documents', [])}")
            print(f"  Metadatas: {test_results.get('metadatas', [])}")
            
        except Exception as e:
            print(f"❌ 测试集合操作失败: {e}")
            
    except Exception as e:
        print(f"❌ 检查失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    print("🔧 ChromaDB直接检查工具")
    print("=" * 50)
    
    check_chroma_direct()
    
    print("\n✅ 检查完成")
