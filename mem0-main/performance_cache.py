#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
性能优化 - 内存缓存系统
实现多层缓存架构，显著提升查询和计算速度
"""

import time
import hashlib
import pickle
import threading
from datetime import datetime, timedelta
from typing import Any, Optional, Dict, List
from functools import wraps
import logging

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class MemoryCache:
    """内存缓存管理器"""
    
    def __init__(self, max_size: int = 1000, default_ttl: int = 3600):
        """
        初始化内存缓存
        
        Args:
            max_size: 最大缓存条目数
            default_ttl: 默认过期时间（秒）
        """
        self.max_size = max_size
        self.default_ttl = default_ttl
        self.cache = {}
        self.access_times = {}
        self.lock = threading.RLock()
        
        # 统计信息
        self.stats = {
            'hits': 0,
            'misses': 0,
            'evictions': 0,
            'total_requests': 0
        }
        
        logger.info(f"内存缓存初始化完成，最大容量: {max_size}, 默认TTL: {default_ttl}秒")
    
    def _generate_key(self, *args, **kwargs) -> str:
        """生成缓存键"""
        key_data = str(args) + str(sorted(kwargs.items()))
        return hashlib.md5(key_data.encode()).hexdigest()
    
    def _is_expired(self, item: Dict) -> bool:
        """检查缓存项是否过期"""
        if 'expires_at' not in item:
            return False
        return datetime.now() > item['expires_at']
    
    def _evict_expired(self):
        """清理过期缓存"""
        with self.lock:
            expired_keys = []
            for key, item in self.cache.items():
                if self._is_expired(item):
                    expired_keys.append(key)
            
            for key in expired_keys:
                del self.cache[key]
                if key in self.access_times:
                    del self.access_times[key]
                self.stats['evictions'] += 1
    
    def _evict_lru(self):
        """LRU淘汰策略"""
        with self.lock:
            if len(self.cache) >= self.max_size:
                # 找到最久未访问的键
                lru_key = min(self.access_times.keys(), 
                             key=lambda k: self.access_times[k])
                del self.cache[lru_key]
                del self.access_times[lru_key]
                self.stats['evictions'] += 1
                logger.debug(f"LRU淘汰缓存项: {lru_key}")
    
    def get(self, key: str) -> Optional[Any]:
        """获取缓存值"""
        with self.lock:
            self.stats['total_requests'] += 1
            
            if key not in self.cache:
                self.stats['misses'] += 1
                return None
            
            item = self.cache[key]
            
            # 检查是否过期
            if self._is_expired(item):
                del self.cache[key]
                if key in self.access_times:
                    del self.access_times[key]
                self.stats['misses'] += 1
                self.stats['evictions'] += 1
                return None
            
            # 更新访问时间
            self.access_times[key] = time.time()
            self.stats['hits'] += 1
            
            logger.debug(f"缓存命中: {key}")
            return item['value']
    
    def set(self, key: str, value: Any, ttl: Optional[int] = None) -> None:
        """设置缓存值"""
        with self.lock:
            # 清理过期缓存
            self._evict_expired()
            
            # LRU淘汰
            self._evict_lru()
            
            # 计算过期时间
            expires_at = None
            if ttl is not None:
                expires_at = datetime.now() + timedelta(seconds=ttl)
            elif self.default_ttl > 0:
                expires_at = datetime.now() + timedelta(seconds=self.default_ttl)
            
            # 存储缓存项
            self.cache[key] = {
                'value': value,
                'created_at': datetime.now(),
                'expires_at': expires_at
            }
            self.access_times[key] = time.time()
            
            logger.debug(f"缓存设置: {key}, TTL: {ttl or self.default_ttl}")
    
    def delete(self, key: str) -> bool:
        """删除缓存项"""
        with self.lock:
            if key in self.cache:
                del self.cache[key]
                if key in self.access_times:
                    del self.access_times[key]
                logger.debug(f"缓存删除: {key}")
                return True
            return False
    
    def clear(self) -> None:
        """清空所有缓存"""
        with self.lock:
            self.cache.clear()
            self.access_times.clear()
            logger.info("缓存已清空")
    
    def get_stats(self) -> Dict:
        """获取缓存统计信息"""
        with self.lock:
            hit_rate = 0
            if self.stats['total_requests'] > 0:
                hit_rate = self.stats['hits'] / self.stats['total_requests']
            
            return {
                **self.stats,
                'hit_rate': hit_rate,
                'cache_size': len(self.cache),
                'max_size': self.max_size
            }

class QueryResultCache:
    """查询结果缓存"""
    
    def __init__(self, cache_manager: MemoryCache):
        self.cache = cache_manager
        self.prefix = "query_result:"
    
    def cache_search_result(self, query: str, user_id: str, results: List, ttl: int = 1800):
        """缓存搜索结果"""
        cache_key = f"{self.prefix}search:{self._hash_query(query, user_id)}"
        self.cache.set(cache_key, results, ttl)
        logger.info(f"搜索结果已缓存: {query[:50]}...")
    
    def get_search_result(self, query: str, user_id: str) -> Optional[List]:
        """获取缓存的搜索结果"""
        cache_key = f"{self.prefix}search:{self._hash_query(query, user_id)}"
        result = self.cache.get(cache_key)
        if result:
            logger.info(f"搜索结果缓存命中: {query[:50]}...")
        return result
    
    def cache_memory_list(self, user_id: str, memories: List, ttl: int = 600):
        """缓存记忆列表"""
        cache_key = f"{self.prefix}memories:{user_id}"
        self.cache.set(cache_key, memories, ttl)
        logger.info(f"记忆列表已缓存: 用户 {user_id}")
    
    def get_memory_list(self, user_id: str) -> Optional[List]:
        """获取缓存的记忆列表"""
        cache_key = f"{self.prefix}memories:{user_id}"
        result = self.cache.get(cache_key)
        if result:
            logger.info(f"记忆列表缓存命中: 用户 {user_id}")
        return result
    
    def invalidate_user_cache(self, user_id: str):
        """使用户相关缓存失效"""
        # 删除用户的记忆列表缓存
        memory_key = f"{self.prefix}memories:{user_id}"
        self.cache.delete(memory_key)
        
        # 注意：搜索结果缓存会自然过期，这里不做主动清理
        logger.info(f"用户缓存已失效: {user_id}")
    
    def _hash_query(self, query: str, user_id: str) -> str:
        """生成查询哈希"""
        return hashlib.md5(f"{query}:{user_id}".encode()).hexdigest()

class VectorCache:
    """向量计算缓存"""
    
    def __init__(self, cache_manager: MemoryCache):
        self.cache = cache_manager
        self.prefix = "vector:"
    
    def cache_vector(self, text: str, vector: List[float], ttl: int = 86400):
        """缓存向量计算结果"""
        cache_key = f"{self.prefix}{self._hash_text(text)}"
        self.cache.set(cache_key, vector, ttl)
        logger.debug(f"向量已缓存: {text[:50]}...")
    
    def get_vector(self, text: str) -> Optional[List[float]]:
        """获取缓存的向量"""
        cache_key = f"{self.prefix}{self._hash_text(text)}"
        result = self.cache.get(cache_key)
        if result:
            logger.debug(f"向量缓存命中: {text[:50]}...")
        return result
    
    def _hash_text(self, text: str) -> str:
        """生成文本哈希"""
        return hashlib.md5(text.encode()).hexdigest()

# 全局缓存实例
_global_cache = None
_query_cache = None
_vector_cache = None

def get_cache_manager() -> MemoryCache:
    """获取全局缓存管理器"""
    global _global_cache
    if _global_cache is None:
        _global_cache = MemoryCache(max_size=2000, default_ttl=3600)
    return _global_cache

def get_query_cache() -> QueryResultCache:
    """获取查询结果缓存"""
    global _query_cache
    if _query_cache is None:
        _query_cache = QueryResultCache(get_cache_manager())
    return _query_cache

def get_vector_cache() -> VectorCache:
    """获取向量缓存"""
    global _vector_cache
    if _vector_cache is None:
        _vector_cache = VectorCache(get_cache_manager())
    return _vector_cache

def clear_all_caches():
    """清空所有缓存"""
    global _global_cache, _query_cache, _vector_cache
    if _global_cache:
        _global_cache.clear()
    logger.info("所有缓存已清空")

def get_cache_stats() -> Dict:
    """获取缓存统计信息"""
    cache_manager = get_cache_manager()
    return cache_manager.get_stats()

if __name__ == "__main__":
    # 测试缓存系统
    print("🧪 测试缓存系统")
    
    cache = get_cache_manager()
    query_cache = get_query_cache()
    vector_cache = get_vector_cache()
    
    # 测试基础缓存
    cache.set("test_key", "test_value", 60)
    result = cache.get("test_key")
    print(f"基础缓存测试: {result}")
    
    # 测试查询缓存
    query_cache.cache_search_result("测试查询", "test_user", ["结果1", "结果2"])
    search_result = query_cache.get_search_result("测试查询", "test_user")
    print(f"查询缓存测试: {search_result}")
    
    # 测试向量缓存
    vector_cache.cache_vector("测试文本", [0.1, 0.2, 0.3])
    vector_result = vector_cache.get_vector("测试文本")
    print(f"向量缓存测试: {vector_result}")
    
    # 显示统计信息
    stats = get_cache_stats()
    print(f"缓存统计: {stats}")
