#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
GPU加速向量计算模块
使用GPU加速embedding计算，显著提升向量生成和相似度搜索速度
"""

import time
import numpy as np
import logging
from typing import List, Dict, Any, Optional, Tuple
import threading
from functools import lru_cache

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class GPUAcceleratedEmbedding:
    """GPU加速的向量计算器"""
    
    def __init__(self, model_name: str = "all-MiniLM-L6-v2", batch_size: int = 32):
        """
        初始化GPU加速向量计算器
        
        Args:
            model_name: 使用的embedding模型
            batch_size: 批量处理大小
        """
        self.model_name = model_name
        self.batch_size = batch_size
        self.device = "cpu"  # 默认CPU
        self.model = None
        self.gpu_available = False
        self.cuda_available = False
        
        # 性能统计
        self.stats = {
            'total_embeddings': 0,
            'gpu_embeddings': 0,
            'cpu_embeddings': 0,
            'total_time': 0.0,
            'avg_time_per_embedding': 0.0,
            'cache_hits': 0
        }
        
        self.lock = threading.RLock()
        
        # 初始化
        self._initialize_gpu()
        self._load_model()
        
    def _initialize_gpu(self):
        """初始化GPU环境"""
        try:
            import torch
            self.cuda_available = torch.cuda.is_available()
            
            if self.cuda_available:
                self.device = "cuda"
                self.gpu_available = True
                device_name = torch.cuda.get_device_name(0)
                logger.info(f"✅ GPU加速已启用: {device_name}")
            else:
                logger.info("⚠️ CUDA不可用，使用CPU计算")
                
        except ImportError:
            logger.warning("⚠️ PyTorch未安装，使用CPU计算")
    
    def _load_model(self):
        """加载embedding模型"""
        try:
            from sentence_transformers import SentenceTransformer
            
            logger.info(f"📦 加载模型: {self.model_name}")
            self.model = SentenceTransformer(self.model_name, device=self.device)
            
            # 预热模型
            self._warmup_model()
            
            logger.info(f"✅ 模型加载完成，设备: {self.device}")
            
        except ImportError:
            logger.error("❌ sentence-transformers未安装")
            raise
        except Exception as e:
            logger.error(f"❌ 模型加载失败: {e}")
            raise
    
    def _warmup_model(self):
        """预热模型"""
        try:
            warmup_texts = ["预热文本", "warmup text"]
            self.model.encode(warmup_texts, convert_to_tensor=True)
            logger.info("🔥 模型预热完成")
        except Exception as e:
            logger.warning(f"⚠️ 模型预热失败: {e}")
    
    def encode_single(self, text: str, use_cache: bool = True) -> np.ndarray:
        """
        编码单个文本
        
        Args:
            text: 要编码的文本
            use_cache: 是否使用缓存
            
        Returns:
            np.ndarray: 向量表示
        """
        if use_cache:
            # 尝试从缓存获取
            try:
                from performance_cache import get_vector_cache
                vector_cache = get_vector_cache()
                cached_vector = vector_cache.get_vector(text)
                
                if cached_vector is not None:
                    with self.lock:
                        self.stats['cache_hits'] += 1
                    return np.array(cached_vector)
            except ImportError:
                pass
        
        # 计算向量
        start_time = time.time()
        
        try:
            if self.gpu_available:
                try:
                    import torch
                    # GPU计算
                    with torch.no_grad():
                        embedding = self.model.encode([text], convert_to_tensor=True)
                        vector = embedding.cpu().numpy()[0]
                    
                    with self.lock:
                        self.stats['gpu_embeddings'] += 1
                except:
                    # GPU失败时降级到CPU
                    vector = self.model.encode([text])[0]
                    with self.lock:
                        self.stats['cpu_embeddings'] += 1
            else:
                # CPU计算
                vector = self.model.encode([text])[0]
                
                with self.lock:
                    self.stats['cpu_embeddings'] += 1
            
            # 更新统计
            execution_time = time.time() - start_time
            with self.lock:
                self.stats['total_embeddings'] += 1
                self.stats['total_time'] += execution_time
                self.stats['avg_time_per_embedding'] = (
                    self.stats['total_time'] / self.stats['total_embeddings']
                )
            
            # 缓存结果
            if use_cache:
                try:
                    from performance_cache import get_vector_cache
                    vector_cache = get_vector_cache()
                    vector_cache.cache_vector(text, vector.tolist())
                except ImportError:
                    pass
            
            logger.debug(f"向量计算完成: {text[:50]}..., 耗时: {execution_time:.3f}秒")
            return vector
            
        except Exception as e:
            logger.error(f"向量计算失败: {e}")
            raise
    
    def encode_batch(self, texts: List[str], use_cache: bool = True) -> List[np.ndarray]:
        """
        批量编码文本
        
        Args:
            texts: 要编码的文本列表
            use_cache: 是否使用缓存
            
        Returns:
            List[np.ndarray]: 向量列表
        """
        if not texts:
            return []
        
        # 检查缓存
        vectors = []
        uncached_texts = []
        uncached_indices = []
        
        if use_cache:
            try:
                from performance_cache import get_vector_cache
                vector_cache = get_vector_cache()
                
                for i, text in enumerate(texts):
                    cached_vector = vector_cache.get_vector(text)
                    if cached_vector is not None:
                        vectors.append(np.array(cached_vector))
                        with self.lock:
                            self.stats['cache_hits'] += 1
                    else:
                        vectors.append(None)
                        uncached_texts.append(text)
                        uncached_indices.append(i)
            except ImportError:
                uncached_texts = texts
                uncached_indices = list(range(len(texts)))
                vectors = [None] * len(texts)
        else:
            uncached_texts = texts
            uncached_indices = list(range(len(texts)))
            vectors = [None] * len(texts)
        
        # 批量计算未缓存的向量
        if uncached_texts:
            start_time = time.time()
            
            try:
                if self.gpu_available:
                    try:
                        import torch
                        # GPU批量计算
                        with torch.no_grad():
                            embeddings = self.model.encode(
                                uncached_texts, 
                                convert_to_tensor=True,
                                batch_size=self.batch_size
                            )
                            computed_vectors = embeddings.cpu().numpy()
                        
                        with self.lock:
                            self.stats['gpu_embeddings'] += len(uncached_texts)
                    except:
                        # GPU失败时降级到CPU
                        computed_vectors = self.model.encode(
                            uncached_texts,
                            batch_size=self.batch_size
                        )
                        with self.lock:
                            self.stats['cpu_embeddings'] += len(uncached_texts)
                else:
                    # CPU批量计算
                    computed_vectors = self.model.encode(
                        uncached_texts,
                        batch_size=self.batch_size
                    )
                    
                    with self.lock:
                        self.stats['cpu_embeddings'] += len(uncached_texts)
                
                # 填充结果
                for i, vector in enumerate(computed_vectors):
                    original_index = uncached_indices[i]
                    vectors[original_index] = vector
                    
                    # 缓存结果
                    if use_cache:
                        try:
                            from performance_cache import get_vector_cache
                            vector_cache = get_vector_cache()
                            vector_cache.cache_vector(uncached_texts[i], vector.tolist())
                        except ImportError:
                            pass
                
                # 更新统计
                execution_time = time.time() - start_time
                with self.lock:
                    self.stats['total_embeddings'] += len(uncached_texts)
                    self.stats['total_time'] += execution_time
                    self.stats['avg_time_per_embedding'] = (
                        self.stats['total_time'] / self.stats['total_embeddings']
                    )
                
                logger.info(f"批量向量计算完成: {len(uncached_texts)}个文本, 耗时: {execution_time:.3f}秒")
                
            except Exception as e:
                logger.error(f"批量向量计算失败: {e}")
                raise
        
        return vectors
    
    def compute_similarity_batch(self, query_vector: np.ndarray, 
                               candidate_vectors: List[np.ndarray]) -> List[float]:
        """
        批量计算相似度
        
        Args:
            query_vector: 查询向量
            candidate_vectors: 候选向量列表
            
        Returns:
            List[float]: 相似度分数列表
        """
        if not candidate_vectors:
            return []
        
        try:
            if self.gpu_available:
                try:
                    import torch
                    # GPU批量相似度计算
                    query_tensor = torch.tensor(query_vector).cuda().unsqueeze(0)
                    candidate_tensor = torch.tensor(np.array(candidate_vectors)).cuda()
                    
                    # 计算余弦相似度
                    similarities = torch.nn.functional.cosine_similarity(
                        query_tensor, candidate_tensor, dim=1
                    )
                    
                    return similarities.cpu().numpy().tolist()
                except:
                    pass
            
            # CPU计算
            from sklearn.metrics.pairwise import cosine_similarity
            similarities = cosine_similarity([query_vector], candidate_vectors)[0]
            return similarities.tolist()
                
        except Exception as e:
            logger.error(f"相似度计算失败: {e}")
            # 简单的点积相似度作为后备
            similarities = []
            for vec in candidate_vectors:
                sim = np.dot(query_vector, vec) / (np.linalg.norm(query_vector) * np.linalg.norm(vec))
                similarities.append(float(sim))
            return similarities
    
    def get_stats(self) -> Dict[str, Any]:
        """获取性能统计"""
        with self.lock:
            stats = self.stats.copy()
            stats.update({
                'gpu_available': self.gpu_available,
                'cuda_available': self.cuda_available,
                'device': self.device,
                'model_name': self.model_name,
                'batch_size': self.batch_size
            })
            return stats

# 全局实例
_global_embedding_engine = None

def get_embedding_engine() -> GPUAcceleratedEmbedding:
    """获取全局向量计算引擎"""
    global _global_embedding_engine
    if _global_embedding_engine is None:
        _global_embedding_engine = GPUAcceleratedEmbedding()
    return _global_embedding_engine

if __name__ == "__main__":
    # 测试GPU加速向量计算
    print("🧪 测试GPU加速向量计算")
    
    engine = get_embedding_engine()
    
    # 测试单个文本编码
    test_text = "这是一个测试文本，用来验证GPU加速向量计算的性能。"
    vector = engine.encode_single(test_text)
    print(f"单个向量维度: {vector.shape}")
    
    # 测试批量编码
    test_texts = [
        "第一个测试文本",
        "第二个测试文本", 
        "第三个测试文本",
        "第四个测试文本",
        "第五个测试文本"
    ]
    
    vectors = engine.encode_batch(test_texts)
    print(f"批量向量数量: {len(vectors)}")
    
    # 显示统计信息
    stats = engine.get_stats()
    print(f"性能统计: {stats}")
