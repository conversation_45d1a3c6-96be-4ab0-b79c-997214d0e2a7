#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Mem0本地化配置 - 简化版本，使用内存向量存储
无需Qdrant，完全基于内存运行
"""

import os
from mem0.configs.base import MemoryConfig
from mem0.llms.configs import LlmConfig
from mem0.embeddings.configs import EmbedderConfig
from mem0.vector_stores.configs import VectorStoreConfig

def get_local_config_simple():
    """获取简化的本地配置 - 使用内存向量存储"""
    
    # LLM配置 - 使用Ollama
    llm_config = LlmConfig(
        provider="ollama",
        config={
            "model": "llama3.1",
            "temperature": 0.7,
            "max_tokens": 1000,
            "ollama_base_url": "http://localhost:11434"
        }
    )
    
    # 嵌入配置 - 使用Ollama
    embedder_config = EmbedderConfig(
        provider="ollama",
        config={
            "model": "nomic-embed-text",
            "ollama_base_url": "http://localhost:11434"
        }
    )
    
    # 向量存储配置 - 使用内存存储
    vector_store_config = VectorStoreConfig(
        provider="chroma",  # 使用Chroma作为轻量级替代
        config={
            "collection_name": "mem0_local",
            "path": "./chroma_db"  # 本地存储路径
        }
    )
    
    # 创建完整的Memory配置
    config = MemoryConfig(
        llm=llm_config,
        embedder=embedder_config,
        vector_store=vector_store_config,
        version="v1.1"
    )
    
    return config

def setup_local_env():
    """设置本地环境变量"""
    os.environ.setdefault("OLLAMA_BASE_URL", "http://localhost:11434")
    os.environ.setdefault("MEM0_TELEMETRY", "false")  # 禁用遥测

    # 禁用OpenAI相关的环境变量检查
    os.environ.setdefault("OPENAI_API_KEY", "not-needed")

def get_memory_client():
    """获取Memory客户端实例"""
    try:
        from mem0 import Memory

        # 设置环境
        setup_local_env()

        # 获取配置
        config = get_local_config_simple()

        # 创建Memory实例
        memory = Memory(config=config)

        return memory

    except Exception as e:
        print(f"创建Memory客户端失败: {e}")
        raise

if __name__ == "__main__":
    # 测试配置
    setup_local_env()
    config = get_local_config_simple()
    print("✅ 简化本地配置创建成功")
    print(f"LLM: {config.llm.provider} - {config.llm.config.get('model', 'unknown')}")
    print(f"Embedder: {config.embedder.provider} - {config.embedder.config.get('model', 'unknown')}")
    print(f"Vector Store: {config.vector_store.provider}")
