#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
库存事务数据模型
根据需求文档采用事务表设计模式
"""

from sqlalchemy import Column, Integer, String, Text, Numeric, Boolean, DateTime, ForeignKey, Enum
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from config.database import Base
import enum

class TransactionType(enum.Enum):
    """事务类型枚举"""
    INBOUND = "inbound"  # 入库
    OUTBOUND = "outbound"  # 出库

class RelatedOrderType(enum.Enum):
    """关联订单类型枚举"""
    INBOUND_ORDER = "inbound_order"
    OUTBOUND_ORDER = "outbound_order"

class InventoryTransaction(Base):
    """库存事务表 - 记录所有出入库操作"""
    __tablename__ = 'inventory_transactions'
    
    id = Column(Integer, primary_key=True)
    transaction_no = Column(String(50), unique=True, nullable=False)
    transaction_type = Column(Enum(TransactionType), nullable=False)
    sub_type = Column(String(20))  # purchase/return/transfer/borrow/issue等
    product_id = Column(Integer, ForeignKey('products.id'), nullable=False)
    location_id = Column(Integer, ForeignKey('locations.id'), nullable=False)
    quantity = Column(Integer, nullable=False)  # 正数入库，负数出库
    unit_price = Column(Numeric(10, 2))
    total_amount = Column(Numeric(12, 2))
    
    # 入库相关字段
    supplier_id = Column(Integer, ForeignKey('suppliers.id'))
    batch_no = Column(String(50))
    expire_date = Column(DateTime)
    
    # 出库相关字段
    department = Column(String(100))
    applicant = Column(String(100))
    equipment = Column(String(100))
    
    # 通用字段
    operator_id = Column(Integer, ForeignKey('users.id'))
    related_order_id = Column(Integer)
    related_order_type = Column(Enum(RelatedOrderType))
    remark = Column(Text)
    transaction_date = Column(DateTime, default=func.now())
    
    # 关系
    product = relationship("Product")
    location = relationship("Location")
    supplier = relationship("Supplier")
    operator = relationship("User")
    
    def __repr__(self):
        return f"<InventoryTransaction(id={self.id}, no='{self.transaction_no}', type='{self.transaction_type}')>"
    
    def to_dict(self):
        """转换为字典"""
        return {
            'id': self.id,
            'transaction_no': self.transaction_no,
            'transaction_type': self.transaction_type.value if self.transaction_type else None,
            'sub_type': self.sub_type,
            'product_id': self.product_id,
            'product_code': self.product.code if self.product else None,
            'product_name': self.product.name if self.product else None,
            'location_id': self.location_id,
            'location_code': self.location.location_code if self.location else None,
            'quantity': self.quantity,
            'unit_price': float(self.unit_price) if self.unit_price else None,
            'total_amount': float(self.total_amount) if self.total_amount else None,
            'supplier_id': self.supplier_id,
            'supplier_name': self.supplier.name if self.supplier else None,
            'batch_no': self.batch_no,
            'expire_date': self.expire_date.isoformat() if self.expire_date else None,
            'department': self.department,
            'applicant': self.applicant,
            'equipment': self.equipment,
            'operator_id': self.operator_id,
            'operator_name': self.operator.real_name if self.operator else None,
            'related_order_id': self.related_order_id,
            'related_order_type': self.related_order_type.value if self.related_order_type else None,
            'remark': self.remark,
            'transaction_date': self.transaction_date.isoformat() if self.transaction_date else None
        }
    
    @classmethod
    def generate_transaction_no(cls, session, transaction_type: TransactionType, date_str: str = None) -> str:
        """生成事务编号
        
        Args:
            session: 数据库会话
            transaction_type: 事务类型
            date_str: 日期字符串 (YYYYMMDD)，默认为今天
            
        Returns:
            事务编号 (如 IN20240603001, OUT20240603001)
        """
        if not date_str:
            from datetime import datetime
            date_str = datetime.now().strftime('%Y%m%d')
        
        prefix = "IN" if transaction_type == TransactionType.INBOUND else "OUT"
        prefix_with_date = f"{prefix}{date_str}"
        
        # 查询当天最大序号
        transactions = session.query(cls).filter(
            cls.transaction_no.like(f"{prefix_with_date}%")
        ).all()
        
        max_sequence = 0
        for transaction in transactions:
            try:
                sequence_str = transaction.transaction_no.replace(prefix_with_date, "")
                sequence = int(sequence_str)
                if sequence > max_sequence:
                    max_sequence = sequence
            except ValueError:
                continue
        
        next_sequence = max_sequence + 1
        return f"{prefix_with_date}{next_sequence:03d}" 