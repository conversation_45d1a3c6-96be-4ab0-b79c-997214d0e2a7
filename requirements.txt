# 武汉总厂仓库管理系统 - 依赖包列表
# 使用国内镜像源安装: pip install -r requirements.txt -i https://pypi.tuna.tsinghua.edu.cn/simple/

# PyQt6 相关
PyQt6>=6.4.0
PyQt6-tools>=6.4.0
PyQt6-Charts>=6.4.0
qt-material>=2.14

# 数据库相关
PyMySQL>=1.1.0
mysqlclient>=2.2.0
SQLAlchemy>=2.0.0
alembic>=1.12.0

# 数据处理
pandas>=2.0.0
numpy>=1.24.0
openpyxl>=3.1.0

# 图像处理
Pillow>=10.0.0

# 网络请求
requests>=2.31.0

# 加密安全
cryptography>=41.0.0
bcrypt>=4.0.0

# 日志和配置
python-dotenv>=1.0.0
loguru>=0.7.0

# 图表绘制
matplotlib>=3.7.0
plotly>=5.15.0

# 3D可视化相关
dash>=3.0.0
dash-bootstrap-components>=2.0.0
pythreejs>=2.4.0
ipywidgets>=8.0.0
vtk>=9.4.0
mayavi>=4.8.0
open3d>=0.19.0
trimesh>=4.6.0
pyvista>=0.45.0
pyvistaqt>=0.11.0
ipyvolume>=0.6.0
vispy>=0.15.0
moderngl>=5.12.0
moderngl-window>=3.1.0

# 工具库
python-dateutil>=2.8.0
validators>=0.20.0

# 开发工具
pytest>=7.4.0
pytest-qt>=4.2.0
black>=23.7.0
flake8>=6.0.0

# 打包部署
pyinstaller>=5.13.0
cx-Freeze>=6.15.0