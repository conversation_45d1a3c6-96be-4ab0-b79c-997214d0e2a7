#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
MySQL数据库创建脚本
"""

import pymysql
import os

def create_database():
    """创建MySQL数据库"""
    try:
        # 连接到MySQL服务器
        conn = pymysql.connect(
            host='localhost',
            port=3306,
            user='root',
            password='179100215'
        )
        
        cursor = conn.cursor()
        
        # 创建数据库（如果不存在）
        cursor.execute("CREATE DATABASE IF NOT EXISTS warehouse_management CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci")
        print("✓ 数据库 warehouse_management 创建成功")
        
        cursor.close()
        conn.close()
        
        # 连接到新创建的数据库并执行初始化脚本
        conn = pymysql.connect(
            host='localhost',
            port=3306,
            user='root',
            password='179100215',
            database='warehouse_management',
            charset='utf8mb4'
        )
        
        cursor = conn.cursor()
        
        # 获取MySQL版本
        cursor.execute("SELECT VERSION()")
        version = cursor.fetchone()[0]
        print(f"📊 MySQL版本: {version}")
        
        # 执行初始化脚本
        sql_file = 'database_init_mysql.sql'
        if os.path.exists(sql_file):
            print(f"📄 执行SQL脚本: {sql_file}")
            with open(sql_file, 'r', encoding='utf-8') as f:
                sql_content = f.read()
                
            # 分割SQL语句并执行
            sql_statements = sql_content.split(';')
            for statement in sql_statements:
                statement = statement.strip()
                if statement and not statement.startswith('--'):
                    try:
                        cursor.execute(statement)
                    except Exception as e:
                        if 'already exists' not in str(e):
                            print(f"⚠️ SQL执行警告: {e}")
            
            conn.commit()
            print("✓ 数据库初始化完成")
        else:
            print(f"❌ SQL脚本文件不存在: {sql_file}")
        
        cursor.close()
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ 数据库创建失败: {e}")
        return False

if __name__ == "__main__":
    create_database()
