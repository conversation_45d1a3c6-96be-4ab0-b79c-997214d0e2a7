---
title: Overview
description: How to integrate Mem0 into other frameworks
---

<Snippet file="paper-release.mdx" />

Mem0 seamlessly integrates with popular AI frameworks and tools to enhance your LLM-based applications with persistent memory capabilities. By integrating Mem0, your applications benefit from:

- Enhanced context management across multiple frameworks
- Consistent memory persistence across different LLM interactions
- Optimized token usage through efficient memory retrieval
- Framework-agnostic memory layer
- Simple integration with existing AI tools and frameworks

Here are the available integrations for Mem0:

## Integrations

<CardGroup cols={2}>
  <Card
    title="AgentOps"
    icon={
      <svg
        xmlns="http://www.w3.org/2000/svg"
        width="25"
        height="26"
        viewBox="0 0 30 36"
        fill="none"
      >
      <path d="M10.4659 6.47277C10.45 6.37428 10.4381 6.27986 10.4303 6.18101L10.4285 6.16388C10.4212 6.09482 10.414 6.02566 10.4106 5.95626L1.18538 21.8752C0.505422 23.0493 0.323356 24.4208 0.675227 25.7289C0.849119 26.3869 1.14971 26.9859 1.55323 27.5098C1.95675 28.0338 2.46282 28.4751 3.05175 28.8143C3.83464 29.2675 4.70856 29.5 5.59028 29.5C6.03318 29.5 6.4798 29.4408 6.91899 29.3226C8.23581 28.972 9.3349 28.1326 10.0152 26.9545L15.9268 16.749V16.7449L16.5001 15.7637L17.6431 13.7936L16.5001 11.8234L15.9309 10.8381L15.9268 10.8341L13.7836 7.13406C13.6651 6.933 13.5741 6.72418 13.5109 6.51165C13.2817 5.80223 13.3292 5.04172 13.6097 4.37599L13.8115 4.02535C14.3532 3.09155 15.31 2.53987 16.3184 2.47692C16.3738 2.46915 16.4251 2.46915 16.4804 2.46915C16.5421 2.46915 16.6038 2.47257 16.6654 2.47599L16.6822 2.47692C17.6906 2.53987 18.6474 3.09155 19.1892 4.02535L21.2216 7.52838L21.8146 8.55289L21.8421 8.60399L30.1024 22.8601C30.5174 23.5814 30.6281 24.4167 30.4148 25.2205C30.1975 26.0244 29.6832 26.6942 28.9598 27.1081C28.2364 27.5258 27.3977 27.6361 26.5911 27.4195C25.7844 27.2066 25.1123 26.6905 24.6968 25.9696L18.2119 14.7788L17.069 16.7449L22.9847 26.9545C23.6646 28.1326 24.7641 28.972 26.0809 29.3226C26.5197 29.4408 26.9626 29.5 27.4096 29.5C28.2914 29.5 29.1612 29.2675 29.9482 28.8143C31.1264 28.1367 31.9728 27.0411 32.3247 25.7289C32.6766 24.4208 32.4949 23.0493 31.8145 21.8752L21.1261 3.43034C20.7029 2.51617 20.0033 1.72011 19.0621 1.18027C18.5281 0.877027 17.9708 0.675975 17.3975 0.581189C17.3027 0.565268 17.2076 0.549717 17.1129 0.537868C17.0099 0.52602 16.9074 0.518244 16.8045 0.510469C16.6027 0.498621 16.3972 0.494548 16.1914 0.510469C16.0885 0.518244 15.9859 0.52639 15.883 0.537868C15.795 0.54887 15.7067 0.563384 15.6187 0.577852L15.5984 0.581189C15.0291 0.675605 14.4673 0.876657 13.9375 1.18027C12.9885 1.72789 12.2766 2.53579 11.8537 3.46181C11.7742 3.63473 11.707 3.81282 11.6471 3.99314C11.6361 4.02668 11.6269 4.06051 11.6177 4.09435C11.612 4.11503 11.6064 4.13579 11.6003 4.15642C11.5624 4.28601 11.5275 4.41634 11.4996 4.54853C11.4885 4.60231 11.4794 4.65668 11.4703 4.71111L11.4666 4.73329C11.4443 4.86399 11.4264 4.99543 11.4145 5.12762C11.4093 5.18686 11.4045 5.24573 11.4012 5.30534C11.3934 5.44567 11.3923 5.58637 11.3963 5.72744C11.3969 5.74403 11.3962 5.76062 11.3956 5.7772C11.3949 5.79616 11.3942 5.81512 11.3952 5.83407C11.3952 5.86184 11.3952 5.88924 11.3993 5.92071C11.3998 5.9291 11.4006 5.93736 11.4014 5.94564C11.402 5.95125 11.4026 5.95687 11.403 5.96255C11.4045 5.98181 11.4064 6.00106 11.4082 6.02031C11.4097 6.03577 11.4109 6.05122 11.4122 6.06674C11.4142 6.09134 11.4163 6.11621 11.419 6.14139L11.4428 6.32282C11.4506 6.38983 11.4625 6.46092 11.4744 6.52757C11.5063 6.68863 11.5468 6.84896 11.5936 7.0078C11.5944 7.0102 11.5949 7.0127 11.5955 7.0152C11.5958 7.01662 11.5961 7.01804 11.5965 7.01944C11.5967 7.02051 11.597 7.02157 11.5974 7.02261C11.6483 7.19293 11.7081 7.36177 11.7787 7.52838C11.8619 7.72943 11.9607 7.92641 12.0715 8.11932L12.3245 8.5566V8.56067L12.4984 8.85614L12.7199 9.24232H12.7239L12.728 9.25417L14.7802 12.7927V12.7968L14.7883 12.805V12.809L15.3576 13.7943L14.7883 14.7796L8.30344 25.9703C7.88431 26.6912 7.21216 27.2077 6.40921 27.4202C6.14019 27.4913 5.86338 27.5306 5.59474 27.5306C5.053 27.5306 4.51906 27.3888 4.04085 27.1089C3.31705 26.6953 2.79909 26.0251 2.58581 25.2213C2.36845 24.4174 2.47917 23.5821 2.89829 22.8609L11.1585 8.60473L11.186 8.56141V8.55734C11.1266 8.45478 11.0753 8.35629 11.024 8.25409C11.0105 8.22496 10.9969 8.19611 10.9834 8.16739C10.9458 8.08735 10.9086 8.00836 10.8739 7.92715C10.8718 7.92504 10.8708 7.92194 10.8698 7.91887C10.8688 7.91602 10.8679 7.91319 10.8661 7.91123V7.90346C10.8423 7.8483 10.8186 7.79311 10.7989 7.73795C10.7476 7.60799 10.7041 7.47803 10.6644 7.3477C10.6012 7.15479 10.5536 6.96152 10.518 6.76861C10.4942 6.67012 10.4786 6.5757 10.4667 6.47684C10.4667 6.47684 10.47 6.47684 10.4659 6.47277Z" fill="currentColor"></path>
      </svg>
    }
    href="/integrations/agentops"
  >
    Monitor and analyze Mem0 operations with comprehensive AI agent analytics and LLM observability.
  </Card>
  <Card
    title="LangChain"
    icon={
      <svg
        role="img"
        viewBox="0 0 24 24"
        xmlns="http://www.w3.org/2000/svg"
        width="32"
        height="32"
      >
        <title>LangChain</title>
        <path
          d="M6.0988 5.9175C2.7359 5.9175 0 8.6462 0 12s2.736 6.0825 6.0988 6.0825h11.8024C21.2641 18.0825 24 15.3538 24 12s-2.736 -6.0825 -6.0988 -6.0825ZM5.9774 7.851c0.493 0.0124 1.02 0.2496 1.273 0.6228 0.3673 0.4592 0.4778 1.0668 0.8944 1.4932 0.5604 0.6118 1.199 1.1505 1.7161 1.802 0.4892 0.5954 0.8386 1.2937 1.1436 1.9975 0.1244 0.2335 0.1257 0.5202 0.31 0.7197 0.0908 0.1204 0.5346 0.4483 0.4383 0.5645 0.0555 0.1204 0.4702 0.286 0.3263 0.4027 -0.1944 0.04 -0.4129 0.0476 -0.5616 -0.1074 -0.0549 0.126 -0.183 0.0596 -0.2819 0.0432a4 4 0 0 0 -0.025 0.0736c-0.3288 0.0219 -0.5754 -0.3126 -0.732 -0.565 -0.3111 -0.168 -0.6642 -0.2702 -0.982 -0.446 -0.0182 0.2895 0.0452 0.6485 -0.231 0.8353 -0.014 0.5565 0.8436 0.0656 0.9222 0.4804 -0.061 0.0067 -0.1286 -0.0095 -0.1774 0.0373 -0.2239 0.2172 -0.4805 -0.1645 -0.7385 -0.007 -0.3464 0.174 -0.3808 0.3161 -0.8096 0.352 -0.0237 -0.0359 -0.0143 -0.0592 0.0059 -0.0811 0.1207 -0.1399 0.1295 -0.3046 0.3356 -0.3643 -0.2122 -0.0334 -0.3899 0.0833 -0.5686 0.1757 -0.2323 0.095 -0.2304 -0.2141 -0.5878 0.0164 -0.0396 -0.0322 -0.0208 -0.0615 0.0018 -0.0864 0.0908 -0.1107 0.2102 -0.127 0.345 -0.1208 -0.663 -0.3686 -0.9751 0.4507 -1.2813 0.0432 -0.092 0.0243 -0.1265 0.1068 -0.1845 0.1652 -0.05 -0.0548 -0.0123 -0.1212 -0.0099 -0.1857 -0.0598 -0.028 -0.1356 -0.041 -0.1179 -0.1366 -0.1171 -0.0395 -0.1988 0.0295 -0.286 0.0952 -0.0787 -0.0608 0.0532 -0.1492 0.0776 -0.2125 0.0702 -0.1216 0.23 -0.025 0.3111 -0.1126 0.2306 -0.1308 0.552 0.0814 0.8155 0.0455 0.203 0.0255 0.4544 -0.1825 0.3526 -0.39 -0.2171 -0.2767 -0.179 -0.6386 -0.1839 -0.9695 -0.0268 -0.1929 -0.491 -0.4382 -0.6252 -0.6462 -0.1659 -0.1873 -0.295 -0.4047 -0.4243 -0.6182 -0.4666 -0.9008 -0.3198 -2.0584 -0.9077 -2.8947 -0.266 0.1466 -0.6125 0.0774 -0.8418 -0.119 -0.1238 0.1125 -0.1292 0.2598 -0.139 0.4161 -0.297 -0.2962 -0.2593 -0.8559 -0.022 -1.1855 0.0969 -0.1302 0.2127 -0.2373 0.342 -0.3316 0.0292 -0.0213 0.0391 -0.0419 0.0385 -0.0747 0.1174 -0.5267 0.5764 -0.7391 1.0694 -0.7267m12.4071 0.46c0.5575 0 1.0806 0.2159 1.474 0.6082s0.61 0.9145 0.61 1.4704c0 0.556 -0.2167 1.078 -0.61 1.4698v0.0006l-0.902 0.8995a2.08 2.08 0 0 1 -0.8597 0.5166l-0.0164 0.0047 -0.0058 0.0164a2.05 2.05 0 0 1 -0.474 0.7308l-0.9018 0.8995c-0.3934 0.3924 -0.917 0.6083 -1.4745 0.6083s-1.0806 -0.216 -1.474 -0.6083c-0.813 -0.8107 -0.813 -2.1294 0 -2.9402l0.9019 -0.8995a2.056 2.056 0 0 1 0.858 -0.5143l0.017 -0.0053 0.0058 -0.0158a2.07 2.07 0 0 1 0.4752 -0.7337l0.9018 -0.8995c0.3934 -0.3924 0.9171 -0.6083 1.4745 -0.6083zm0 0.8965a1.18 1.18 0 0 0 -0.8388 0.3462l-0.9018 0.8995a1.181 1.181 0 0 0 -0.3427 0.9252l0.0053 0.0572c0.0323 0.2652 0.149 0.5044 0.3374 0.6917 0.13 0.1296 0.2733 0.2114 0.4471 0.2686a0.9 0.9 0 0 1 0.014 0.1582 0.884 0.884 0 0 1 -0.2609 0.6304l-0.0554 0.0554c-0.3013 -0.1028 -0.5525 -0.253 -0.7794 -0.4792a2.06 2.06 0 0 1 -0.5761 -1.0968l-0.0099 -0.0578 -0.0461 0.0368a1.1 1.1 0 0 0 -0.0876 0.0794l-0.9024 0.8995c-0.4623 0.461 -0.4623 1.212 0 1.673 0.2311 0.2305 0.535 0.346 0.8394 0.3461 0.3043 0 0.6077 -0.1156 0.8388 -0.3462l0.9019 -0.8995c0.4623 -0.461 0.4623 -1.2113 0 -1.673a1.17 1.17 0 0 0 -0.4367 -0.2749 1 1 0 0 1 -0.014 -0.1611c0 -0.2591 0.1023 -0.505 0.2901 -0.6923 0.3019 0.1028 0.57 0.2694 0.7962 0.495 0.3007 0.2999 0.4994 0.679 0.5756 1.0968l0.0105 0.0578 0.0455 -0.0373a1.1 1.1 0 0 0 0.0887 -0.0794l0.902 -0.8996c0.4622 -0.461 0.4628 -1.2124 0 -1.6735a1.18 1.18 0 0 0 -0.8395 -0.3462Zm-9.973 5.1567 -0.0006 0.0006c-0.0793 0.3078 -0.1048 0.8318 -0.506 0.847 -0.033 0.1776 0.1228 0.2445 0.2655 0.1874 0.141 -0.0645 0.2081 0.0508 0.2557 0.1657 0.2177 0.0317 0.5394 -0.0725 0.5516 -0.3298 -0.325 -0.1867 -0.4253 -0.5418 -0.5662 -0.8709"
          fill="currentColor"
        />
      </svg>
    }
    href="/integrations/langchain"
  >
    Integrate Mem0 with LangChain to build powerful agents with memory
    capabilities.
  </Card>
  <Card
    title="LlamaIndex"
    icon={
      <svg
        width="24"
        height="24"
        viewBox="0 0 80 80"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M0 16C0 7.16344 7.16925 0 16.013 0H64.0518C72.8955 0 80.0648 7.16344 80.0648 16V64C80.0648 72.8366 72.8955 80 64.0518 80H16.013C7.16924 80 0 72.8366 0 64V16Z"
          fill="currentColor"
        />
        <path
          d="M50.3091 52.6201C45.1552 54.8952 39.5718 53.963 37.4243 53.2126C37.4243 53.726 37.4009 55.3218 37.3072 57.597C37.2135 59.8721 36.4873 61.3099 36.1359 61.7444C36.1749 63.1664 36.2062 66.271 36.0188 67.3138C35.8313 68.3566 35.1598 69.2493 34.8474 69.5652H31.6848C31.9659 68.1433 33.0513 67.2348 33.5589 66.9583C33.84 64.0195 33.2856 61.4679 32.9733 60.5594C32.6609 61.6654 31.8956 64.2328 31.3334 65.6548C30.7711 67.0768 29.9278 68.3803 29.5763 68.8543H27.2337C27.1165 67.4323 27.8974 66.9583 28.405 66.9583C28.6393 66.5238 29.2015 65.1571 29.5763 63.1664C29.9512 61.1756 29.4202 57.439 29.1078 55.8195V50.7241C25.3595 48.7096 23.9539 46.6952 23.0168 44.4437C22.2672 42.6425 22.4702 39.9013 22.6654 38.7558C22.4311 38.3213 21.7481 37.217 21.4941 35.6749C21.1427 33.5419 21.3379 32.0014 21.4941 31.1719C21.2598 30.9349 20.7913 29.7263 20.7913 26.7875C20.7913 23.8488 21.6502 22.3241 22.0797 21.9291V20.6256C20.4398 20.5071 18.7999 19.7961 17.8629 18.8482C16.9258 17.9002 17.6286 16.4782 18.2143 16.0042C18.7999 15.5302 19.3856 15.8857 20.2056 15.6487C21.0255 15.4117 21.7283 15.1747 22.0797 14.4637C22.3608 13.895 21.8064 11.5408 21.494 10.4348C22.8997 10.6244 23.7977 11.8568 24.071 12.4493V10.4348C25.828 11.2643 28.9907 13.2788 30.0449 17.6632C30.8882 21.1707 31.4895 28.5255 31.6847 31.7645C36.1749 31.804 41.8755 31.1211 47.0294 32.2384C51.7148 33.2542 53.8232 35.3194 56.283 35.3194C58.7428 35.3194 60.1484 33.8974 61.9055 35.0824C63.6625 36.2674 64.5996 39.5853 64.3653 42.0738C64.1779 44.0645 62.6473 44.7202 61.9055 44.7992C60.9684 47.9276 61.9055 50.9216 62.4911 52.0276V56.5305C62.7645 56.9255 63.3111 58.1421 63.3111 59.8484C63.3111 61.5548 62.7645 62.6924 62.4911 63.0479C62.9597 65.7022 62.2959 68.4198 61.9055 69.4468H58.7428C59.1177 68.4988 59.758 68.2618 60.0313 68.2618C60.5936 65.3231 60.1875 62.6134 59.9142 61.6259C58.1337 60.5831 56.9858 58.7425 56.6344 57.9525C56.6735 58.624 56.5641 60.4883 55.8145 62.5739C55.0648 64.6595 53.9403 65.8918 53.4718 66.2473V68.7358H50.3091C50.3091 67.219 51.1681 66.9188 51.5976 66.9583C52.1443 65.9708 53.4718 64.4699 53.4718 61.5074C53.4718 59.0077 51.7148 57.834 50.4263 55.5825C49.8141 54.5128 50.1139 53.1731 50.3091 52.6201Z"
          fill="url(#paint0_linear_3021_4156)"
        />
        <defs>
          <linearGradient
            id="paint0_linear_3021_4156"
            x1="21.1546"
            y1="15.4117"
            x2="71.8865"
            y2="57.9279"
            gradientUnits="userSpaceOnUse"
          >
            <stop offset="0.0619804" stop-color="#F6DCD9" />
            <stop offset="0.325677" stop-color="#FFA5EA" />
            <stop offset="0.589257" stop-color="#45DFF8" />
            <stop offset="1" stop-color="#BC8DEB" />
          </linearGradient>
        </defs>
      </svg>
    }
    href="/integrations/llama-index"
  >
    Build RAG applications with LlamaIndex and Mem0.
  </Card>
  <Card
    title="AutoGen"
    icon={
      <svg
        xmlns="http://www.w3.org/2000/svg"
        width="24"
        height="24"
        viewBox="0 0 96 85"
        fill="none"
      >
        <rect width="96" height="85" rx="6" fill="#2D2D2F" />
        <path
          d="M32.6484 28.7109L23.3672 57H15.8906L28.5703 22.875H33.3281L32.6484 28.7109ZM40.3594 57L31.0547 28.7109L30.3047 22.875H35.1094L47.8594 57H40.3594ZM39.9375 44.2969V49.8047H21.9141V44.2969H39.9375ZM77.6484 39.1641V52.6875C77.1172 53.3281 76.2969 54.0234 75.1875 54.7734C74.0781 55.5078 72.6484 56.1406 70.8984 56.6719C69.1484 57.2031 67.0312 57.4688 64.5469 57.4688C62.3438 57.4688 60.3359 57.1094 58.5234 56.3906C56.7109 55.6562 55.1484 54.5859 53.8359 53.1797C52.5391 51.7734 51.5391 50.0547 50.8359 48.0234C50.1328 45.9766 49.7812 43.6406 49.7812 41.0156V38.8828C49.7812 36.2578 50.1172 33.9219 50.7891 31.875C51.4766 29.8281 52.4531 28.1016 53.7188 26.6953C54.9844 25.2891 56.4922 24.2188 58.2422 23.4844C59.9922 22.75 61.9375 22.3828 64.0781 22.3828C67.0469 22.3828 69.4844 22.8672 71.3906 23.8359C73.2969 24.7891 74.75 26.1172 75.75 27.8203C76.7656 29.5078 77.3906 31.4453 77.625 33.6328H70.8047C70.6328 32.4766 70.3047 31.4688 69.8203 30.6094C69.3359 29.75 68.6406 29.0781 67.7344 28.5938C66.8438 28.1094 65.6875 27.8672 64.2656 27.8672C63.0938 27.8672 62.0469 28.1094 61.125 28.5938C60.2188 29.0625 59.4531 29.7578 58.8281 30.6797C58.2031 31.6016 57.7266 32.7422 57.3984 34.1016C57.0703 35.4609 56.9062 37.0391 56.9062 38.8359V41.0156C56.9062 42.7969 57.0781 44.375 57.4219 45.75C57.7656 47.1094 58.2734 48.2578 58.9453 49.1953C59.6328 50.1172 60.4766 50.8125 61.4766 51.2812C62.4766 51.75 63.6406 51.9844 64.9688 51.9844C66.0781 51.9844 67 51.8906 67.7344 51.7031C68.4844 51.5156 69.0859 51.2891 69.5391 51.0234C70.0078 50.7422 70.3672 50.4766 70.6172 50.2266V44.1797H64.1953V39.1641H77.6484Z"
          fill="white"
        />
      </svg>
    }
    href="/integrations/autogen"
  >
    Build multi-agent systems with persistent memory capabilities.
  </Card>
  <Card
    title="CrewAI"
    icon={
      <svg
        xmlns="http://www.w3.org/2000/svg"
        width="24"
        height="24"
        viewBox="0 0 48 48"
        preserveAspectRatio="xMidYMid meet"
      >
        <g
          transform="translate(0.000000,48.000000) scale(0.100000,-0.100000)"
          fill="currentColor"
          stroke="none"
        >
          <path d="M252 469 c-103 -22 -213 -172 -214 -294 -1 -107 60 -168 168 -167 130 1 276 133 234 211 -13 25 -27 26 -52 4 -31 -27 -32 -6 -4 56 34 77 33 103 -6 146 -38 40 -78 55 -126 44z m103 -40 c44 -39 46 -82 9 -163 -27 -60 -42 -68 -74 -36 -24 24 -26 67 -5 117 22 51 19 60 -11 32 -72 -65 -125 -189 -105 -242 9 -23 16 -27 53 -27 54 0 122 33 154 76 34 44 54 44 54 1 0 -75 -125 -167 -225 -167 -121 0 -181 92 -145 222 17 58 86 153 137 187 63 42 110 42 158 0z" />
        </g>
      </svg>
    }
    href="/integrations/crewai"
  >
    Develop collaborative AI agents with shared memory using CrewAI and Mem0.
  </Card>
  <Card
    title="LangGraph"
    icon={
      <svg
        width="32"
        height="32"
        viewBox="0 0 63 33"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          fill-rule="evenodd"
          clip-rule="evenodd"
          d="M16.0556 0.580566H46.6516C55.3698 0.580566 62.4621 7.69777 62.4621 16.4459C62.4621 25.194 55.3698 32.3112 46.6516 32.3112H16.0556C7.16924 32.3112 0.245117 25.194 0.245117 16.4459C0.245117 7.69777 7.16924 0.580566 16.0556 0.580566ZM30.103 25.1741C30.487 25.5781 31.0556 25.5581 31.5593 25.4534L31.5643 25.4559C31.7981 25.2657 31.4658 25.0248 31.1484 24.7948C30.9581 24.6569 30.7731 24.5228 30.7189 24.406C30.8946 24.1917 30.375 23.7053 29.9704 23.3266C29.8006 23.1676 29.6511 23.0276 29.5818 22.9347C29.2939 22.6213 29.1782 22.226 29.0618 21.8283C28.9846 21.5645 28.9071 21.2996 28.7788 21.0569C27.9883 19.2215 27.083 17.401 25.8137 15.8474C24.9979 14.8148 24.0669 13.8901 23.1356 12.965C22.5352 12.3687 21.9347 11.7722 21.3648 11.1466C20.7784 10.5413 20.4255 9.79548 20.072 9.04847C19.7761 8.42309 19.4797 7.79685 19.0456 7.25139C17.7314 5.30625 13.5818 4.77508 12.9733 7.52321C12.9758 7.60799 12.9484 7.66286 12.8735 7.71772C12.5369 7.9646 12.2376 8.2439 11.9858 8.58306C11.3698 9.44341 11.275 10.9023 12.0431 11.6753C12.0442 11.6587 12.0453 11.6422 12.0464 11.6257C12.0721 11.2354 12.0961 10.8705 12.4047 10.5905C12.9982 11.1018 13.8985 11.2838 14.5868 10.9023C15.4166 12.0926 15.681 13.5317 15.9462 14.9756C16.1671 16.1784 16.3887 17.3847 16.9384 18.4534C16.9497 18.4723 16.9611 18.4912 16.9725 18.5101C17.2955 19.048 17.6238 19.5946 18.0381 20.0643C18.1886 20.2976 18.4977 20.5495 18.8062 20.8009C19.2132 21.1326 19.6194 21.4636 19.6591 21.7501C19.6609 21.8748 19.6603 22.0012 19.6598 22.1283C19.6566 22.8808 19.6532 23.6601 20.1354 24.2788C20.4022 24.82 19.7489 25.3636 19.2227 25.2963C18.9342 25.3363 18.619 25.2602 18.306 25.1848C17.8778 25.0815 17.4537 24.9792 17.108 25.1766C17.011 25.2816 16.8716 25.2852 16.7316 25.2889C16.5657 25.2933 16.3987 25.2977 16.3 25.4708C16.2797 25.5223 16.2323 25.5804 16.183 25.6408C16.0748 25.7735 15.9575 25.9173 16.098 26.0269C16.1106 26.0174 16.1231 26.0078 16.1356 25.9983C16.3484 25.8358 16.5513 25.681 16.8386 25.7776C16.8004 25.9899 16.9375 26.0467 17.0745 26.1036C17.0984 26.1135 17.1224 26.1234 17.1454 26.1342C17.1439 26.1835 17.1342 26.2332 17.1245 26.2825C17.1015 26.4004 17.0789 26.516 17.1703 26.618C17.2137 26.5738 17.2521 26.5243 17.2905 26.4746C17.3846 26.353 17.4791 26.2308 17.6491 26.1865C18.023 26.6858 18.3996 26.4784 18.8721 26.2182C19.4051 25.9248 20.0601 25.5641 20.9708 26.0743C20.6217 26.0569 20.3099 26.0993 20.0755 26.3885C20.0182 26.4534 19.9683 26.5282 20.0705 26.613C20.6094 26.2639 20.8336 26.3893 21.0446 26.5074C21.1969 26.5927 21.3423 26.6741 21.5942 26.5706C21.6538 26.5395 21.7133 26.5074 21.7729 26.4752C22.1775 26.257 22.5877 26.0357 23.068 26.1117C22.7093 26.2152 22.5816 26.4426 22.4423 26.6908C22.3734 26.8136 22.3017 26.9414 22.1977 27.0619C22.1429 27.1167 22.1179 27.1815 22.1803 27.2738C22.9315 27.2114 23.2153 27.0209 23.5988 26.7636C23.7818 26.6408 23.9875 26.5027 24.2775 26.3561C24.5981 26.1587 24.9187 26.285 25.2293 26.4073C25.5664 26.54 25.8917 26.6681 26.1927 26.3736C26.2878 26.284 26.4071 26.2829 26.5258 26.2818C26.569 26.2814 26.6122 26.281 26.6541 26.2763C26.5604 25.7745 26.0319 25.7804 25.4955 25.7864C24.875 25.7933 24.2438 25.8004 24.2626 25.022C24.8391 24.6282 24.8444 23.9449 24.8494 23.299C24.8507 23.1431 24.8518 22.9893 24.8611 22.8424C25.2851 23.0788 25.7336 23.2636 26.1794 23.4473C26.5987 23.62 27.0156 23.7917 27.4072 24.0045C27.8162 24.6628 28.4546 25.5357 29.305 25.4783C29.3274 25.411 29.3474 25.3536 29.3723 25.2863C29.4213 25.2949 29.4731 25.308 29.5257 25.3213C29.7489 25.3778 29.9879 25.4384 30.103 25.1741ZM46.7702 17.6925C47.2625 18.1837 47.9304 18.4597 48.6267 18.4597C49.323 18.4597 49.9909 18.1837 50.4832 17.6925C50.9756 17.2013 51.2523 16.5351 51.2523 15.8404C51.2523 15.1458 50.9756 14.4795 50.4832 13.9883C49.9909 13.4971 49.323 13.2212 48.6267 13.2212C48.3006 13.2212 47.9807 13.2817 47.6822 13.3965L46.1773 11.1999L45.1285 11.9184L46.6412 14.1266C46.2297 14.6009 46.0011 15.2089 46.0011 15.8404C46.0011 16.5351 46.2778 17.2013 46.7702 17.6925ZM42.0587 10.5787C42.4271 10.7607 42.8332 10.8539 43.2443 10.8508C43.8053 10.8465 44.3501 10.663 44.7989 10.3274C45.2478 9.99169 45.577 9.52143 45.7385 8.9855C45.9 8.44957 45.8851 7.87615 45.6961 7.34925C45.5072 6.82235 45.154 6.36968 44.6884 6.05757C44.3471 5.82883 43.9568 5.68323 43.5488 5.6325C43.1409 5.58176 42.7266 5.62731 42.3396 5.76548C41.9525 5.90365 41.6033 6.13057 41.3202 6.42797C41.0371 6.72537 40.8279 7.08494 40.7096 7.47773C40.5913 7.87051 40.567 8.28552 40.6389 8.68935C40.7107 9.09317 40.8766 9.47453 41.1233 9.80269C41.3699 10.1309 41.6903 10.3967 42.0587 10.5787ZM42.0587 25.7882C42.4271 25.9702 42.8332 26.0634 43.2443 26.0602C43.8053 26.0559 44.3501 25.8725 44.7989 25.5368C45.2478 25.2011 45.577 24.7309 45.7385 24.195C45.9 23.659 45.8851 23.0856 45.6961 22.5587C45.5072 22.0318 45.154 21.5791 44.6884 21.267C44.3471 21.0383 43.9568 20.8927 43.5488 20.842C43.1409 20.7912 42.7266 20.8368 42.3396 20.9749C41.9525 21.1131 41.6033 21.34 41.3202 21.6374C41.0371 21.9348 40.8279 22.2944 40.7096 22.6872C40.5913 23.08 40.567 23.495 40.6389 23.8988C40.7107 24.3026 40.8766 24.684 41.1233 25.0122C41.3699 25.3403 41.6903 25.6061 42.0587 25.7882ZM44.4725 16.4916V15.1894H40.454C40.3529 14.7946 40.1601 14.4289 39.8911 14.1216L41.4029 11.8819L40.3034 11.1526L38.7916 13.3924C38.5145 13.2923 38.2224 13.2395 37.9277 13.2361C37.2333 13.2361 36.5675 13.5105 36.0765 13.9989C35.5856 14.4874 35.3097 15.1498 35.3097 15.8405C35.3097 16.5313 35.5856 17.1937 36.0765 17.6821C36.5675 18.1705 37.2333 18.4449 37.9277 18.4449C38.2224 18.4416 38.5145 18.3888 38.7916 18.2887L40.3034 20.5284L41.3899 19.7992L39.8911 17.5594C40.1601 17.2522 40.3529 16.8865 40.454 16.4916H44.4725Z"
          fill="currentColor"
        />
      </svg>
    }
    href="/integrations/langgraph"
  >
    Create complex agent workflows with memory persistence using LangGraph.
  </Card>
  <Card
    title="Vercel AI SDK"
    icon={
      <svg
        width="24"
        height="24"
        viewBox="0 0 128 128"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path d="M64.002 8.576 128 119.424H0Z" fill="currentColor" />
      </svg>
    }
    href="/integrations/vercel-ai-sdk"
  >
    Build AI-powered applications with memory using the Vercel AI SDK.
  </Card>
  <Card
    title="LangChain Tools"
    icon={
      <svg
        role="img"
        viewBox="0 0 24 24"
        xmlns="http://www.w3.org/2000/svg"
        width="32"
        height="32"
      >
        <title>LangChain</title>
        <path
          d="M6.0988 5.9175C2.7359 5.9175 0 8.6462 0 12s2.736 6.0825 6.0988 6.0825h11.8024C21.2641 18.0825 24 15.3538 24 12s-2.736 -6.0825 -6.0988 -6.0825ZM5.9774 7.851c0.493 0.0124 1.02 0.2496 1.273 0.6228 0.3673 0.4592 0.4778 1.0668 0.8944 1.4932 0.5604 0.6118 1.199 1.1505 1.7161 1.802 0.4892 0.5954 0.8386 1.2937 1.1436 1.9975 0.1244 0.2335 0.1257 0.5202 0.31 0.7197 0.0908 0.1204 0.5346 0.4483 0.4383 0.5645 0.0555 0.1204 0.4702 0.286 0.3263 0.4027 -0.1944 0.04 -0.4129 0.0476 -0.5616 -0.1074 -0.0549 0.126 -0.183 0.0596 -0.2819 0.0432a4 4 0 0 0 -0.025 0.0736c-0.3288 0.0219 -0.5754 -0.3126 -0.732 -0.565 -0.3111 -0.168 -0.6642 -0.2702 -0.982 -0.446 -0.0182 0.2895 0.0452 0.6485 -0.231 0.8353 -0.014 0.5565 0.8436 0.0656 0.9222 0.4804 -0.061 0.0067 -0.1286 -0.0095 -0.1774 0.0373 -0.2239 0.2172 -0.4805 -0.1645 -0.7385 -0.007 -0.3464 0.174 -0.3808 0.3161 -0.8096 0.352 -0.0237 -0.0359 -0.0143 -0.0592 0.0059 -0.0811 0.1207 -0.1399 0.1295 -0.3046 0.3356 -0.3643 -0.2122 -0.0334 -0.3899 0.0833 -0.5686 0.1757 -0.2323 0.095 -0.2304 -0.2141 -0.5878 0.0164 -0.0396 -0.0322 -0.0208 -0.0615 0.0018 -0.0864 0.0908 -0.1107 0.2102 -0.127 0.345 -0.1208 -0.663 -0.3686 -0.9751 0.4507 -1.2813 0.0432 -0.092 0.0243 -0.1265 0.1068 -0.1845 0.1652 -0.05 -0.0548 -0.0123 -0.1212 -0.0099 -0.1857 -0.0598 -0.028 -0.1356 -0.041 -0.1179 -0.1366 -0.1171 -0.0395 -0.1988 0.0295 -0.286 0.0952 -0.0787 -0.0608 0.0532 -0.1492 0.0776 -0.2125 0.0702 -0.1216 0.23 -0.025 0.3111 -0.1126 0.2306 -0.1308 0.552 0.0814 0.8155 0.0455 0.203 0.0255 0.4544 -0.1825 0.3526 -0.39 -0.2171 -0.2767 -0.179 -0.6386 -0.1839 -0.9695 -0.0268 -0.1929 -0.491 -0.4382 -0.6252 -0.6462 -0.1659 -0.1873 -0.295 -0.4047 -0.4243 -0.6182 -0.4666 -0.9008 -0.3198 -2.0584 -0.9077 -2.8947 -0.266 0.1466 -0.6125 0.0774 -0.8418 -0.119 -0.1238 0.1125 -0.1292 0.2598 -0.139 0.4161 -0.297 -0.2962 -0.2593 -0.8559 -0.022 -1.1855 0.0969 -0.1302 0.2127 -0.2373 0.342 -0.3316 0.0292 -0.0213 0.0391 -0.0419 0.0385 -0.0747 0.1174 -0.5267 0.5764 -0.7391 1.0694 -0.7267m12.4071 0.46c0.5575 0 1.0806 0.2159 1.474 0.6082s0.61 0.9145 0.61 1.4704c0 0.556 -0.2167 1.078 -0.61 1.4698v0.0006l-0.902 0.8995a2.08 2.08 0 0 1 -0.8597 0.5166l-0.0164 0.0047 -0.0058 0.0164a2.05 2.05 0 0 1 -0.474 0.7308l-0.9018 0.8995c-0.3934 0.3924 -0.917 0.6083 -1.4745 0.6083s-1.0806 -0.216 -1.474 -0.6083c-0.813 -0.8107 -0.813 -2.1294 0 -2.9402l0.9019 -0.8995a2.056 2.056 0 0 1 0.858 -0.5143l0.017 -0.0053 0.0058 -0.0158a2.07 2.07 0 0 1 0.4752 -0.7337l0.9018 -0.8995c0.3934 -0.3924 0.9171 -0.6083 1.4745 -0.6083zm0 0.8965a1.18 1.18 0 0 0 -0.8388 0.3462l-0.9018 0.8995a1.181 1.181 0 0 0 -0.3427 0.9252l0.0053 0.0572c0.0323 0.2652 0.149 0.5044 0.3374 0.6917 0.13 0.1296 0.2733 0.2114 0.4471 0.2686a0.9 0.9 0 0 1 0.014 0.1582 0.884 0.884 0 0 1 -0.2609 0.6304l-0.0554 0.0554c-0.3013 -0.1028 -0.5525 -0.253 -0.7794 -0.4792a2.06 2.06 0 0 1 -0.5761 -1.0968l-0.0099 -0.0578 -0.0461 0.0368a1.1 1.1 0 0 0 -0.0876 0.0794l-0.9024 0.8995c-0.4623 0.461 -0.4623 1.212 0 1.673 0.2311 0.2305 0.535 0.346 0.8394 0.3461 0.3043 0 0.6077 -0.1156 0.8388 -0.3462l0.9019 -0.8995c0.4623 -0.461 0.4623 -1.2113 0 -1.673a1.17 1.17 0 0 0 -0.4367 -0.2749 1 1 0 0 1 -0.014 -0.1611c0 -0.2591 0.1023 -0.505 0.2901 -0.6923 0.3019 0.1028 0.57 0.2694 0.7962 0.495 0.3007 0.2999 0.4994 0.679 0.5756 1.0968l0.0105 0.0578 0.0455 -0.0373a1.1 1.1 0 0 0 0.0887 -0.0794l0.902 -0.8996c0.4622 -0.461 0.4628 -1.2124 0 -1.6735a1.18 1.18 0 0 0 -0.8395 -0.3462Zm-9.973 5.1567 -0.0006 0.0006c-0.0793 0.3078 -0.1048 0.8318 -0.506 0.847 -0.033 0.1776 0.1228 0.2445 0.2655 0.1874 0.141 -0.0645 0.2081 0.0508 0.2557 0.1657 0.2177 0.0317 0.5394 -0.0725 0.5516 -0.3298 -0.325 -0.1867 -0.4253 -0.5418 -0.5662 -0.8709"
          fill="currentColor"
        />
      </svg>
    }
    href="/integrations/langchain-tools"
  >
    Use Mem0 with LangChain Tools for enhanced agent capabilities.
  </Card>
  <Card
    title="Dify"
    icon={
      <svg
        xmlns="http://www.w3.org/2000/svg"
        width="24"
        height="24"
        viewBox="0 0 200 200"
        fill="none"
      >
        <path
          d="M40 20 H120 C160 20, 160 180, 120 180 H40 V20"
          fill="currentColor"
        />
      </svg>
    }
    href="/integrations/dify"
  >
    Build AI applications with persistent memory using Dify and Mem0.
  </Card>
  <Card
    title="MCP Server"
    icon={
      <svg
        viewBox="0 0 180 180"
        xmlns="http://www.w3.org/2000/svg"
        width="24"
        height="24"
      >
        <path
          d="M45 45 L135 45 M45 90 L135 90 M45 135 L135 135"
          stroke="currentColor"
          strokeWidth="12"
          strokeLinecap="round"
          fill="none"
        />
      </svg>
    }
    href="/integrations/mcp-server"
  >
    Integrate Mem0 as an MCP Server in Cursor.
  </Card>
  <Card
    title="Livekit"
    icon={
      <svg
        viewBox="0 0 24 24"
        xmlns="http://www.w3.org/2000/svg"
        width="24"
        height="24"
      >
        <text
          x="12"
          y="16"
          fontFamily="Arial"
          fontSize="12"
          textAnchor="middle"
          fill="currentColor"
          fontWeight="bold"
        >
          LK
        </text>
      </svg>
    }
    href="/integrations/livekit"
  >
    Integrate Mem0 with Livekit for voice agents.
  </Card>
  <Card
    title="ElevenLabs"
    icon={
      <svg
        xmlns="http://www.w3.org/2000/svg"
        width="24"
        height="24"
        viewBox="0 0 24 24"
        fill="none"
      >
        <rect width="24" height="24" fill="white"/>
        <rect x="8" y="4" width="2" height="16" fill="black"/>
        <rect x="14" y="4" width="2" height="16" fill="black"/>
      </svg>
    }
    href="/integrations/elevenlabs"
  >
    Build voice agents with memory using ElevenLabs Conversational AI.
  </Card>
  <Card
    title="Pipecat"
    icon={
      <svg
        viewBox="0 0 24 24"
        xmlns="http://www.w3.org/2000/svg"
        width="24"
        height="24"
      >
        <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 18c-4.41 0-8-3.59-8-8s3.59-8 8-8 8 3.59 8 8-3.59 8-8 8z" fill="currentColor"/>
        <circle cx="8.5" cy="9" r="1.5" fill="currentColor"/>
        <circle cx="15.5" cy="9" r="1.5" fill="currentColor"/>
        <path d="M12 16c1.66 0 3-1.34 3-3H9c0 1.66 1.34 3 3 3z" fill="currentColor"/>
        <path d="M17.5 12c-.83 0-1.5-.67-1.5-1.5s.67-1.5 1.5-1.5 1.5.67 1.5 1.5-.67 1.5-1.5 1.5z" fill="currentColor"/>
        <path d="M6.5 12c-.83 0-1.5-.67-1.5-1.5S5.67 9 6.5 9s1.5.67 1.5 1.5S7.33 12 6.5 12z" fill="currentColor"/>
      </svg>
    } 
    href="/integrations/pipecat"
  >
    Build conversational AI agents with memory using Pipecat.
  </Card>
  <Card
    title="Agno"
    icon={
      <svg
        xmlns="http://www.w3.org/2000/svg"
        width="24"
        height="24"
        viewBox="0 0 24 24"
        fill="none"
      >
        <path d="M8 4h8v12h8" stroke="currentColor" strokeWidth="2" fill="none" transform="rotate(15, 12, 12)"/>
      </svg>
    }
    href="/integrations/agno"
  >
    Build autonomous agents with memory using Agno framework.
  </Card>

  <Card
    title="Keywords AI"
    icon={
      <svg
        xmlns="http://www.w3.org/2000/svg"
        width="24"
        height="24"
        viewBox="0 0 24 24"
        fill="none"
      >
        <path fill-rule="evenodd" clip-rule="evenodd" d="M9.07513 1.1863C9.21663 1.07722 9.39144 1.01009 9.56624 1.01009C9.83261 1.01009 10.0823 1.12756 10.2405 1.33734L15.0101 7.4964V12.4136L16.4335 13.8401C16.7582 14.1673 16.7582 14.7043 16.4335 15.0316C16.1089 15.3588 15.5762 15.3588 15.2515 15.0316L13.3453 13.1016V8.07538L8.92529 2.36944V2.36105C8.64228 2.00024 8.70887 1.4716 9.07513 1.1863ZM18.976 14.4133C18.8344 14.3778 18.7003 14.3042 18.5894 14.1925L16.9163 12.5059C16.7249 12.3129 16.6416 12.0528 16.6749 11.8094V6.88385H16.6499L11.8553 0.691225C11.7282 0.529117 11.6716 0.333133 11.6803 0.140562C11.134 0.0481292 10.5726 0 10 0C4.47715 0 0 4.47715 0 10C0 15.5228 4.47715 20 10 20C13.9387 20 17.3456 17.7229 18.976 14.4133Z" fill="currentColor"></path>
      </svg>
    }
    href="/integrations/keywords"
  >
    Build AI applications with persistent memory and comprehensive LLM observability.
  </Card>
  <Card
    title="Raycast"
    icon={
      <svg
        xmlns="http://www.w3.org/2000/svg"
        width="24"
        height="24"
        viewBox="0 0 24 24"
        fill="none"
      >
        <path
          d="M3 12L21 12M12 3L12 21M7.5 7.5L16.5 16.5M16.5 7.5L7.5 16.5"
          stroke="currentColor"
          strokeWidth="2"
          strokeLinecap="round"
        />
      </svg>
    }
    href="/integrations/raycast"
  >
    Mem0 Raycast extension for intelligent memory management and retrieval.
  </Card>
  <Card
    title="Mastra"
    icon={
      <svg
        xmlns="http://www.w3.org/2000/svg"
        width="24"
        height="24"
        viewBox="0 0 24 24"
        fill="none"
      >
        <path
          d="M12 2L22 7L12 12L2 7L12 2Z"
          stroke="currentColor"
          strokeWidth="2"
          strokeLinejoin="round"
        />
        <path
          d="M2 17L12 22L22 17"
          stroke="currentColor"
          strokeWidth="2"
          strokeLinejoin="round"
        />
        <path
          d="M2 12L12 17L22 12"
          stroke="currentColor"
          strokeWidth="2"
          strokeLinejoin="round"
        />
      </svg>
    }
    href="/integrations/mastra"
  >
    Build AI agents with persistent memory using Mastra's framework and tools.
  </Card>
</CardGroup>
