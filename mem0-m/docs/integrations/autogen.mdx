Build conversational AI agents with memory capabilities. This integration combines AutoGen for creating AI agents with Mem0 for memory management, enabling context-aware and personalized interactions.

<Snippet file="paper-release.mdx" />

## Overview

In this guide, we'll explore an example of creating a conversational AI system with memory:
- A customer service bot that can recall previous interactions and provide personalized responses.

## Setup and Configuration

Install necessary libraries:

```bash
pip install pyautogen mem0ai openai
```

First, we'll import the necessary libraries and set up our configurations.

<Note>Remember to get the Mem0 API key from [Mem0 Platform](https://app.mem0.ai).</Note>

```python
import os
from autogen import ConversableAgent
from mem0 import MemoryClient
from openai import OpenAI

# Configuration
OPENAI_API_KEY = 'sk-xxx'  # Replace with your actual OpenAI API key
MEM0_API_KEY = 'your-mem0-key'  # Replace with your actual Mem0 API key from https://app.mem0.ai
USER_ID = "customer_service_bot"

# Set up OpenAI API key
os.environ['OPENAI_API_KEY'] = OPENAI_API_KEY
os.environ['MEM0_API_KEY'] = MEM0_API_KEY

# Initialize Mem0 and AutoGen agents
memory_client = MemoryClient()
agent = ConversableAgent(
    "chatbot",
    llm_config={"config_list": [{"model": "gpt-4", "api_key": OPENAI_API_KEY}]},
    code_execution_config=False,
    human_input_mode="NEVER",
)
```

## Storing Conversations in Memory

Add conversation history to Mem0 for future reference:

```python
conversation = [
    {"role": "assistant", "content": "Hi, I'm Best Buy's chatbot! How can I help you?"},
    {"role": "user", "content": "I'm seeing horizontal lines on my TV."},
    {"role": "assistant", "content": "I'm sorry to hear that. Can you provide your TV model?"},
    {"role": "user", "content": "It's a Sony - 77\" Class BRAVIA XR A80K OLED 4K UHD Smart Google TV"},
    {"role": "assistant", "content": "Thank you for the information. Let's troubleshoot this issue..."}
]

memory_client.add(messages=conversation, user_id=USER_ID)
print("Conversation added to memory.")
```

## Retrieving and Using Memory

Create a function to get context-aware responses based on user's question and previous interactions:

```python
def get_context_aware_response(question):
    relevant_memories = memory_client.search(question, user_id=USER_ID)
    context = "\n".join([m["memory"] for m in relevant_memories])

    prompt = f"""Answer the user question considering the previous interactions:
    Previous interactions:
    {context}

    Question: {question}
    """

    reply = agent.generate_reply(messages=[{"content": prompt, "role": "user"}])
    return reply

# Example usage
question = "What was the issue with my TV?"
answer = get_context_aware_response(question)
print("Context-aware answer:", answer)
```

## Multi-Agent Conversation

For more complex scenarios, you can create multiple agents:

```python
manager = ConversableAgent(
    "manager",
    system_message="You are a manager who helps in resolving complex customer issues.",
    llm_config={"config_list": [{"model": "gpt-4", "api_key": OPENAI_API_KEY}]},
    human_input_mode="NEVER"
)

def escalate_to_manager(question):
    relevant_memories = memory_client.search(question, user_id=USER_ID)
    context = "\n".join([m["memory"] for m in relevant_memories])

    prompt = f"""
    Context from previous interactions:
    {context}

    Customer question: {question}

    As a manager, how would you address this issue?
    """

    manager_response = manager.generate_reply(messages=[{"content": prompt, "role": "user"}])
    return manager_response

# Example usage
complex_question = "I'm not satisfied with the troubleshooting steps. What else can be done?"
manager_answer = escalate_to_manager(complex_question)
print("Manager's response:", manager_answer)
```

## Conclusion

By integrating AutoGen with Mem0, you've created a conversational AI system with memory capabilities. This example demonstrates a customer service bot that can recall previous interactions and provide context-aware responses, with the ability to escalate complex issues to a manager agent.

This integration enables the creation of more intelligent and personalized AI agents for various applications, such as customer support, virtual assistants, and interactive chatbots.

## Help

In case of any questions, please feel free to reach out to us using one of the following methods:

<Snippet file="get-help.mdx" />
