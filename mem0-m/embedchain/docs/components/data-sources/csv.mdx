---
title: '📊 CSV'
---

You can load any csv file from your local file system or through a URL. Headers are included for each line, so if you have an `age` column, `18` will be added as `age: 18`.

## Usage

### Load from a local file

```python
from embedchain import App
app = App()
app.add('/path/to/file.csv', data_type='csv')
```

### Load from URL

```python
from embedchain import App
app = App()
app.add('https://people.sc.fsu.edu/~jburkardt/data/csv/airtravel.csv', data_type="csv")
```

<Note>
There is a size limit allowed for csv file beyond which it can throw error. This limit is set by the LLMs. Please consider chunking large csv files into smaller csv files.
</Note>

