"""Create initial migrations

Revision ID: 40a327b3debd
Revises:
Create Date: 2024-02-18 15:29:19.409064

"""

from typing import Sequence, Union

import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision: str = "40a327b3debd"
down_revision: Union[str, None] = None
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        "ec_chat_history",
        sa.Column("app_id", sa.String(), nullable=False),
        sa.Column("id", sa.String(), nullable=False),
        sa.Column("session_id", sa.String(), nullable=False),
        sa.Column("question", sa.Text(), nullable=True),
        sa.Column("answer", sa.Text(), nullable=True),
        sa.Column("metadata", sa.Text(), nullable=True),
        sa.Column("created_at", sa.TIMESTAMP(), nullable=True),
        sa.PrimaryKeyConstraint("app_id", "id", "session_id"),
    )
    op.create_index(op.f("ix_ec_chat_history_created_at"), "ec_chat_history", ["created_at"], unique=False)
    op.create_index(op.f("ix_ec_chat_history_session_id"), "ec_chat_history", ["session_id"], unique=False)
    op.create_table(
        "ec_data_sources",
        sa.Column("id", sa.String(), nullable=False),
        sa.Column("app_id", sa.Text(), nullable=True),
        sa.Column("hash", sa.Text(), nullable=True),
        sa.Column("type", sa.Text(), nullable=True),
        sa.Column("value", sa.Text(), nullable=True),
        sa.Column("metadata", sa.Text(), nullable=True),
        sa.Column("is_uploaded", sa.Integer(), nullable=True),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_index(op.f("ix_ec_data_sources_hash"), "ec_data_sources", ["hash"], unique=False)
    op.create_index(op.f("ix_ec_data_sources_app_id"), "ec_data_sources", ["app_id"], unique=False)
    op.create_index(op.f("ix_ec_data_sources_type"), "ec_data_sources", ["type"], unique=False)
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f("ix_ec_data_sources_type"), table_name="ec_data_sources")
    op.drop_index(op.f("ix_ec_data_sources_app_id"), table_name="ec_data_sources")
    op.drop_index(op.f("ix_ec_data_sources_hash"), table_name="ec_data_sources")
    op.drop_table("ec_data_sources")
    op.drop_index(op.f("ix_ec_chat_history_session_id"), table_name="ec_chat_history")
    op.drop_index(op.f("ix_ec_chat_history_created_at"), table_name="ec_chat_history")
    op.drop_table("ec_chat_history")
    # ### end Alembic commands ###
