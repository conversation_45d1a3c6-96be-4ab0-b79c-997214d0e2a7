#%% md
## Cookbook for using LLAMA2 with Embedchain
#%% md
### Step-1: Install embedchain package
#%%
!pip install embedchain[llama2]
#%% md
### Step-2: Set LLAMA2 related environment variables

You can find `OPENAI_API_KEY` on your [OpenAI dashboard](https://platform.openai.com/account/api-keys) and `REPLICATE_API_TOKEN` key on your [Replicate dashboard](https://replicate.com/account/api-tokens).
#%%
import os
from embedchain import App

os.environ["OPENAI_API_KEY"] = "sk-xxx"
os.environ["REPLICATE_API_TOKEN"] = "xxx"
#%% md
### Step-3 Create embedchain app and define your config
#%%
app = App.from_config(config={
    "provider": "llama2",
    "config": {
        "model": "a16z-infra/llama13b-v2-chat:df7690f1994d94e96ad9d568eac121aecf50684a0b0963b25a41cc40061269e5",
        "temperature": 0.5,
        "max_tokens": 1000,
        "top_p": 0.5,
        "stream": False
    }
})
#%% md
### Step-4: Add data sources to your app
#%%
app.add("https://www.forbes.com/profile/elon-musk")
#%% md
### Step-5: All set. Now start asking questions related to your data
#%%
while(True):
    question = input("Enter question: ")
    if question in ['q', 'exit', 'quit']:
        break
    answer = app.query(question)
    print(answer)