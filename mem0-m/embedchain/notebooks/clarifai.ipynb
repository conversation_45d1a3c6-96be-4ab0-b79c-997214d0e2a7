#%% md
# Cookbook for using Clarifai LLM and Embedders with Embedchain
#%% md
## Step-1: Install embedchain-clarifai package
#%%
!pip install embedchain[clarifai]
#%% md
## Step-2: Set Clarifai PAT as env variable.
Sign-up to [Clarifai](https://clarifai.com/signup?utm_source=clarifai_home&utm_medium=direct&) platform and you can obtain `CLARIFAI_PAT` by following this [link](https://docs.clarifai.com/clarifai-basics/authentication/personal-access-tokens/).

optionally you can also pass `api_key` in config of llm/embedder class.
#%%
import os
from embedchain import App

os.environ["CLARIFAI_PAT"]="xxx"
#%% md
## Step-3 Create embedchain app using clarifai LLM and embedder and define your config.

Browse through Clarifai community page to get the URL of different [LLM](https://clarifai.com/explore/models?page=1&perPage=24&filterData=%5B%7B%22field%22%3A%22use_cases%22%2C%22value%22%3A%5B%22llm%22%5D%7D%5D) and [embedding](https://clarifai.com/explore/models?page=1&perPage=24&filterData=%5B%7B%22field%22%3A%22input_fields%22%2C%22value%22%3A%5B%22text%22%5D%7D%2C%7B%22field%22%3A%22output_fields%22%2C%22value%22%3A%5B%22embeddings%22%5D%7D%5D) models available.
#%%
# Use model_kwargs to pass all model specific parameters for inference.
app = App.from_config(config={
    "llm": {
        "provider": "clarifai",
        "config": {
            "model": "https://clarifai.com/mistralai/completion/models/mistral-7B-Instruct",
            "model_kwargs": {
            "temperature": 0.5,
            "max_tokens": 1000
            }
        }
    },
    "embedder": {
        "provider": "clarifai",
        "config": {
            "model": "https://clarifai.com/openai/embed/models/text-embedding-ada",
        }
}
})
#%% md
## Step-4: Add data sources to your app
#%%
app.add("https://www.forbes.com/profile/elon-musk")
#%% md
## Step-5: All set. Now start asking questions related to your data
#%%
while(True):
    question = input("Enter question: ")
    if question in ['q', 'exit', 'quit']:
        break
    answer = app.query(question)
    print(answer)