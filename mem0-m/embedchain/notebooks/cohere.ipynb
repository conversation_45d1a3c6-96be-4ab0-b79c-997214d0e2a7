#%% md
## Cookbook for using Cohere with Embedchain
#%% md
### Step-1: Install embedchain package
#%%
!pip install embedchain[cohere]
#%% md
### Step-2: Set Cohere related environment variables

You can find `OPENAI_API_KEY` on your [OpenAI dashboard](https://platform.openai.com/account/api-keys) and `COHERE_API_KEY` key on your [Cohere dashboard](https://dashboard.cohere.com/api-keys).
#%%
import os
from embedchain import App

os.environ["OPENAI_API_KEY"] = "sk-xxx"
os.environ["COHERE_API_KEY"] = "xxx"
#%% md
### Step-3 Create embedchain app and define your config
#%%
app = App.from_config(config={
    "provider": "cohere",
    "config": {
        "model": "gptd-instruct-tft",
        "temperature": 0.5,
        "max_tokens": 1000,
        "top_p": 1,
        "stream": False
    }
})
#%% md
### Step-4: Add data sources to your app
#%%
app.add("https://www.forbes.com/profile/elon-musk")
#%% md
### Step-5: All set. Now start asking questions related to your data
#%%
while(True):
    question = input("Enter question: ")
    if question in ['q', 'exit', 'quit']:
        break
    answer = app.query(question)
    print(answer)