#%% md
## Cookbook for using PineconeDB with Embedchain
#%% md
### Step-1: Install embedchain package
#%%
!pip install embedchain pinecone-client pinecone-text
#%% md
### Step-2: Set environment variables needed for Pinecone

You can find this env variable on your [OpenAI dashboard](https://platform.openai.com/account/api-keys) and [Pinecone dashboard](https://app.pinecone.io/).
#%%
import os
from embedchain import App

os.environ["OPENAI_API_KEY"] = "sk-xxx"
os.environ["PINECONE_API_KEY"] = "xxx"
os.environ["PINECONE_ENV"] = "xxx"
#%% md
### Step-3 Create embedchain app and define your config
#%%
app = App.from_config(config={
    "provider": "pinecone",
    "config": {
        "metric": "cosine",
        "vector_dimension": 768,
        "collection_name": "pc-index"
    }
})
#%% md
### Step-4: Add data sources to your app
#%%
app.add("https://www.forbes.com/profile/elon-musk")
#%% md
### Step-5: All set. Now start asking questions related to your data
#%%
while(True):
    question = input("Enter question: ")
    if question in ['q', 'exit', 'quit']:
        break
    answer = app.query(question)
    print(answer)