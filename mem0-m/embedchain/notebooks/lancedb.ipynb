#%% md
## Cookbook for using LanceDB with Embedchain
#%% md
### Step-1: Install embedchain package
#%%
! pip install embedchain lancedb
#%% md
### Step-2: Set environment variables needed for LanceDB

You can find this env variable on your [OpenAI](https://platform.openai.com/account/api-keys).
#%%
import os
from embedchain import App

os.environ["OPENAI_API_KEY"] = "sk-xxx"
#%% md
### Step-3 Create embedchain app and define your config
#%%
app = App.from_config(config={
    "vectordb": {
        "provider": "lancedb",
            "config": {
                "collection_name": "lancedb-index"
            }
        }
    }
)
#%% md
### Step-4: Add data sources to your app
#%%
app.add("https://www.forbes.com/profile/elon-musk")
#%% md
### Step-5: All set. Now start asking questions related to your data
#%%
while(True):
    question = input("Enter question: ")
    if question in ['q', 'exit', 'quit']:
        break
    answer = app.query(question)
    print(answer)