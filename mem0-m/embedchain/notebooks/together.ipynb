#%% md
## Cookbook for using Cohere with Embedchain
#%% md
### Step-1: Install embedchain package
#%%
!pip install embedchain[together]
#%% md
### Step-2: Set Cohere related environment variables

You can find `OPENAI_API_KEY` on your [OpenAI dashboard](https://platform.openai.com/account/api-keys) and `TOGETHER_API_KEY` key on your [Together dashboard](https://api.together.xyz/settings/api-keys).
#%%
import os
from embedchain import App

os.environ["OPENAI_API_KEY"] = ""
os.environ["TOGETHER_API_KEY"] = ""
#%% md
### Step-3 Create embedchain app and define your config
#%%
app = App.from_config(config={
    "provider": "together",
    "config": {
        "model": "mistralai/Mixtral-8x7B-Instruct-v0.1",
        "temperature": 0.5,
        "max_tokens": 1000
    }
})
#%% md
### Step-4: Add data sources to your app
#%%
app.add("https://www.forbes.com/profile/elon-musk")
#%% md
### Step-5: All set. Now start asking questions related to your data
#%%
while(True):
    question = input("Enter question: ")
    if question in ['q', 'exit', 'quit']:
        break
    answer = app.query(question)
    print(answer)
#%%
