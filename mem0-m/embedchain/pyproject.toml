[tool.poetry]
name = "embedchain"
version = "0.1.128"
description = "Simplest open source retrieval (RAG) framework"
authors = [
    "<PERSON><PERSON><PERSON><PERSON> <<EMAIL>>",
    "<PERSON><PERSON><PERSON>dav <<EMAIL>>",
]
license = "Apache License"
readme = "README.md"
exclude = [
    "db",
    "configs",
    "notebooks"
]
packages = [
    { include = "embedchain" },
]

[build-system]
build-backend = "poetry.core.masonry.api"
requires = ["poetry-core"]

[tool.ruff]
line-length = 120
exclude = [
    ".bzr",
    ".direnv",
    ".eggs",
    ".git",
    ".git-rewrite",
    ".hg",
    ".mypy_cache",
    ".nox",
    ".pants.d",
    ".pytype",
    ".ruff_cache",
    ".svn",
    ".tox",
    ".venv",
    "__pypackages__",
    "_build",
    "buck-out",
    "build",
    "dist",
    "node_modules",
    "venv"
]
target-version = "py38"

[tool.ruff.lint]
select = ["ASYNC", "E", "F"]
ignore = []
fixable = ["ALL"]
unfixable = []
dummy-variable-rgx = "^(_+|(_+[a-zA-Z0-9_]*[a-zA-Z0-9]+?))$"

# Ignore `E402` (import violations) in all `__init__.py` files, and in `path/to/file.py`.
[tool.ruff.lint.per-file-ignores]
"embedchain/__init__.py" = ["E401"]

[tool.ruff.lint.mccabe]
max-complexity = 10

[tool.black]
line-length = 120
target-version = ["py38", "py39", "py310", "py311"]
include = '\.pyi?$'
exclude = '''
/(
    \.eggs
  | \.git
  | \.hg
  | \.mypy_cache
  | \.nox
  | \.pants.d
  | \.pytype
  | \.ruff_cache
  | \.svn
  | \.tox
  | \.venv
  | __pypackages__
  | _build
  | buck-out
  | build
  | dist
  | node_modules
  | venv
)/
'''

[tool.black.format]
color = true

[tool.poetry.dependencies]
python = ">=3.9,<=3.13.2"
python-dotenv = "^1.0.0"
langchain = "^0.3.1"
requests = "^2.31.0"
openai = ">=1.1.1"
chromadb = "^0.5.10"
posthog = "^3.0.2"
rich = "^13.7.0"
beautifulsoup4 = "^4.12.2"
pypdf = "^5.0.0"
gptcache = "^0.1.43"
pysbd = "^0.3.4"
mem0ai = "^0.1.54"
tiktoken = { version = "^0.7.0", optional = true }
sentence-transformers = { version = "^2.2.2", optional = true }
torch = { version = "2.3.0", optional = true }
# Torch 2.0.1 is not compatible with poetry (https://github.com/pytorch/pytorch/issues/100974)
gpt4all = { version = "2.0.2", optional = true }
# 1.0.9 is not working for some users (https://github.com/nomic-ai/gpt4all/issues/1394)
opensearch-py = { version = "2.3.1", optional = true }
elasticsearch = { version = "^8.9.0", optional = true }
cohere = { version = "^5.3", optional = true }
together = { version = "^1.2.1", optional = true }
lancedb = { version = "^0.6.2", optional = true }
weaviate-client = { version = "^3.24.1", optional = true }
qdrant-client = { version = "^1.6.3", optional = true }
pymilvus = { version = "2.4.3", optional = true }
google-cloud-aiplatform = { version = "^1.26.1", optional = true }
replicate = { version = "^0.15.4", optional = true }
schema = "^0.7.5"
psycopg = { version = "^3.1.12", optional = true }
psycopg-binary = { version = "^3.1.12", optional = true }
psycopg-pool = { version = "^3.1.8", optional = true }
mysql-connector-python = { version = "^8.1.0", optional = true }
google-generativeai = { version = "^0.3.0", optional = true }
google-api-python-client = { version = "^2.111.0", optional = true }
google-auth-oauthlib = { version = "^1.2.0", optional = true }
google-auth = { version = "^2.25.2", optional = true }
google-auth-httplib2 = { version = "^0.2.0", optional = true }
google-api-core = { version = "^2.15.0", optional = true }
langchain-mistralai = { version = "^0.2.0", optional = true }
langchain-openai = "^0.2.1"
langchain-google-vertexai = { version = "^2.0.2", optional = true }
sqlalchemy = "^2.0.27"
alembic = "^1.13.1"
langchain-cohere = "^0.3.0"
langchain-community = "^0.3.1"
langchain-aws = {version = "^0.2.1", optional = true}
langsmith = "^0.3.18"

[tool.poetry.group.dev.dependencies]
black = "^23.3.0"
pre-commit = "^3.2.2"
ruff = "^0.1.11"
pytest = "^7.3.1"
pytest-mock = "^3.10.0"
pytest-env = "^0.8.1"
click = "^8.1.3"
isort = "^5.12.0"
pytest-cov = "^4.1.0"
responses = "^0.23.3"
mock = "^5.1.0"
pytest-asyncio = "^0.21.1"

[tool.poetry.extras]
opensource = ["sentence-transformers", "torch", "gpt4all"]
lancedb = ["lancedb"]
elasticsearch = ["elasticsearch"]
opensearch = ["opensearch-py"]
weaviate = ["weaviate-client"]
qdrant = ["qdrant-client"]
together = ["together"]
milvus = ["pymilvus"]
vertexai = ["langchain-google-vertexai"]
llama2 = ["replicate"]
gmail = [
    "requests",
    "google-api-python-client",
    "google-auth",
    "google-auth-oauthlib",
    "google-auth-httplib2",
    "google-api-core",
]
googledrive = ["google-api-python-client", "google-auth-oauthlib", "google-auth-httplib2"]
postgres = ["psycopg", "psycopg-binary", "psycopg-pool"]
mysql = ["mysql-connector-python"]
google = ["google-generativeai"]
mistralai = ["langchain-mistralai"]
aws = ["langchain-aws"]

[tool.poetry.group.docs.dependencies]

[tool.poetry.scripts]
ec = "embedchain.cli:cli"