# fly.toml app configuration file generated for nextjs-discord on 2024-01-04T06:56:01+05:30
#
# See https://fly.io/docs/reference/configuration/ for information about how to use this file.
#

app = "nextjs-discord"
primary_region = "sjc"

[build]

[http_service]
  internal_port = 8080
  force_https = true
  auto_stop_machines = true
  auto_start_machines = true
  min_machines_running = 0
  processes = ["app"]

[[vm]]
  cpu_kind = "shared"
  cpus = 1
  memory_mb = 1024
