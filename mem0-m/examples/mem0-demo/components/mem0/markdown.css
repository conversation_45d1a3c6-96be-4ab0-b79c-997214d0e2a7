.token {
    word-break: break-word; /* Break long words */
    overflow-wrap: break-word; /* Wrap text if it's too long */
    width: 100%;
    white-space: pre-wrap;
  }

  .prose li p {
    margin-top: -19px;
  }

  @keyframes highlightSweep {
    0% {
      transform: scaleX(0);
      opacity: 0;
    }
    100% {
      transform: scaleX(1);
      opacity: 1;
    }
  }

  .highlight-text {
    display: inline-block;
    position: relative;
    font-weight: normal;
    padding: 0;
    border-radius: 4px;
  }

  .highlight-text::before {
    content: "";
    position: absolute;
    left: 0;
    right: 0;
    top: 0;
    bottom: 0;
    background: rgb(233 213 255 / 0.7);
    transform-origin: left;
    transform: scaleX(0);
    opacity: 0;
    z-index: -1;
    border-radius: inherit;
  }

  @keyframes fontWeightAnimation {
    0% {
      font-weight: normal;
      padding: 0;
    }
    100% {
      font-weight: 600;
      padding: 0 4px;
    }
  }

  @keyframes backgroundColorAnimation {
    0% {
      background-color: transparent;
    }
    100% {
      background-color: rgba(180, 231, 255, 0.7);
    }
  }

  .highlight-text.animate {
    animation: 
      fontWeightAnimation 0.1s ease-out forwards,
      backgroundColorAnimation 0.1s ease-out forwards;
    animation-delay: 0.88s, 1.1s;
  }

  .highlight-text.dark {
    background-color: rgba(213, 242, 255, 0.7);
    color: #000;
  }

  .highlight-text.animate::before {
    animation: highlightSweep 0.5s ease-out forwards;
    animation-delay: 0.6s;
    animation-fill-mode: forwards;
    animation-iteration-count: 1;
  }

  :root[class~="dark"] .highlight-text::before {
    background: rgb(88 28 135 / 0.5);
  }

  @keyframes blink {
    0%, 100% { opacity: 0; }
    50% { opacity: 1; }
  }

  .markdown-cursor {
    display: inline-block;
    animation: blink 0.8s ease-in-out infinite;
    color: rgba(213, 242, 255, 0.7);
    margin-left: 1px;
    font-size: 1.2em;
    line-height: 1;
    vertical-align: baseline;
    position: relative;
    top: 2px;
  }

  :root[class~="dark"] .markdown-cursor {
    color: #6366f1;
  }