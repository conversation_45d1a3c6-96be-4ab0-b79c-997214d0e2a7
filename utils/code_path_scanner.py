#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
代码路径扫描工具
用于查找项目中的硬编码路径，为路径改造提供参考
"""

import os
import re
import sys
from pathlib import Path
from typing import List, Dict, Tuple
import logging

class CodePathScanner:
    """代码路径扫描器"""
    
    def __init__(self, project_root: str = None):
        self.logger = logging.getLogger(__name__)
        
        if project_root is None:
            # 自动检测项目根目录
            current_file = Path(__file__).resolve()
            self.project_root = current_file.parent.parent
        else:
            self.project_root = Path(project_root)
            
        # 需要扫描的文件类型
        self.scan_extensions = {'.py', '.json', '.yaml', '.yml', '.txt', '.md'}
        
        # 排除的目录
        self.exclude_dirs = {
            'venv', '__pycache__', '.git', '.idea', 'node_modules',
            'mem0-m', 'mem0-main', 'mem0-main-backup-before-optimization'
        }
        
        # 路径模式匹配规则
        self.path_patterns = [
            # 资源文件路径
            r'["\']([^"\']*(?:resources|images|icons|styles|ui)[^"\']*)["\']',
            # 绝对路径（Windows/Linux）
            r'["\']([C-Z]:[\\\/][^"\']*)["\']',
            r'["\']([\/][^"\']*)["\']',
            # 相对路径模式
            r'["\']([\.]{1,2}[\\\/][^"\']*)["\']',
            # 日志路径
            r'["\']([^"\']*logs[^"\']*)["\']',
            # 备份路径
            r'["\']([^"\']*backups?[^"\']*)["\']',
            # 数据库路径
            r'["\']([^"\']*\.sql[^"\']*)["\']',
            # 导出路径
            r'["\']([^"\']*exports?[^"\']*)["\']',
            # 临时文件路径
            r'["\']([^"\']*temp[^"\']*)["\']',
        ]
        
        # 可能需要更新的函数调用
        self.path_functions = [
            'os.path.join',
            'os.path.dirname',
            'os.path.abspath',
            'Path(',
            'open(',
            'QPixmap(',
            'QIcon(',
        ]
    
    def scan_file(self, file_path: Path) -> List[Dict]:
        """扫描单个文件中的路径
        
        Args:
            file_path: 文件路径
            
        Returns:
            List[Dict]: 发现的路径信息列表
        """
        findings = []
        
        try:
            with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                lines = f.readlines()
            
            for line_num, line in enumerate(lines, 1):
                line = line.strip()
                
                # 跳过注释行
                if line.startswith('#') or line.startswith('//'):
                    continue
                
                # 扫描路径模式
                for pattern in self.path_patterns:
                    matches = re.findall(pattern, line)
                    for match in matches:
                        if self._is_potential_path(match):
                            findings.append({
                                'file': str(file_path.relative_to(self.project_root)),
                                'line': line_num,
                                'content': line.strip(),
                                'path': match,
                                'type': 'path_pattern',
                                'severity': self._assess_severity(match)
                            })
                
                # 扫描路径函数调用
                for func in self.path_functions:
                    if func in line:
                        findings.append({
                            'file': str(file_path.relative_to(self.project_root)),
                            'line': line_num,
                            'content': line.strip(),
                            'path': '',
                            'type': f'function_call:{func}',
                            'severity': 'medium'
                        })
        
        except Exception as e:
            self.logger.warning(f"扫描文件 {file_path} 失败: {e}")
        
        return findings
    
    def _is_potential_path(self, text: str) -> bool:
        """判断文本是否可能是文件路径"""
        if not text or len(text) < 3:
            return False
        
        # 排除一些明显不是路径的字符串
        if text in {'', '/', '\\', '.', '..'}:
            return False
        
        # 排除URL
        if text.startswith(('http://', 'https://', 'ftp://')):
            return False
        
        # 排除数据库连接字符串
        if 'mysql://' in text or 'postgresql://' in text:
            return False
        
        # 包含路径分隔符或常见路径关键词
        path_indicators = ['/', '\\', 'resources', 'images', 'logs', 'backup', 'export', 'temp']
        return any(indicator in text for indicator in path_indicators)
    
    def _assess_severity(self, path: str) -> str:
        """评估路径问题的严重程度"""
        # 绝对路径 - 高风险
        if (path.startswith('/') and len(path) > 1) or (len(path) > 3 and path[1] == ':'):
            return 'high'
        
        # 包含资源文件 - 中风险
        if any(keyword in path.lower() for keyword in ['resources', 'images', 'icons', 'styles']):
            return 'medium'
        
        # 相对路径 - 低风险
        if path.startswith('./') or path.startswith('../'):
            return 'low'
        
        return 'low'
    
    def scan_project(self) -> Dict[str, List[Dict]]:
        """扫描整个项目
        
        Returns:
            Dict: {文件路径: [发现的问题列表]}
        """
        results = {}
        scanned_files = 0
        
        self.logger.info(f"开始扫描项目: {self.project_root}")
        
        for root, dirs, files in os.walk(self.project_root):
            # 排除不需要扫描的目录
            dirs[:] = [d for d in dirs if d not in self.exclude_dirs]
            
            for file in files:
                file_path = Path(root) / file
                
                # 只扫描指定类型的文件
                if file_path.suffix.lower() in self.scan_extensions:
                    findings = self.scan_file(file_path)
                    if findings:
                        results[str(file_path.relative_to(self.project_root))] = findings
                    scanned_files += 1
        
        self.logger.info(f"扫描完成，共扫描 {scanned_files} 个文件")
        return results
    
    def generate_report(self, results: Dict[str, List[Dict]]) -> str:
        """生成扫描报告
        
        Args:
            results: 扫描结果
            
        Returns:
            str: 格式化的报告文本
        """
        report_lines = []
        report_lines.append("=" * 80)
        report_lines.append("代码路径扫描报告")
        report_lines.append("=" * 80)
        report_lines.append("")
        
        # 统计信息
        total_files = len(results)
        total_issues = sum(len(issues) for issues in results.values())
        
        # 按严重程度统计
        severity_counts = {'high': 0, 'medium': 0, 'low': 0}
        for issues in results.values():
            for issue in issues:
                severity = issue.get('severity', 'low')
                severity_counts[severity] += 1
        
        report_lines.append(f"📊 统计信息:")
        report_lines.append(f"  - 包含问题的文件: {total_files}")
        report_lines.append(f"  - 发现的问题总数: {total_issues}")
        report_lines.append(f"  - 高风险问题: {severity_counts['high']}")
        report_lines.append(f"  - 中风险问题: {severity_counts['medium']}")
        report_lines.append(f"  - 低风险问题: {severity_counts['low']}")
        report_lines.append("")
        
        # 按严重程度分组显示
        for severity in ['high', 'medium', 'low']:
            severity_issues = []
            for file_path, issues in results.items():
                for issue in issues:
                    if issue.get('severity') == severity:
                        severity_issues.append((file_path, issue))
            
            if severity_issues:
                severity_names = {'high': '🔴 高风险问题', 'medium': '🟡 中风险问题', 'low': '🟢 低风险问题'}
                report_lines.append(severity_names[severity])
                report_lines.append("-" * 40)
                
                for file_path, issue in severity_issues:
                    report_lines.append(f"📁 文件: {file_path}")
                    report_lines.append(f"📍 行号: {issue['line']}")
                    report_lines.append(f"🔍 类型: {issue['type']}")
                    if issue['path']:
                        report_lines.append(f"📂 路径: {issue['path']}")
                    report_lines.append(f"💬 代码: {issue['content'][:100]}...")
                    report_lines.append("")
        
        return "\n".join(report_lines)
    
    def save_report(self, results: Dict[str, List[Dict]], output_file: str = None):
        """保存扫描报告到文件
        
        Args:
            results: 扫描结果
            output_file: 输出文件路径
        """
        if output_file is None:
            from datetime import datetime
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            output_file = self.project_root / f"path_scan_report_{timestamp}.txt"
        
        report = self.generate_report(results)
        
        try:
            with open(output_file, 'w', encoding='utf-8') as f:
                f.write(report)
            
            self.logger.info(f"报告已保存到: {output_file}")
            return output_file
        
        except Exception as e:
            self.logger.error(f"保存报告失败: {e}")
            return None

def main():
    """主函数 - 命令行工具"""
    import argparse
    
    parser = argparse.ArgumentParser(description='WMS项目代码路径扫描工具')
    parser.add_argument('--project-root', help='项目根目录路径')
    parser.add_argument('--output', help='输出报告文件路径')
    parser.add_argument('--verbose', action='store_true', help='详细输出')
    
    args = parser.parse_args()
    
    # 配置日志
    log_level = logging.DEBUG if args.verbose else logging.INFO
    logging.basicConfig(
        level=log_level,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    # 创建扫描器
    scanner = CodePathScanner(args.project_root)
    
    # 执行扫描
    print("🔍 开始扫描项目中的硬编码路径...")
    results = scanner.scan_project()
    
    # 生成报告
    report = scanner.generate_report(results)
    print(report)
    
    # 保存报告
    if args.output or results:
        output_file = scanner.save_report(results, args.output)
        if output_file:
            print(f"\n📄 完整报告已保存到: {output_file}")

if __name__ == '__main__':
    main() 