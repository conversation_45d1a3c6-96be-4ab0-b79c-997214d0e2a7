#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
路径管理器 - 解决跨设备开发路径不一致问题
支持E盘、D盘等不同根目录的自动适配
"""

import os
import sys
import json
import platform
from pathlib import Path
from typing import Dict, Optional, Union, List
import logging

class PathManager:
    """智能路径管理器
    
    自动检测项目根目录，支持跨设备路径转换
    """
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self._project_root = None
        self._path_cache = {}
        self._config_file = None
        self._system_type = platform.system()
        
        # 初始化路径管理器
        self._initialize()
    
    def _initialize(self):
        """初始化路径管理器"""
        try:
            # 自动检测项目根目录
            self._detect_project_root()
            
            # 加载路径配置
            self._load_path_config()
            
            # 创建必要的目录结构
            self._ensure_directories()
            
            self.logger.info(f"路径管理器初始化成功，项目根目录: {self._project_root}")
            
        except Exception as e:
            self.logger.error(f"路径管理器初始化失败: {e}")
            raise
    
    def _detect_project_root(self):
        """自动检测项目根目录"""
        # 从当前文件开始向上查找项目根目录
        current_path = Path(__file__).resolve()
        
        # 查找包含特定标识文件的目录作为项目根目录
        project_markers = [
            'app.py',  # WMS主程序
            'config',  # 配置目录
            '.git',    # Git仓库
            'requirements.txt'  # Python依赖文件
        ]
        
        # 向上查找项目根目录
        for parent in current_path.parents:
            if any((parent / marker).exists() for marker in project_markers):
                self._project_root = parent
                break
        
        if not self._project_root:
            # 如果找不到项目根目录，使用当前文件的上一级目录
            self._project_root = current_path.parent.parent
        
        # 确保路径是绝对路径
        self._project_root = Path(self._project_root).resolve()
    
    def _load_path_config(self):
        """加载路径配置文件"""
        self._config_file = self._project_root / 'config' / 'path_config.json'
        
        # 默认路径配置
        default_config = {
            "paths": {
                "resources": "resources",
                "images": "resources/images", 
                "icons": "resources/icons",
                "styles": "resources/styles",
                "ui": "resources/ui",
                "logs": "logs",
                "backups": "backups",
                "exports": "exports",
                "temp": "temp",
                "uploads": "uploads"
            },
            "database": {
                "backup_dir": "backups/database",
                "export_dir": "exports/database"
            },
            "system": {
                "project_root": str(self._project_root),
                "system_type": self._system_type,
                "last_updated": None
            }
        }
        
        try:
            if self._config_file.exists():
                with open(self._config_file, 'r', encoding='utf-8') as f:
                    self._path_config = json.load(f)
                    
                # 更新项目根目录（适应不同设备）
                self._path_config['system']['project_root'] = str(self._project_root)
            else:
                self._path_config = default_config
                
            # 保存配置文件
            self._save_path_config()
            
        except Exception as e:
            self.logger.warning(f"加载路径配置失败，使用默认配置: {e}")
            self._path_config = default_config
    
    def _save_path_config(self):
        """保存路径配置文件"""
        try:
            # 确保配置目录存在
            self._config_file.parent.mkdir(parents=True, exist_ok=True)
            
            # 更新时间戳
            from datetime import datetime
            self._path_config['system']['last_updated'] = datetime.now().isoformat()
            
            with open(self._config_file, 'w', encoding='utf-8') as f:
                json.dump(self._path_config, f, indent=2, ensure_ascii=False)
                
        except Exception as e:
            self.logger.error(f"保存路径配置失败: {e}")
    
    def _ensure_directories(self):
        """确保必要的目录结构存在"""
        try:
            # 创建基本目录结构
            for path_key, relative_path in self._path_config['paths'].items():
                full_path = self.get_path(path_key)
                full_path.mkdir(parents=True, exist_ok=True)
                
            # 创建数据库相关目录
            for db_key, relative_path in self._path_config['database'].items():
                full_path = self.get_database_path(db_key.replace('_dir', ''))
                full_path.mkdir(parents=True, exist_ok=True)
                
        except Exception as e:
            self.logger.error(f"创建目录结构失败: {e}")
    
    @property
    def project_root(self) -> Path:
        """获取项目根目录"""
        return self._project_root
    
    def get_path(self, path_key: str) -> Path:
        """获取路径
        
        Args:
            path_key: 路径键名（如'resources', 'images'等）
            
        Returns:
            Path: 完整路径对象
        """
        if path_key in self._path_cache:
            return self._path_cache[path_key]
        
        if path_key in self._path_config['paths']:
            relative_path = self._path_config['paths'][path_key]
            full_path = self._project_root / relative_path
            self._path_cache[path_key] = full_path
            return full_path
        else:
            raise ValueError(f"未知的路径键: {path_key}")
    
    def get_file_path(self, path_key: str, filename: str) -> Path:
        """获取文件的完整路径
        
        Args:
            path_key: 路径键名
            filename: 文件名
            
        Returns:
            Path: 文件完整路径
        """
        base_path = self.get_path(path_key)
        return base_path / filename
    
    def get_database_path(self, db_type: str) -> Path:
        """获取数据库相关路径
        
        Args:
            db_type: 数据库类型（'backup', 'export'）
            
        Returns:
            Path: 数据库路径
        """
        db_key = f"{db_type}_dir"
        if db_key in self._path_config['database']:
            relative_path = self._path_config['database'][db_key]
            return self._project_root / relative_path
        else:
            raise ValueError(f"未知的数据库路径类型: {db_type}")
    
    def get_relative_path(self, full_path: Union[str, Path]) -> str:
        """将绝对路径转换为相对路径
        
        Args:
            full_path: 完整路径
            
        Returns:
            str: 相对于项目根目录的路径
        """
        full_path = Path(full_path)
        try:
            return str(full_path.relative_to(self._project_root))
        except ValueError:
            # 如果路径不在项目根目录下，返回绝对路径
            return str(full_path)
    
    def convert_path_to_current_system(self, stored_path: str) -> Path:
        """将存储的路径转换为当前系统的路径
        
        Args:
            stored_path: 存储的路径字符串
            
        Returns:
            Path: 转换后的路径
        """
        # 如果是相对路径，直接拼接到项目根目录
        if not os.path.isabs(stored_path):
            return self._project_root / stored_path
        
        # 如果是绝对路径，尝试转换为相对路径
        stored_path = Path(stored_path)
        
        # 尝试提取相对于项目的路径部分
        path_parts = stored_path.parts
        
        # 查找可能的项目标识符
        project_indicators = ['WMS', 'wms']
        
        for i, part in enumerate(path_parts):
            if part in project_indicators:
                # 从这个部分开始构建相对路径
                relative_parts = path_parts[i+1:]
                if relative_parts:
                    return self._project_root / Path(*relative_parts)
                else:
                    return self._project_root
        
        # 如果无法转换，返回原路径
        return stored_path
    
    def update_config_paths(self, database_paths: List[str]) -> Dict[str, str]:
        """更新配置文件中的路径
        
        Args:
            database_paths: 数据库中存储的路径列表
            
        Returns:
            Dict: 路径映射表 {原路径: 新路径}
        """
        path_mapping = {}
        
        for old_path in database_paths:
            new_path = self.convert_path_to_current_system(old_path)
            path_mapping[old_path] = str(new_path)
        
        return path_mapping
    
    def get_backup_filename(self, base_name: str, extension: str = '.bak') -> Path:
        """生成备份文件名
        
        Args:
            base_name: 基础文件名
            extension: 扩展名
            
        Returns:
            Path: 备份文件路径
        """
        from datetime import datetime
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        backup_name = f"{base_name}_{timestamp}{extension}"
        return self.get_file_path('backups', backup_name)
    
    def create_export_path(self, export_type: str, filename: str) -> Path:
        """创建导出文件路径
        
        Args:
            export_type: 导出类型
            filename: 文件名
            
        Returns:
            Path: 导出文件路径
        """
        export_dir = self.get_path('exports') / export_type
        export_dir.mkdir(parents=True, exist_ok=True)
        return export_dir / filename
    
    def cleanup_temp_files(self, max_age_hours: int = 24):
        """清理临时文件
        
        Args:
            max_age_hours: 最大保留时间（小时）
        """
        import time
        
        try:
            temp_dir = self.get_path('temp')
            if not temp_dir.exists():
                return
            
            current_time = time.time()
            max_age_seconds = max_age_hours * 3600
            
            for file_path in temp_dir.iterdir():
                if file_path.is_file():
                    file_age = current_time - file_path.stat().st_mtime
                    if file_age > max_age_seconds:
                        file_path.unlink()
                        self.logger.info(f"清理临时文件: {file_path}")
                        
        except Exception as e:
            self.logger.error(f"清理临时文件失败: {e}")
    
    def get_system_info(self) -> Dict:
        """获取系统路径信息"""
        return {
            'project_root': str(self._project_root),
            'system_type': self._system_type,
            'python_version': sys.version,
            'available_paths': list(self._path_config['paths'].keys()),
            'config_file': str(self._config_file)
        }


# 全局路径管理器实例
_path_manager = None

def get_path_manager() -> PathManager:
    """获取全局路径管理器实例"""
    global _path_manager
    if _path_manager is None:
        _path_manager = PathManager()
    return _path_manager

def get_project_root() -> Path:
    """快捷方式：获取项目根目录"""
    return get_path_manager().project_root

def get_resource_path(resource_type: str, filename: str = None) -> Path:
    """快捷方式：获取资源文件路径"""
    manager = get_path_manager()
    if filename:
        return manager.get_file_path(resource_type, filename)
    else:
        return manager.get_path(resource_type)

def get_image_path(filename: str) -> Path:
    """快捷方式：获取图片路径"""
    return get_resource_path('images', filename)

def get_config_path(filename: str = None) -> Path:
    """快捷方式：获取配置文件路径"""
    manager = get_path_manager()
    config_dir = manager.project_root / 'config'
    if filename:
        return config_dir / filename
    else:
        return config_dir

if __name__ == '__main__':
    # 测试路径管理器
    pm = PathManager()
    print("项目根目录:", pm.project_root)
    print("图片目录:", pm.get_path('images'))
    print("系统信息:", pm.get_system_info()) 