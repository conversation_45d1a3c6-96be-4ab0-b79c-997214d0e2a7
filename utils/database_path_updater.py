#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据库路径更新工具
用于更新数据库中存储的文件路径，适配不同设备的路径结构
"""

import sys
import os
from pathlib import Path
from typing import Dict, List, Tuple, Optional
import logging

# 添加项目根目录到路径
sys.path.insert(0, str(Path(__file__).parent.parent))

from config.database import get_db_connection
from utils.path_manager import get_path_manager

class DatabasePathUpdater:
    """数据库路径更新工具"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.path_manager = get_path_manager()
        self.connection = None
        
        # 需要更新路径的表和字段配置
        self.path_fields_config = {
            'products': [
                'image_path',     # 商品图片路径
                'attachment_path' # 商品附件路径
            ],
            'inventory_transactions': [
                'document_path',  # 单据路径
                'attachment_path' # 附件路径
            ],
            'system_logs': [
                'log_file_path'   # 日志文件路径
            ],
            'exports': [
                'file_path'       # 导出文件路径
            ]
        }
    
    def connect_database(self):
        """连接数据库"""
        try:
            self.connection = get_db_connection()
            self.logger.info("数据库连接成功")
            return True
        except Exception as e:
            self.logger.error(f"数据库连接失败: {e}")
            return False
    
    def close_database(self):
        """关闭数据库连接"""
        if self.connection:
            self.connection.close()
            self.logger.info("数据库连接已关闭")
    
    def scan_path_fields(self) -> Dict[str, List[Tuple]]:
        """扫描数据库中的路径字段
        
        Returns:
            Dict: {表名: [(ID, 字段名, 当前路径), ...]}
        """
        if not self.connection:
            return {}
        
        found_paths = {}
        cursor = self.connection.cursor()
        
        try:
            for table_name, field_names in self.path_fields_config.items():
                found_paths[table_name] = []
                
                # 检查表是否存在
                cursor.execute("""
                    SELECT COUNT(*) FROM information_schema.tables 
                    WHERE table_name = %s AND table_schema = DATABASE()
                """, (table_name,))
                
                if cursor.fetchone()[0] == 0:
                    self.logger.warning(f"表 {table_name} 不存在，跳过")
                    continue
                
                # 检查每个字段
                for field_name in field_names:
                    # 检查字段是否存在
                    cursor.execute("""
                        SELECT COUNT(*) FROM information_schema.columns 
                        WHERE table_name = %s AND column_name = %s AND table_schema = DATABASE()
                    """, (table_name, field_name))
                    
                    if cursor.fetchone()[0] == 0:
                        self.logger.warning(f"字段 {table_name}.{field_name} 不存在，跳过")
                        continue
                    
                    # 查询包含路径的记录
                    query = f"""
                        SELECT id, {field_name} 
                        FROM {table_name} 
                        WHERE {field_name} IS NOT NULL AND {field_name} != ''
                    """
                    
                    cursor.execute(query)
                    results = cursor.fetchall()
                    
                    for record_id, current_path in results:
                        if current_path and str(current_path).strip():
                            found_paths[table_name].append((record_id, field_name, current_path))
                
                self.logger.info(f"表 {table_name} 找到 {len(found_paths[table_name])} 个路径字段")
        
        except Exception as e:
            self.logger.error(f"扫描路径字段失败: {e}")
        
        finally:
            cursor.close()
        
        return found_paths
    
    def analyze_paths(self, found_paths: Dict[str, List[Tuple]]) -> Dict[str, str]:
        """分析路径并生成转换映射
        
        Args:
            found_paths: 扫描到的路径数据
            
        Returns:
            Dict: 路径映射表 {原路径: 新路径}
        """
        path_mapping = {}
        
        for table_name, path_records in found_paths.items():
            for record_id, field_name, current_path in path_records:
                if current_path not in path_mapping:
                    # 使用路径管理器转换路径
                    new_path = self.path_manager.convert_path_to_current_system(current_path)
                    path_mapping[current_path] = str(new_path)
        
        return path_mapping
    
    def preview_changes(self, found_paths: Dict[str, List[Tuple]], path_mapping: Dict[str, str]) -> Dict:
        """预览将要进行的更改
        
        Args:
            found_paths: 扫描到的路径数据
            path_mapping: 路径映射表
            
        Returns:
            Dict: 更改预览信息
        """
        preview = {
            'total_records': 0,
            'total_changes': 0,
            'changes_by_table': {},
            'path_conversions': path_mapping
        }
        
        for table_name, path_records in found_paths.items():
            table_changes = []
            
            for record_id, field_name, current_path in path_records:
                preview['total_records'] += 1
                
                if current_path in path_mapping:
                    new_path = path_mapping[current_path]
                    if current_path != new_path:
                        table_changes.append({
                            'record_id': record_id,
                            'field_name': field_name,
                            'old_path': current_path,
                            'new_path': new_path
                        })
                        preview['total_changes'] += 1
            
            preview['changes_by_table'][table_name] = table_changes
        
        return preview
    
    def update_database_paths(self, found_paths: Dict[str, List[Tuple]], path_mapping: Dict[str, str], dry_run: bool = True) -> bool:
        """更新数据库中的路径
        
        Args:
            found_paths: 扫描到的路径数据
            path_mapping: 路径映射表
            dry_run: 是否为预演模式（不实际执行）
            
        Returns:
            bool: 更新是否成功
        """
        if not self.connection:
            return False
        
        cursor = self.connection.cursor()
        updated_count = 0
        
        try:
            if not dry_run:
                # 开始事务
                self.connection.autocommit = False
            
            for table_name, path_records in found_paths.items():
                for record_id, field_name, current_path in path_records:
                    if current_path in path_mapping:
                        new_path = path_mapping[current_path]
                        
                        if current_path != new_path:
                            update_query = f"""
                                UPDATE {table_name} 
                                SET {field_name} = %s 
                                WHERE id = %s
                            """
                            
                            if dry_run:
                                self.logger.info(f"[预演] 更新 {table_name}.{field_name} (ID: {record_id}): {current_path} -> {new_path}")
                            else:
                                cursor.execute(update_query, (new_path, record_id))
                                self.logger.info(f"[执行] 更新 {table_name}.{field_name} (ID: {record_id}): {current_path} -> {new_path}")
                            
                            updated_count += 1
            
            if not dry_run:
                # 提交事务
                self.connection.commit()
                self.connection.autocommit = True
                self.logger.info(f"成功更新 {updated_count} 条记录")
            else:
                self.logger.info(f"预演完成，将更新 {updated_count} 条记录")
            
            return True
        
        except Exception as e:
            if not dry_run:
                # 回滚事务
                self.connection.rollback()
                self.connection.autocommit = True
            
            self.logger.error(f"更新数据库路径失败: {e}")
            return False
        
        finally:
            cursor.close()
    
    def create_backup(self) -> Optional[Path]:
        """创建数据库备份
        
        Returns:
            Path: 备份文件路径，失败返回None
        """
        try:
            backup_filename = self.path_manager.get_backup_filename('wms_before_path_update', '.sql')
            
            # 使用mysqldump创建备份
            import subprocess
            from config.database import DATABASE_CONFIG
            
            cmd = [
                'mysqldump',
                '-h', DATABASE_CONFIG['host'],
                '-P', str(DATABASE_CONFIG['port']),
                '-u', DATABASE_CONFIG['username'],
                f'-p{DATABASE_CONFIG["password"]}',
                DATABASE_CONFIG['database']
            ]
            
            with open(backup_filename, 'w', encoding='utf-8') as backup_file:
                result = subprocess.run(cmd, stdout=backup_file, stderr=subprocess.PIPE, text=True)
            
            if result.returncode == 0:
                self.logger.info(f"数据库备份创建成功: {backup_filename}")
                return backup_filename
            else:
                self.logger.error(f"数据库备份失败: {result.stderr}")
                return None
        
        except Exception as e:
            self.logger.error(f"创建数据库备份失败: {e}")
            return None
    
    def run_full_update(self, create_backup: bool = True, dry_run: bool = True) -> Dict:
        """执行完整的路径更新流程
        
        Args:
            create_backup: 是否创建备份
            dry_run: 是否为预演模式
            
        Returns:
            Dict: 执行结果
        """
        result = {
            'success': False,
            'backup_file': None,
            'preview': None,
            'error': None
        }
        
        try:
            # 连接数据库
            if not self.connect_database():
                result['error'] = "数据库连接失败"
                return result
            
            # 创建备份
            if create_backup and not dry_run:
                backup_file = self.create_backup()
                result['backup_file'] = str(backup_file) if backup_file else None
                
                if not backup_file:
                    result['error'] = "数据库备份失败"
                    return result
            
            # 扫描路径字段
            self.logger.info("开始扫描数据库中的路径字段...")
            found_paths = self.scan_path_fields()
            
            if not found_paths:
                result['error'] = "未找到需要更新的路径字段"
                return result
            
            # 分析路径转换
            self.logger.info("分析路径转换...")
            path_mapping = self.analyze_paths(found_paths)
            
            # 生成预览
            preview = self.preview_changes(found_paths, path_mapping)
            result['preview'] = preview
            
            if preview['total_changes'] == 0:
                self.logger.info("所有路径都是最新的，无需更新")
                result['success'] = True
                return result
            
            # 更新数据库
            self.logger.info(f"开始更新数据库路径（{'预演模式' if dry_run else '执行模式'}）...")
            update_success = self.update_database_paths(found_paths, path_mapping, dry_run)
            
            result['success'] = update_success
            
        except Exception as e:
            self.logger.error(f"路径更新流程失败: {e}")
            result['error'] = str(e)
        
        finally:
            self.close_database()
        
        return result

def main():
    """主函数 - 命令行工具"""
    import argparse
    
    parser = argparse.ArgumentParser(description='WMS数据库路径更新工具')
    parser.add_argument('--execute', action='store_true', help='执行实际更新（默认为预演模式）')
    parser.add_argument('--no-backup', action='store_true', help='跳过数据库备份')
    parser.add_argument('--verbose', action='store_true', help='详细输出')
    
    args = parser.parse_args()
    
    # 配置日志
    log_level = logging.DEBUG if args.verbose else logging.INFO
    logging.basicConfig(
        level=log_level,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    # 创建更新器
    updater = DatabasePathUpdater()
    
    # 执行更新
    result = updater.run_full_update(
        create_backup=not args.no_backup,
        dry_run=not args.execute
    )
    
    # 输出结果
    if result['success']:
        print("\n✅ 路径更新流程执行成功！")
        
        if result['preview']:
            preview = result['preview']
            print(f"\n📊 统计信息:")
            print(f"  - 扫描记录数: {preview['total_records']}")
            print(f"  - 需要更新: {preview['total_changes']}")
            
            if preview['total_changes'] > 0:
                print(f"\n📝 按表统计:")
                for table_name, changes in preview['changes_by_table'].items():
                    if changes:
                        print(f"  - {table_name}: {len(changes)} 条记录")
        
        if result['backup_file']:
            print(f"\n💾 备份文件: {result['backup_file']}")
        
        if not args.execute:
            print(f"\n⚠️  这是预演模式，使用 --execute 参数执行实际更新")
    else:
        print(f"\n❌ 路径更新失败: {result.get('error', '未知错误')}")
        sys.exit(1)

if __name__ == '__main__':
    main() 