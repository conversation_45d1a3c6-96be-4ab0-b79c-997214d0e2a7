#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
库存管理控制器
处理库存管理相关的业务逻辑和界面交互
包含入库管理、出库管理、需求计划、数据分析等功能
"""

import os
import sys
from datetime import datetime, date, timedelta
from typing import List, Dict, Optional, Tuple, Any

from PyQt6.QtCore import QObject, pyqtSignal, QThread, QTimer
from PyQt6.QtWidgets import QMessageBox, QFileDialog

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from services.inventory_service import InventoryService
from services.excel_service import ExcelService
from services.log_service import LogService


class InventoryController(QObject):
    """库存管理控制器"""
    
    # 定义信号
    inventory_updated = pyqtSignal(list)  # 库存更新信号
    inbound_order_created = pyqtSignal(dict)  # 入库单创建信号
    outbound_order_created = pyqtSignal(dict)  # 出库单创建信号
    data_imported = pyqtSignal(str)  # 数据导入信号
    data_exported = pyqtSignal(str)  # 数据导出信号
    error_occurred = pyqtSignal(str)  # 错误信号
    progress_updated = pyqtSignal(int)  # 进度更新信号
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.inventory_service = InventoryService()
        self.excel_service = ExcelService()
        self.logger = LogService()
        
    # ==================== 库存查询 ====================
    
    def get_inventory_list(self, filters: Dict = None) -> List[Dict]:
        """获取库存列表"""
        try:
            return self.inventory_service.get_inventory_list(filters)
        except Exception as e:
            error_msg = f"获取库存列表失败: {str(e)}"
            self.logger.error(error_msg)
            self.error_occurred.emit(error_msg)
            return []
    
    def search_inventory(self, search_params: Dict) -> List[Dict]:
        """搜索库存"""
        try:
            filters = {}
            
            # 处理搜索参数
            if search_params.get('product_code'):
                filters['product_code'] = search_params['product_code']
            if search_params.get('product_name'):
                filters['product_name'] = search_params['product_name']
            if search_params.get('category_id'):
                filters['category_id'] = search_params['category_id']
            if search_params.get('location_code'):
                filters['location_code'] = search_params['location_code']
            if search_params.get('status'):
                filters['status'] = search_params['status']
                
            inventory_list = self.inventory_service.get_inventory_list(filters)
            self.inventory_updated.emit(inventory_list)
            return inventory_list
            
        except Exception as e:
            error_msg = f"搜索库存失败: {str(e)}"
            self.logger.error(error_msg)
            self.error_occurred.emit(error_msg)
            return []
    
    def get_low_stock_items(self) -> List[Dict]:
        """获取低库存商品"""
        try:
            return self.inventory_service.get_low_stock_items()
        except Exception as e:
            error_msg = f"获取低库存商品失败: {str(e)}"
            self.logger.error(error_msg)
            self.error_occurred.emit(error_msg)
            return []
    
    def get_out_of_stock_items(self) -> List[Dict]:
        """获取缺货商品"""
        try:
            return self.inventory_service.get_out_of_stock_items()
        except Exception as e:
            error_msg = f"获取缺货商品失败: {str(e)}"
            self.logger.error(error_msg)
            self.error_occurred.emit(error_msg)
            return []

    def get_inventory_by_warehouse(self, warehouse_id: int) -> List[Dict]:
        """根据仓库ID获取库存数据"""
        try:
            filters = {'warehouse_id': warehouse_id}
            return self.inventory_service.get_inventory_list(filters)
        except Exception as e:
            error_msg = f"根据仓库ID获取库存数据失败: {str(e)}"
            self.logger.error(error_msg)
            self.error_occurred.emit(error_msg)
            return []
    
    # ==================== 入库管理 ====================
    
    def create_inbound_order(self, order_data: Dict) -> bool:
        """创建入库单"""
        try:
            # 数据验证
            if not self._validate_inbound_order_data(order_data):
                return False
            
            success, message = self.inventory_service.create_inbound_order(order_data)
            if success:
                self.inbound_order_created.emit(order_data)
                self.logger.info(f"创建入库单成功: {message}")
            else:
                self.error_occurred.emit(message)
            
            return success
            
        except Exception as e:
            error_msg = f"创建入库单失败: {str(e)}"
            self.logger.error(error_msg)
            self.error_occurred.emit(error_msg)
            return False
    
    def get_inbound_orders(self, filters: Dict = None) -> List[Dict]:
        """获取入库单列表"""
        try:
            return self.inventory_service.get_inbound_orders(filters)
        except Exception as e:
            error_msg = f"获取入库单列表失败: {str(e)}"
            self.logger.error(error_msg)
            self.error_occurred.emit(error_msg)
            return []
    
    def approve_inbound_order(self, order_id: int, approver_id: int) -> bool:
        """审批入库单"""
        try:
            success, message = self.inventory_service.approve_inbound_order(order_id, approver_id)
            if not success:
                self.error_occurred.emit(message)
            return success
        except Exception as e:
            error_msg = f"审批入库单失败: {str(e)}"
            self.logger.error(error_msg)
            self.error_occurred.emit(error_msg)
            return False
    
    def delete_inbound_order(self, order_id: int) -> bool:
        """删除入库单"""
        try:
            success, message = self.inventory_service.delete_inbound_order(order_id)
            if not success:
                self.error_occurred.emit(message)
            else:
                self.logger.info(f"删除入库单成功: {message}")
            return success
        except Exception as e:
            error_msg = f"删除入库单失败: {str(e)}"
            self.logger.error(error_msg)
            self.error_occurred.emit(error_msg)
            return False
    
    # ==================== 出库管理 ====================
    
    def create_outbound_order(self, order_data: Dict) -> bool:
        """创建出库单"""
        try:
            # 数据验证
            if not self._validate_outbound_order_data(order_data):
                return False
            
            success, message = self.inventory_service.create_outbound_order(order_data)
            if success:
                self.outbound_order_created.emit(order_data)
                self.logger.info(f"创建出库单成功: {message}")
            else:
                self.error_occurred.emit(message)
            
            return success
            
        except Exception as e:
            error_msg = f"创建出库单失败: {str(e)}"
            self.logger.error(error_msg)
            self.error_occurred.emit(error_msg)
            return False
    
    def get_outbound_orders(self, filters: Dict = None) -> List[Dict]:
        """获取出库单列表"""
        try:
            return self.inventory_service.get_outbound_orders(filters)
        except Exception as e:
            error_msg = f"获取出库单列表失败: {str(e)}"
            self.logger.error(error_msg)
            self.error_occurred.emit(error_msg)
            return []
    
    def approve_outbound_order(self, order_id: int, approver_id: int) -> bool:
        """审批出库单"""
        try:
            success, message = self.inventory_service.approve_outbound_order(order_id, approver_id)
            if not success:
                self.error_occurred.emit(message)
            return success
        except Exception as e:
            error_msg = f"审批出库单失败: {str(e)}"
            self.logger.error(error_msg)
            self.error_occurred.emit(error_msg)
            return False
    
    def delete_outbound_order(self, order_id: int) -> bool:
        """删除出库单"""
        try:
            success, message = self.inventory_service.delete_outbound_order(order_id)
            if not success:
                self.error_occurred.emit(message)
            else:
                self.logger.info(f"删除出库单成功: {message}")
            return success
        except Exception as e:
            error_msg = f"删除出库单失败: {str(e)}"
            self.logger.error(error_msg)
            self.error_occurred.emit(error_msg)
            return False
    
    # ==================== 时间过滤 ====================
    
    def get_orders_by_time_range(self, order_type: str, time_range: str, 
                                start_date: date = None, end_date: date = None) -> List[Dict]:
        """根据时间范围获取订单"""
        try:
            # 计算时间范围
            if not start_date or not end_date:
                end_date = date.today()
                
                if time_range == 'day':
                    start_date = end_date
                elif time_range == 'week':
                    start_date = end_date - timedelta(days=7)
                elif time_range == 'month':
                    start_date = end_date - timedelta(days=30)
                elif time_range == 'quarter':
                    start_date = end_date - timedelta(days=90)
                elif time_range == 'year':
                    start_date = end_date - timedelta(days=365)
                else:
                    start_date = end_date - timedelta(days=30)  # 默认一个月
            
            filters = {
                'start_date': start_date,
                'end_date': end_date
            }
            
            if order_type == 'inbound':
                return self.inventory_service.get_inbound_orders(filters)
            elif order_type == 'outbound':
                return self.inventory_service.get_outbound_orders(filters)
            else:
                return []
                
        except Exception as e:
            error_msg = f"获取时间范围内订单失败: {str(e)}"
            self.logger.error(error_msg)
            self.error_occurred.emit(error_msg)
            return []
    
    # ==================== 数据验证 ====================
    
    def _validate_inbound_order_data(self, order_data: Dict) -> bool:
        """验证入库单数据"""
        required_fields = ['type', 'warehouse_id', 'operator_id']
        
        for field in required_fields:
            if not order_data.get(field):
                self.error_occurred.emit(f"缺少必要字段: {field}")
                return False
        
        # 验证入库类型
        valid_types = ['purchase', 'return', 'transfer']
        if order_data['type'] not in valid_types:
            self.error_occurred.emit(f"无效的入库类型: {order_data['type']}")
            return False
        
        return True
    
    def _validate_outbound_order_data(self, order_data: Dict) -> bool:
        """验证出库单数据"""
        required_fields = ['type', 'warehouse_id', 'operator_id']
        
        for field in required_fields:
            if not order_data.get(field):
                self.error_occurred.emit(f"缺少必要字段: {field}")
                return False
        
        # 验证出库类型
        valid_types = ['borrow', 'issue', 'transfer']
        if order_data['type'] not in valid_types:
            self.error_occurred.emit(f"无效的出库类型: {order_data['type']}")
            return False
        
        return True

    # ==================== Excel导入导出 ====================

    def import_inbound_orders_from_excel(self, file_path: str) -> bool:
        """从Excel导入入库单"""
        try:
            if not self.excel_service.is_available():
                self.error_occurred.emit("Excel处理功能不可用，请安装pandas和openpyxl")
                return False

            # 验证文件
            success, message = self.excel_service.validate_excel_file(file_path)
            if not success:
                self.error_occurred.emit(message)
                return False

            # 读取Excel数据
            success, data, message = self.excel_service.read_inbound_orders_excel(file_path)
            if not success:
                self.error_occurred.emit(message)
                return False

            # 批量创建入库单
            success_count = 0
            total_count = len(data)

            for i, order_data in enumerate(data):
                try:
                    success, msg = self.inventory_service.create_inbound_order(order_data)
                    if success:
                        success_count += 1
                    else:
                        self.logger.warning(f"导入第{i+1}行入库单失败: {msg}")

                    # 更新进度
                    progress = int((i + 1) / total_count * 100)
                    self.progress_updated.emit(progress)

                except Exception as e:
                    self.logger.error(f"导入第{i+1}行入库单异常: {str(e)}")

            result_msg = f"导入完成，成功导入 {success_count}/{total_count} 条入库单"
            self.data_imported.emit(result_msg)
            self.logger.info(result_msg)

            return success_count > 0

        except Exception as e:
            error_msg = f"导入入库单失败: {str(e)}"
            self.logger.error(error_msg)
            self.error_occurred.emit(error_msg)
            return False

    def import_outbound_orders_from_excel(self, file_path: str) -> bool:
        """从Excel导入出库单"""
        try:
            if not self.excel_service.is_available():
                self.error_occurred.emit("Excel处理功能不可用，请安装pandas和openpyxl")
                return False

            # 验证文件
            success, message = self.excel_service.validate_excel_file(file_path)
            if not success:
                self.error_occurred.emit(message)
                return False

            # 读取Excel数据
            success, data, message = self.excel_service.read_outbound_orders_excel(file_path)
            if not success:
                self.error_occurred.emit(message)
                return False

            # 批量创建出库单
            success_count = 0
            total_count = len(data)

            for i, order_data in enumerate(data):
                try:
                    success, msg = self.inventory_service.create_outbound_order(order_data)
                    if success:
                        success_count += 1
                    else:
                        self.logger.warning(f"导入第{i+1}行出库单失败: {msg}")

                    # 更新进度
                    progress = int((i + 1) / total_count * 100)
                    self.progress_updated.emit(progress)

                except Exception as e:
                    self.logger.error(f"导入第{i+1}行出库单异常: {str(e)}")

            result_msg = f"导入完成，成功导入 {success_count}/{total_count} 条出库单"
            self.data_imported.emit(result_msg)
            self.logger.info(result_msg)

            return success_count > 0

        except Exception as e:
            error_msg = f"导入出库单失败: {str(e)}"
            self.logger.error(error_msg)
            self.error_occurred.emit(error_msg)
            return False

    def export_inbound_orders_to_excel(self, file_path: str, filters: Dict = None) -> bool:
        """导出入库单到Excel"""
        try:
            if not self.excel_service.is_available():
                self.error_occurred.emit("Excel处理功能不可用，请安装pandas和openpyxl")
                return False

            # 获取入库单数据
            inbound_orders = self.inventory_service.get_inbound_orders(filters)
            if not inbound_orders:
                self.error_occurred.emit("没有可导出的入库单数据")
                return False

            # 导出到Excel
            success, message = self.excel_service.export_inbound_report(inbound_orders, file_path)
            if success:
                self.data_exported.emit(f"入库单导出成功: {file_path}")
                self.logger.info(f"入库单导出成功: {file_path}")
            else:
                self.error_occurred.emit(message)

            return success

        except Exception as e:
            error_msg = f"导出入库单失败: {str(e)}"
            self.logger.error(error_msg)
            self.error_occurred.emit(error_msg)
            return False

    def export_outbound_orders_to_excel(self, file_path: str, filters: Dict = None) -> bool:
        """导出出库单到Excel"""
        try:
            if not self.excel_service.is_available():
                self.error_occurred.emit("Excel处理功能不可用，请安装pandas和openpyxl")
                return False

            # 获取出库单数据
            outbound_orders = self.inventory_service.get_outbound_orders(filters)
            if not outbound_orders:
                self.error_occurred.emit("没有可导出的出库单数据")
                return False

            # 导出到Excel
            success, message = self.excel_service.export_outbound_report(outbound_orders, file_path)
            if success:
                self.data_exported.emit(f"出库单导出成功: {file_path}")
                self.logger.info(f"出库单导出成功: {file_path}")
            else:
                self.error_occurred.emit(message)

            return success

        except Exception as e:
            error_msg = f"导出出库单失败: {str(e)}"
            self.logger.error(error_msg)
            self.error_occurred.emit(error_msg)
            return False

    def export_inventory_to_excel(self, file_path: str, filters: Dict = None) -> bool:
        """导出库存到Excel"""
        try:
            if not self.excel_service.is_available():
                self.error_occurred.emit("Excel处理功能不可用，请安装pandas和openpyxl")
                return False

            # 获取库存数据
            inventory_data = self.inventory_service.get_inventory_list(filters)
            if not inventory_data:
                self.error_occurred.emit("没有可导出的库存数据")
                return False

            # 导出到Excel
            success, message = self.excel_service.export_inventory_report(inventory_data, file_path)
            if success:
                self.data_exported.emit(f"库存数据导出成功: {file_path}")
                self.logger.info(f"库存数据导出成功: {file_path}")
            else:
                self.error_occurred.emit(message)

            return success

        except Exception as e:
            error_msg = f"导出库存数据失败: {str(e)}"
            self.logger.error(error_msg)
            self.error_occurred.emit(error_msg)
            return False

    # ==================== 数据分析 ====================

    def get_inventory_analysis(self, time_range: str = 'month') -> Dict:
        """获取库存分析数据"""
        try:
            return self.inventory_service.get_inventory_analysis(time_range)
        except Exception as e:
            error_msg = f"获取库存分析数据失败: {str(e)}"
            self.logger.error(error_msg)
            self.error_occurred.emit(error_msg)
            return {}

    def get_inbound_outbound_statistics(self, time_range: str = 'month') -> Dict:
        """获取出入库统计数据"""
        try:
            return self.inventory_service.get_inbound_outbound_statistics(time_range)
        except Exception as e:
            error_msg = f"获取出入库统计数据失败: {str(e)}"
            self.logger.error(error_msg)
            self.error_occurred.emit(error_msg)
            return {}

    def get_stock_warning_analysis(self) -> Dict:
        """获取库存预警分析"""
        try:
            return self.inventory_service.get_stock_warning_analysis()
        except Exception as e:
            error_msg = f"获取库存预警分析失败: {str(e)}"
            self.logger.error(error_msg)
            self.error_occurred.emit(error_msg)
            return {}
