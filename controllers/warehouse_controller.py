#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
仓库管理控制器
"""

from typing import Dict, Any, List, Optional
from PyQt6.QtCore import QObject, pyqtSignal

from services.warehouse_service import WarehouseService
from services.log_service import LogService

class WarehouseController(QObject):
    """仓库管理控制器"""
    
    # 信号定义
    factory_created = pyqtSignal(dict)
    factory_updated = pyqtSignal(dict)
    factory_deleted = pyqtSignal(int)
    
    warehouse_created = pyqtSignal(dict)
    warehouse_updated = pyqtSignal(dict)
    warehouse_deleted = pyqtSignal(int)
    
    shelf_created = pyqtSignal(dict)
    shelf_updated = pyqtSignal(dict)
    shelf_deleted = pyqtSignal(int)
    
    error_occurred = pyqtSignal(str)
    
    def __init__(self):
        super().__init__()
        self.service = WarehouseService()
        self.logger = LogService.get_logger(__name__)
    
    # ==================== 厂区管理 ====================
    
    def create_factory(self, factory_data: Dict[str, Any]) -> bool:
        """创建厂区"""
        try:
            # 数据验证
            if not self._validate_factory_data(factory_data):
                return False
            
            factory = self.service.create_factory(factory_data)
            if factory:
                self.factory_created.emit(factory.to_dict())
                return True
            return False
            
        except Exception as e:
            self.logger.error(f"创建厂区失败: {e}")
            self.error_occurred.emit(str(e))
            return False
    
    def update_factory(self, factory_id: int, factory_data: Dict[str, Any]) -> bool:
        """更新厂区"""
        try:
            # 数据验证
            if not self._validate_factory_data(factory_data, is_update=True):
                return False
            
            factory = self.service.update_factory(factory_id, factory_data)
            if factory:
                self.factory_updated.emit(factory.to_dict())
                return True
            return False
            
        except Exception as e:
            self.logger.error(f"更新厂区失败: {e}")
            self.error_occurred.emit(str(e))
            return False
    
    def delete_factory(self, factory_id: int) -> bool:
        """删除厂区"""
        try:
            if self.service.delete_factory(factory_id):
                self.factory_deleted.emit(factory_id)
                return True
            return False
            
        except Exception as e:
            self.logger.error(f"删除厂区失败: {e}")
            self.error_occurred.emit(str(e))
            return False
    
    def get_factories(self, active_only: bool = True) -> List[Dict[str, Any]]:
        """获取厂区列表"""
        try:
            return self.service.get_factories(active_only)
        except Exception as e:
            self.logger.error(f"获取厂区列表失败: {e}")
            self.error_occurred.emit(str(e))
            return []
    
    def get_factory_by_id(self, factory_id: int) -> Optional[Dict[str, Any]]:
        """根据ID获取厂区"""
        try:
            return self.service.get_factory_by_id(factory_id)
        except Exception as e:
            self.logger.error(f"获取厂区失败: {e}")
            self.error_occurred.emit(str(e))
            return None
    
    def _validate_factory_data(self, data: Dict[str, Any], is_update: bool = False) -> bool:
        """验证厂区数据"""
        required_fields = ['code', 'name'] if not is_update else []
        
        for field in required_fields:
            if field not in data or not data[field]:
                self.error_occurred.emit(f"请填写{field}")
                return False
        
        # 验证编码格式
        if 'code' in data:
            code = data['code'].strip()
            if len(code) < 2:
                self.error_occurred.emit("厂区编码长度至少为2个字符")
                return False
            data['code'] = code
        
        # 验证名称
        if 'name' in data:
            name = data['name'].strip()
            if len(name) < 2:
                self.error_occurred.emit("厂区名称长度至少为2个字符")
                return False
            data['name'] = name
        
        return True
    
    # ==================== 仓库管理 ====================
    
    def create_warehouse(self, warehouse_data: Dict[str, Any]) -> bool:
        """创建仓库"""
        try:
            # 数据验证
            if not self._validate_warehouse_data(warehouse_data):
                return False
            
            warehouse = self.service.create_warehouse(warehouse_data)
            if warehouse:
                self.warehouse_created.emit(warehouse)
                return True
            return False
            
        except Exception as e:
            self.logger.error(f"创建仓库失败: {e}")
            self.error_occurred.emit(str(e))
            return False
    
    def update_warehouse(self, warehouse_id: int, warehouse_data: Dict[str, Any]) -> bool:
        """更新仓库"""
        try:
            # 数据验证
            if not self._validate_warehouse_data(warehouse_data, is_update=True):
                return False
            
            warehouse = self.service.update_warehouse(warehouse_id, warehouse_data)
            if warehouse:
                self.warehouse_updated.emit(warehouse)
                return True
            return False
            
        except Exception as e:
            self.logger.error(f"更新仓库失败: {e}")
            self.error_occurred.emit(str(e))
            return False
    
    def delete_warehouse(self, warehouse_id: int) -> bool:
        """删除仓库"""
        try:
            if self.service.delete_warehouse(warehouse_id):
                self.warehouse_deleted.emit(warehouse_id)
                return True
            return False
            
        except Exception as e:
            self.logger.error(f"删除仓库失败: {e}")
            self.error_occurred.emit(str(e))
            return False
    
    def get_warehouses(self, factory_id: Optional[int] = None, active_only: bool = True) -> List[Dict[str, Any]]:
        """获取仓库列表"""
        try:
            return self.service.get_warehouses(factory_id, active_only)
        except Exception as e:
            self.logger.error(f"获取仓库列表失败: {e}")
            self.error_occurred.emit(str(e))
            return []
    
    def get_warehouse_by_id(self, warehouse_id: int) -> Optional[Dict[str, Any]]:
        """根据ID获取仓库"""
        try:
            return self.service.get_warehouse_by_id(warehouse_id)
        except Exception as e:
            self.logger.error(f"获取仓库失败: {e}")
            self.error_occurred.emit(str(e))
            return None
    
    def _validate_warehouse_data(self, data: Dict[str, Any], is_update: bool = False) -> bool:
        """验证仓库数据"""
        required_fields = ['factory_id', 'code', 'name', 'type'] if not is_update else []
        
        for field in required_fields:
            if field not in data or not data[field]:
                field_names = {
                    'factory_id': '所属厂区',
                    'code': '仓库编码',
                    'name': '仓库名称',
                    'type': '仓库类型'
                }
                self.error_occurred.emit(f"请填写{field_names.get(field, field)}")
                return False
        
        # 验证编码格式
        if 'code' in data:
            code = data['code'].strip()
            if len(code) < 2:
                self.error_occurred.emit("仓库编码长度至少为2个字符")
                return False
            data['code'] = code
        
        # 验证名称
        if 'name' in data:
            name = data['name'].strip()
            if len(name) < 2:
                self.error_occurred.emit("仓库名称长度至少为2个字符")
                return False
            data['name'] = name
        
        # 验证仓库类型
        if 'type' in data:
            valid_types = ['general', 'material', 'parts', 'finished']
            if data['type'] not in valid_types:
                self.error_occurred.emit("仓库类型无效")
                return False
        
        return True
    
    # ==================== 货架管理 ====================
    
    def create_shelf(self, shelf_data: Dict[str, Any]) -> bool:
        """创建货架"""
        try:
            # 数据验证
            if not self._validate_shelf_data(shelf_data):
                return False
            
            shelf = self.service.create_shelf(shelf_data)
            if shelf:
                self.shelf_created.emit(shelf)
                return True
            return False
            
        except Exception as e:
            self.logger.error(f"创建货架失败: {e}")
            self.error_occurred.emit(str(e))
            return False
    
    def update_shelf(self, shelf_id: int, shelf_data: Dict[str, Any]) -> bool:
        """更新货架"""
        try:
            # 数据验证
            if not self._validate_shelf_data(shelf_data, is_update=True):
                return False
            
            shelf = self.service.update_shelf(shelf_id, shelf_data)
            if shelf:
                self.shelf_updated.emit(shelf)
                return True
            return False
            
        except Exception as e:
            self.logger.error(f"更新货架失败: {e}")
            self.error_occurred.emit(str(e))
            return False
    
    def delete_shelf(self, shelf_id: int) -> bool:
        """删除货架"""
        try:
            if self.service.delete_shelf(shelf_id):
                self.shelf_deleted.emit(shelf_id)
                return True
            return False
            
        except Exception as e:
            self.logger.error(f"删除货架失败: {e}")
            self.error_occurred.emit(str(e))
            return False
    
    def get_shelves(self, warehouse_id: Optional[int] = None, active_only: bool = True) -> List[Dict[str, Any]]:
        """获取货架列表"""
        try:
            return self.service.get_shelves(warehouse_id, active_only)
        except Exception as e:
            self.logger.error(f"获取货架列表失败: {e}")
            self.error_occurred.emit(str(e))
            return []
    
    def get_shelf_by_id(self, shelf_id: int) -> Optional[Dict[str, Any]]:
        """根据ID获取货架"""
        try:
            return self.service.get_shelf_by_id(shelf_id)
        except Exception as e:
            self.logger.error(f"获取货架失败: {e}")
            self.error_occurred.emit(str(e))
            return None
    
    def _validate_shelf_data(self, data: Dict[str, Any], is_update: bool = False) -> bool:
        """验证货架数据"""
        required_fields = ['warehouse_id', 'shelf_code'] if not is_update else []
        
        for field in required_fields:
            if field not in data or not data[field]:
                field_names = {
                    'warehouse_id': '所属仓库',
                    'shelf_code': '货架编码'
                }
                self.error_occurred.emit(f"请填写{field_names.get(field, field)}")
                return False
        
        # 验证编码格式
        if 'shelf_code' in data:
            code = data['shelf_code'].strip()
            if len(code) < 2:
                self.error_occurred.emit("货架编码长度至少为2个字符")
                return False
            data['shelf_code'] = code
        
        # 验证层数和位置数
        if 'max_layers' in data:
            if not isinstance(data['max_layers'], int) or data['max_layers'] < 1 or data['max_layers'] > 10:
                self.error_occurred.emit("货架层数必须在1-10之间")
                return False
        
        if 'positions_per_layer' in data:
            if not isinstance(data['positions_per_layer'], int) or data['positions_per_layer'] < 1 or data['positions_per_layer'] > 50:
                self.error_occurred.emit("每层位置数必须在1-50之间")
                return False
        
        return True
    
    # ==================== 库位和布局管理 ====================
    
    def get_locations(self, shelf_id: Optional[int] = None, warehouse_id: Optional[int] = None) -> List[Dict[str, Any]]:
        """获取库位列表"""
        try:
            return self.service.get_locations(shelf_id, warehouse_id)
        except Exception as e:
            self.logger.error(f"获取库位列表失败: {e}")
            self.error_occurred.emit(str(e))
            return []

    def get_locations_by_warehouse(self, warehouse_id: int) -> List[Dict[str, Any]]:
        """根据仓库ID获取库位列表"""
        try:
            return self.service.get_locations(warehouse_id=warehouse_id)
        except Exception as e:
            self.logger.error(f"根据仓库ID获取库位列表失败: {e}")
            self.error_occurred.emit(str(e))
            return []
    
    def get_warehouse_layout(self, warehouse_id: int) -> Optional[Dict[str, Any]]:
        """获取仓库布局信息"""
        try:
            return self.service.get_warehouse_layout(warehouse_id)
        except Exception as e:
            self.logger.error(f"获取仓库布局失败: {e}")
            self.error_occurred.emit(str(e))
            return None
    
    def get_warehouse_statistics(self) -> Optional[Dict[str, Any]]:
        """获取仓库统计信息"""
        try:
            return self.service.get_warehouse_statistics()
        except Exception as e:
            self.logger.error(f"获取仓库统计信息失败: {e}")
            self.error_occurred.emit(str(e))
            return None 