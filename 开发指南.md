# 武汉总厂仓库管理系统 - 开发指南

## 1. 项目概述

### 1.1 项目信息
- **项目名称**: 武汉总厂仓库管理系统
- **技术栈**: PyQt6 + MySQL + Qt-Material + QtCharts
- **开发模式**: 桌面应用
- **部署模式**: 服务器+多客户端并发模式
- **当前版本**: v1.0.0-alpha
- **开发状态**: 第二阶段进行中 (约45%完成)

### 1.2 系统架构
```
客户端层 (PyQt6)
    ├── 用户界面 (UI) ✅
    ├── 业务逻辑 (Business Logic) 🔄
    └── 数据访问层 (Data Access) 🔄
            ↓
网络通信层 (TCP/HTTP) ⏳
            ↓
服务器端
    ├── 数据库服务 (MySQL) ✅
    ├── 业务服务 🔄
    └── 文件服务 ⏳
```

## 2. 开发环境配置

### 2.1 Python环境 ✅ 已配置
```bash
# 创建虚拟环境
python -m venv venv

# 激活虚拟环境 (Windows)
venv\Scripts\activate

# 安装依赖包
pip install -r requirements.txt -i https://pypi.tuna.tsinghua.edu.cn/simple/
```

### 2.2 必需依赖包 ✅ 已安装
```txt
# PyQt6 相关
PyQt6>=6.4.0
PyQt6-tools>=6.4.0
PyQt6-Charts>=6.4.0
qt-material>=2.14

# 数据库相关
PyMySQL>=1.1.0
mysqlclient>=2.2.0
SQLAlchemy>=2.0.0
alembic>=1.12.0

# 数据处理
pandas>=2.0.0
numpy>=1.24.0
openpyxl>=3.1.0

# 图像处理
Pillow>=10.0.0

# 网络请求
requests>=2.31.0

# 加密安全
cryptography>=41.0.0
bcrypt>=4.0.0

# 日志和配置
python-dotenv>=1.0.0
loguru>=0.7.0

# 图表绘制
matplotlib>=3.7.0
plotly>=5.15.0

# 工具库
python-dateutil>=2.8.0
validators>=0.20.0

# 开发工具
pytest>=7.4.0
pytest-qt>=4.2.0
black>=23.7.0
flake8>=6.0.0

# 打包部署
pyinstaller>=5.13.0
cx-Freeze>=6.15.0
```

### 2.3 数据库配置 ✅ 已完成
```sql
-- MySQL 8.0 配置
-- 数据库名: wms_db
-- 用户名: wms_user
-- 密码: admin123
-- 端口: 5432

CREATE DATABASE wms_db;
CREATE USER wms_user WITH PASSWORD 'admin123';
GRANT ALL PRIVILEGES ON DATABASE wms_db TO wms_user;
```

### 2.4 开发工具
- **IDE**: PyCharm Professional / VS Code
- **UI设计**: Qt Designer
- **数据库管理**: pgAdmin 4
- **版本控制**: Git

## 3. 项目结构 (实际结构)

```
wms_system/
├── app.py                  # 主程序入口 ✅
├── requirements.txt        # 依赖包列表 ✅
├── database_init_utf8.sql  # 数据库初始化脚本 ✅
├── database_status.py      # 数据库状态检查 ✅
├── create_tables.py        # 数据表创建脚本 ✅
├── config/                 # 配置文件 ✅
│   ├── __init__.py
│   └── database.py         # 数据库配置
├── models/                 # 数据模型 🔄
│   ├── __init__.py
│   └── user.py             # 用户模型 ✅
├── views/                  # 界面视图 ✅
│   ├── __init__.py
│   ├── main_window.py      # 主窗口 ✅ (1519行)
│   ├── login_window.py     # 登录窗口 ✅ (474行)
│   ├── register_window.py  # 注册窗口 ✅ (403行)
│   ├── forgot_password_window.py # 忘记密码窗口 ✅ (372行)
│   ├── dashboard/          # 仪表盘 ⏳
│   ├── user_management/    # 用户管理 ⏳
│   ├── warehouse_management/ # 仓库管理 ⏳
│   ├── product_management/ # 商品管理 ⏳
│   └── inventory_management/ # 库存管理 ⏳
├── controllers/            # 控制器 ⏳
│   └── __init__.py
├── services/               # 业务服务 🔄
│   ├── __init__.py
│   ├── user_service.py     # 用户服务 ✅ (308行)
│   ├── backup_service.py   # 备份服务 ✅ (317行)
│   ├── log_service.py      # 日志服务 ✅ (136行)
│   └── notification_service.py # 通知服务 ✅ (155行)
├── utils/                  # 工具类 🔄
│   ├── __init__.py
│   └── email_utils.py      # 邮件工具 ✅ (166行)
├── resources/              # 资源文件 ✅
│   ├── ui/                 # UI文件
│   ├── images/             # 图片资源
│   │   └── login_bg (2).png # 登录背景图
│   ├── icons/              # 图标资源
│   └── styles/             # 样式文件
├── tests/                  # 测试文件 ⏳
│   └── __init__.py
├── docs/                   # 文档 ✅
│   ├── 需求文档.md
│   ├── 开发指南.md
│   └── 进度指南.md
├── logs/                   # 日志目录 ✅
├── backups/                # 备份目录 ✅
└── venv/                   # 虚拟环境 ✅
```

## 4. 编码规范

### 4.1 Python编码规范 (PEP8) ✅ 已实施
```python
# 类名使用驼峰命名
class UserController:
    pass

# 函数和变量使用下划线命名
def get_user_info():
    user_name = "admin"
    return user_name

# 常量使用大写字母
DATABASE_URL = "mysql+pymysql://user:pass@localhost/db"

# 导入顺序：标准库 -> 第三方库 -> 本地模块
import os
import sys

from PyQt6.QtWidgets import QApplication
from sqlalchemy import create_engine

from models.user import User
```

### 4.2 UI设计规范 ✅ 已实施
```python
# Bootstrap风格设计要求
class BaseWidget(QWidget):
    def __init__(self):
        super().__init__()
        self.setup_ui()
        self.apply_theme()
    
    def apply_theme(self):
        # 浅蓝色渐变背景
        self.setStyleSheet("""
            QWidget {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #E3F2FD, stop:1 #BBDEFB);
                border-radius: 8px;
            }
            QLineEdit {
                border: none;
                background: white;
                border-radius: 4px;
                padding: 8px;
            }
        """)
```

### 4.3 数据库操作规范 ✅ 已实施
```python
# 使用SQLAlchemy ORM
from sqlalchemy.orm import sessionmaker

class BaseService:
    def __init__(self):
        self.session = sessionmaker(bind=engine)()
    
    def __enter__(self):
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        if exc_type:
            self.session.rollback()
        else:
            self.session.commit()
        self.session.close()

# 使用示例
with UserService() as service:
    user = service.create_user(username="admin", password="123456")
```

## 5. 错误处理规范 ✅ 已实施

### 5.1 异常处理
```python
class WMSException(Exception):
    """仓库管理系统基础异常"""
    pass

class AuthenticationError(WMSException):
    """认证错误"""
    pass

class ValidationError(WMSException):
    """验证错误"""
    pass

# 错误处理示例
def login_user(username: str, password: str):
    try:
        if not username or not password:
            raise ValidationError("用户名和密码不能为空")
        
        user = authenticate(username, password)
        if not user:
            raise AuthenticationError("用户名或密码错误")
        
        return user
    except WMSException as e:
        logger.error(f"登录失败: {e}")
        raise
    except Exception as e:
        logger.error(f"系统错误: {e}")
        raise WMSException("系统内部错误")
```

### 5.2 日志记录 ✅ 已实施
```python
import logging
from services.log_service import LogService

logger = LogService.get_logger(__name__)

def process_inventory(data):
    try:
        logger.info(f"开始处理库存数据: {len(data)} 条记录")
        # 处理逻辑
        logger.info("库存数据处理完成")
    except Exception as e:
        logger.error(f"库存数据处理失败: {e}", exc_info=True)
        raise
```

## 6. 安全规范 ✅ 已实施

### 6.1 密码加密
```python
from cryptography.fernet import Fernet
import hashlib
import bcrypt

def hash_password(password: str) -> str:
    """密码哈希 - 使用bcrypt"""
    salt = bcrypt.gensalt()
    return bcrypt.hashpw(password.encode('utf-8'), salt).decode('utf-8')

def verify_password(password: str, hashed: str) -> bool:
    """验证密码"""
    return bcrypt.checkpw(password.encode('utf-8'), hashed.encode('utf-8'))

def encrypt_sensitive_data(data: str) -> str:
    """敏感数据加密"""
    key = Fernet.generate_key()
    f = Fernet(key)
    return f.encrypt(data.encode()).decode()
```

### 6.2 权限控制 ✅ 已实施
```python
from functools import wraps

def require_permission(permission):
    def decorator(func):
        @wraps(func)
        def wrapper(self, *args, **kwargs):
            if not self.current_user.has_permission(permission):
                raise PermissionError(f"需要权限: {permission}")
            return func(self, *args, **kwargs)
        return wrapper
    return decorator

# 使用示例
@require_permission('inventory.modify')
def update_inventory(self, item_id, quantity):
    # 更新库存逻辑
    pass
```

## 7. 测试规范 🔄 待完善

### 7.1 单元测试
```python
import unittest
from models.user import User

class TestUserModel(unittest.TestCase):
    def setUp(self):
        self.user = User(username="test", password="123456")
    
    def test_user_creation(self):
        self.assertEqual(self.user.username, "test")
        self.assertTrue(self.user.check_password("123456"))
    
    def test_user_permissions(self):
        self.user.role = "admin"
        self.assertTrue(self.user.has_permission("all"))

if __name__ == '__main__':
    unittest.main()
```

### 7.2 集成测试
```python
class TestInventoryIntegration(unittest.TestCase):
    def setUp(self):
        self.app = create_test_app()
        self.client = self.app.test_client()
    
    def test_inventory_workflow(self):
        # 测试完整的库存管理流程
        # 1. 创建商品
        # 2. 入库
        # 3. 出库
        # 4. 查询库存
        pass
```

## 8. 性能优化 ✅ 已实施

### 8.1 数据库优化
```sql
-- 已创建的索引 (33个)
CREATE INDEX idx_product_code ON products(product_code);
CREATE INDEX idx_inventory_date ON inventory_records(created_at);
CREATE INDEX idx_user_username ON users(username);
-- ... 更多索引
```

### 8.2 UI优化 ✅ 已实施
```python
# 大数据量时使用分页
class PaginatedTableWidget(QTableWidget):
    def __init__(self, page_size=100):
        super().__init__()
        self.page_size = page_size
        self.current_page = 0
        self.setup_pagination()
    
    def load_data(self, data):
        start = self.current_page * self.page_size
        end = start + self.page_size
        page_data = data[start:end]
        self.populate_table(page_data)
```

## 9. 已实现功能模块

### 9.1 用户认证系统 ✅ 完成
- **登录窗口**: 用户名/密码登录，记住密码，数据库连接状态
- **注册窗口**: 用户注册，角色选择，数据验证
- **忘记密码**: 邮箱验证，密码重置
- **权限控制**: 基于角色的访问控制 (admin/operator/viewer)

### 9.2 主界面系统 ✅ 完成
- **主窗口**: 标签页导航，顶部栏，用户信息显示
- **数据看板**: 实时数据卡片，图表展示，数据统计
- **响应式布局**: 适配不同屏幕尺寸
- **Material Design**: 现代化UI风格

### 9.3 系统管理模块 ✅ 基本完成
- **用户管理**: 用户CRUD操作，角色分配
- **系统配置**: 系统参数设置，配置管理
- **数据备份**: 全量备份，增量备份，恢复功能
- **日志管理**: 操作日志记录，日志查看和导出

### 9.4 业务服务层 🔄 部分完成
- **用户服务**: 用户认证，权限验证，用户管理
- **备份服务**: 数据备份和恢复
- **日志服务**: 日志记录和管理
- **通知服务**: 系统通知和消息推送

## 10. 待开发功能模块

### 10.1 数据模型层 ⏳ 优先级高
```python
# 需要实现的模型
models/
├── product.py          # 商品模型
├── warehouse.py        # 仓库模型
├── inventory.py        # 库存模型
├── supplier.py         # 供应商模型
├── category.py         # 分类模型
└── transaction.py      # 交易记录模型
```

### 10.2 控制器层 ⏳ 优先级高
```python
# 需要实现的控制器
controllers/
├── product_controller.py      # 商品控制器
├── warehouse_controller.py    # 仓库控制器
├── inventory_controller.py    # 库存控制器
├── supplier_controller.py     # 供应商控制器
└── report_controller.py       # 报表控制器
```

### 10.3 业务功能模块 ⏳ 优先级中
- **商品管理**: 商品CRUD，分类管理，供应商管理
- **仓库管理**: 仓库信息，货架布局，库位管理
- **库存管理**: 入库，出库，库存查询，预警
- **报表系统**: Excel导入导出，报表生成，打印

## 11. 部署指南

### 11.1 开发环境部署 ✅ 已完成
```bash
# 克隆项目
git clone <repository-url>
cd wms

# 激活虚拟环境
venv\Scripts\activate

# 运行应用
python app.py
```

### 11.2 生产环境部署 ⏳ 计划中
```bash
# 安装MySQL
sudo apt-get install mysql-server mysql-client

# 配置数据库
mysql -u root -p
CREATE DATABASE warehouse_management CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci;
CREATE USER 'wms_user'@'localhost' IDENTIFIED BY '179100215';
GRANT ALL PRIVILEGES ON warehouse_management.* TO 'wms_user'@'localhost';
FLUSH PRIVILEGES;

# 启动应用服务
python app.py --host 0.0.0.0 --port 8080
```

### 11.3 客户端部署 ⏳ 计划中
```python
# 自动更新检测
class UpdateChecker:
    def check_for_updates(self):
        try:
            response = requests.get(f"{SERVER_URL}/api/version")
            server_version = response.json()['version']
            if server_version > CURRENT_VERSION:
                return True
        except Exception as e:
            logger.error(f"检查更新失败: {e}")
        return False
```

## 12. 开发工作流 ✅ 已建立

### 12.1 Git工作流
```bash
# 功能开发
git checkout -b feature/product-management
git add .
git commit -m "feat: 添加商品管理功能"
git push origin feature/product-management

# 代码审查后合并
git checkout main
git merge feature/product-management
```

### 12.2 发布流程
1. 代码审查 ✅
2. 单元测试通过 🔄
3. 集成测试通过 ⏳
4. 性能测试 ⏳
5. 用户验收测试 ⏳
6. 生产环境部署 ⏳

## 13. 常见问题解决

### 13.1 数据库连接问题 ✅ 已解决
```python
# 连接池配置
from sqlalchemy import create_engine
from sqlalchemy.pool import QueuePool

engine = create_engine(
    DATABASE_URL,
    poolclass=QueuePool,
    pool_size=10,
    max_overflow=20,
    pool_recycle=3600
)
```

### 13.2 UI响应问题 ✅ 已解决
```python
# 使用线程处理耗时操作
from PyQt6.QtCore import QThread, pyqtSignal

class DataProcessThread(QThread):
    finished = pyqtSignal(object)
    
    def run(self):
        # 耗时操作
        result = process_large_data()
        self.finished.emit(result)
```

## 14. 代码质量指标

### 14.1 当前代码统计
- **总代码行数**: 约8,000行
- **主要模块**:
  - 界面层: 约3,000行 (主窗口1519行，登录474行，注册403行等)
  - 服务层: 约1,200行 (用户服务308行，备份服务317行等)
  - 工具层: 约200行
  - 配置层: 约100行

### 14.2 代码质量
- **编码规范**: ✅ 遵循PEP8
- **注释覆盖**: ✅ 详细中文注释
- **错误处理**: ✅ 统一异常处理机制
- **单元测试**: 🔄 覆盖率待提升
- **代码审查**: ✅ 定期进行

## 15. 下一步开发重点

### 15.1 本周任务 (6月2日-6月8日)
1. **商品管理模块开发** (优先级: 高)
   - 实现商品模型 (`models/product.py`)
   - 开发商品控制器 (`controllers/product_controller.py`)
   - 完善商品管理界面业务逻辑

2. **数据模型层完善** (优先级: 高)
   - 仓库模型 (`models/warehouse.py`)
   - 库存模型 (`models/inventory.py`)
   - 供应商模型 (`models/supplier.py`)

3. **控制器层开发** (优先级: 中)
   - 基础控制器框架
   - 业务逻辑封装
   - 数据验证机制

### 15.2 技术债务
- 单元测试覆盖率提升
- 代码重构和优化
- 文档更新和完善
- 性能测试和优化

## 16. 文档维护 ✅ 持续更新

- **API文档**: 使用Sphinx自动生成 ⏳
- **数据库文档**: 定期更新ER图和表结构 ✅
- **用户手册**: 每个版本发布时更新 ⏳
- **开发文档**: 代码变更时同步更新 ✅

---

**武汉总厂仓库管理系统** - 技术先进，架构清晰，开发规范！ 🚀 